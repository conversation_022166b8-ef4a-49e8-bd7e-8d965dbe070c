1. long and lat has to be updated whena user is updating custom data 
2. when user is creating second data map template filed will not be included 
3. best way to sort custom data fields
4. user cant edit default fields and cant add new fields with same name
5. api for user to send customdata 
6. api limit and dashbaord 
7. geofacning and nitification  , check if geofancing fields are working and also check if geo fancing validation are all harned 
8. GPS tracking similation and live refresh upddate on browser
9. add support key for gps data request
10. improve search to have also public data 
11. navigation, buttons and icon , links , cards, forms 
        we will use rounded navigation to switch to main page settings 
        
 12. single object tracking for oder  or someting or locatioin


 What's Still Missing in the Services Layer

  Even with this strong base, to meet the goal of being the "go-to" platform, your services layer is missing the implementation for the following concepts:

  1. Data & Content Management

   * What you have: A way to search existing, manually seeded data (Provinces, HealthFacs, etc.) via MapService.
   * What's missing: A dedicated `POIService` or `ContentManagementService`.
       * Why: Right now, adding new data types (e.g., schools, ATMs, restaurants) would require creating new models, seeders, and adding more logic to MapService. A dedicated service could manage a single, flexible
         PointOfInterest model, making it easier to add, update, and categorize thousands of POIs without changing the search service each time. This is crucial for scaling your content.

  2. User Interaction & Data Management

   * What you have: A powerful, API-driven way for users to add individual data points (createCustomData in DataMapService) and for devices to send GPS data (storeGpsData in PlaceMapService).
   * What's missing:
       * A `DataImportService`: There is no service that handles file uploads. A business user will want to upload a CSV or Excel file of their 100 store locations, not enter them one by one via an API. This service would
         parse the file, geocode the addresses (using NominatimService), and then use PlaceMapService to create the items in bulk.
       * A `CollaborationService`: PlaceMapService has a "share via key" feature for public viewing, but there's no service logic for true collaboration (e.g., inviteUserToMap(mapId, userId, 'editor'),
         listMapPermissions(mapId)).
       * Support for Lines and Polygons: Your services are entirely point-based (latitude, longitude). There is no service-level concept of storing a user-drawn line (like a route they drew manually) or a polygon (like a
         custom sales territory). This would require a new service to handle saving and querying GeoJSON line/polygon data associated with a PlaceMapItem.

  3. Analytics & Business Intelligence

   * What you have: Basic stats (getDataItemsStats in DataMapService) and distance calculations (getCellDistance in MapService).
   * What's missing:
       * An `AnalyticsService` or `SpatialAnalysisService`: This is the biggest gap. There is no service that can:
           * Generate heatmaps.
           * Perform choropleth mapping (coloring districts/sectors based on user data).
           * Find the nearest N points to a given location.
           * Perform "within" queries (e.g., "show me all my customers within the Gasabo district").
       * Advanced Routing in `OsrmService`: Your OsrmService is a direct wrapper for the OSRM API. It can find a simple A-to-B route. It is missing a higher-level method for multi-stop route optimization (the "Traveling
         Salesperson Problem"), which is the #1 feature for any business doing deliveries.

  The LocationSearchPatternService is currently empty and seems like a good candidate to be expanded into a more comprehensive SpatialAnalysisService.
        