<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Response;
use App\Exceptions\NotFound;
use App\Exceptions\ServerError;
use App\Exceptions\BadRequest;
use App\Exceptions\Forbidden;
use App\Exceptions\NotAuthorized;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->web(append: [
            \App\Http\Middleware\HandleInertiaRequests::class,
            \Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets::class,
        ]);

    })
    ->withExceptions(function (Exceptions $exceptions): void {

          $exceptions->render(function (NotFound $e) {
                return response()->json(['message' => $e->getMessage(), "timestamp" => now()->format("Y-m-d, H:i:s"),], Response::HTTP_NOT_FOUND);
            });

            $exceptions->renderable(function (ServerError $e) {
                return response()->json([
                    "message" => $e->getMessage() ?? "Something went wrong.",
                    "timestamp" => now()->format("Y-m-d, H:i:s")
                ], Response::HTTP_INTERNAL_SERVER_ERROR);
            });

            $exceptions->renderable(function (BadRequest $e) {
                return response()->json([
                    'message' => $e->getMessage(),
                    "timestamp" => now()->format("Y-m-d, H:i:s")
                ], Response::HTTP_BAD_REQUEST);
            });

            $exceptions->renderable(function (Forbidden $e) {
                return response()->json([
                    'message' => $e->getMessage(),
                    "timestamp" => now()->format("Y-m-d, H:i:s")
                ], Response::HTTP_FORBIDDEN);
            });

            $exceptions->renderable(function (NotAuthorized $e) {
                return response()->json([
                    'message' => $e->getMessage(),
                    "timestamp" => now()->format("Y-m-d, H:i:s")
                ], Response::HTTP_UNAUTHORIZED);
            });
       
    })->create();
