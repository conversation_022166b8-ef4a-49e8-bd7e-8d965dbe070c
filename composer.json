{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "cuonggt/laravel-dibi": "^0.5.0", "darkaonline/l5-swagger": "^9.0", "inertiajs/inertia-laravel": "^2.0", "laravel/framework": "^12.0", "laravel/jetstream": "^5.3", "laravel/nightwatch": "^1.11", "laravel/octane": "^2.12", "laravel/sanctum": "^4.0", "laravel/scout": "^10.15", "laravel/tinker": "^2.10.1", "phayes/geophp": "^1.2", "predis/predis": "^3.0", "spatie/fork": "^1.2", "spatie/laravel-data": "^4.17", "spatie/laravel-db-snapshots": "^2.7", "spinen/laravel-geometry": "^2.9", "spiral/roadrunner-cli": "^2.7", "spiral/roadrunner-http": "^3.5", "tarfin-labs/laravel-spatial": "^3.1", "tightenco/ziggy": "^2.0", "typesense/typesense-php": "^5.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "fakerphp/faker": "^1.23", "laravel/boost": "^1.0", "laravel/pail": "^1.2.2", "laravel/pint": "^1.13", "laravel/sail": "^1.43", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "pestphp/pest": "^3.8", "pestphp/pest-plugin-drift": "^3.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "test": ["@php artisan config:clear --ansi", "@php artisan test"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}