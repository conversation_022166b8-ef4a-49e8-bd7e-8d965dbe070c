(()=>{var e={155:e=>{var t,n,r=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function o(e){if(t===setTimeout)return setTimeout(e,0);if((t===i||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(n){try{return t.call(null,e,0)}catch(n){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:i}catch(e){t=i}try{n="function"==typeof clearTimeout?clearTimeout:s}catch(e){n=s}}();var a,l=[],u=!1,h=-1;function c(){u&&a&&(u=!1,a.length?l=a.concat(l):h=-1,l.length&&d())}function d(){if(!u){var e=o(c);u=!0;for(var t=l.length;t;){for(a=l,l=[];++h<t;)a&&a[h].run();h=-1,t=l.length}a=null,u=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===s||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{return n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function g(e,t){this.fun=e,this.array=t}function m(){}r.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];l.push(new g(e,t)),1!==l.length||u||o(d)},g.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=m,r.addListener=m,r.once=m,r.off=m,r.removeListener=m,r.removeAllListeners=m,r.emit=m,r.prependListener=m,r.prependOnceListener=m,r.listeners=function(e){return[]},r.binding=function(e){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(e){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,n),s.exports}n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),(()=>{"use strict";const e=new class{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(e){setTimeout((()=>{if(e.stack){if(a.isErrorNoTelemetry(e))throw new a(e.message+"\n\n"+e.stack);throw new Error(e.message+"\n\n"+e.stack)}throw e}),0)}}emit(e){this.listeners.forEach((t=>{t(e)}))}onUnexpectedError(e){this.unexpectedErrorHandler(e),this.emit(e)}onUnexpectedExternalError(e){this.unexpectedErrorHandler(e)}};function t(t){s(t)||e.onUnexpectedError(t)}function r(e){if(e instanceof Error){const{name:t,message:n}=e;return{$isError:!0,name:t,message:n,stack:e.stacktrace||e.stack,noTelemetry:a.isErrorNoTelemetry(e)}}return e}const i="Canceled";function s(e){return e instanceof o||e instanceof Error&&e.name===i&&e.message===i}class o extends Error{constructor(){super(i),this.name=this.message}}class a extends Error{constructor(e){super(e),this.name="CodeExpectedError"}static fromError(e){if(e instanceof a)return e;const t=new a;return t.message=e.message,t.stack=e.stack,t}static isErrorNoTelemetry(e){return"CodeExpectedError"===e.name}}class l extends Error{constructor(e){super(e||"An unexpected bug occurred."),Object.setPrototypeOf(this,l.prototype)}}function u(e){const t=this;let n,r=!1;return function(){return r||(r=!0,n=e.apply(t,arguments)),n}}var h;!function(e){function t(e){return e&&"object"==typeof e&&"function"==typeof e[Symbol.iterator]}e.is=t;const n=Object.freeze([]);function*r(e){yield e}e.empty=function(){return n},e.single=r,e.wrap=function(e){return t(e)?e:r(e)},e.from=function(e){return e||n},e.isEmpty=function(e){return!e||!0===e[Symbol.iterator]().next().done},e.first=function(e){return e[Symbol.iterator]().next().value},e.some=function(e,t){for(const n of e)if(t(n))return!0;return!1},e.find=function(e,t){for(const n of e)if(t(n))return n},e.filter=function*(e,t){for(const n of e)t(n)&&(yield n)},e.map=function*(e,t){let n=0;for(const r of e)yield t(r,n++)},e.concat=function*(...e){for(const t of e)for(const e of t)yield e},e.reduce=function(e,t,n){let r=n;for(const n of e)r=t(r,n);return r},e.slice=function*(e,t,n=e.length){for(t<0&&(t+=e.length),n<0?n+=e.length:n>e.length&&(n=e.length);t<n;t++)yield e[t]},e.consume=function(t,n=Number.POSITIVE_INFINITY){const r=[];if(0===n)return[r,t];const i=t[Symbol.iterator]();for(let t=0;t<n;t++){const t=i.next();if(t.done)return[r,e.empty()];r.push(t.value)}return[r,{[Symbol.iterator]:()=>i}]}}(h||(h={}));let c=null;function d(e){return null==c||c.trackDisposable(e),e}function g(e){null==c||c.markAsDisposed(e)}function m(e,t){null==c||c.setParent(e,t)}function f(e){if(h.is(e)){const t=[];for(const n of e)if(n)try{n.dispose()}catch(e){t.push(e)}if(1===t.length)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}if(e)return e.dispose(),e}function p(...e){const t=b((()=>f(e)));return function(e,t){if(c)for(const n of e)c.setParent(n,t)}(e,t),t}function b(e){const t=d({dispose:u((()=>{g(t),e()}))});return t}class _{constructor(){this._toDispose=new Set,this._isDisposed=!1,d(this)}dispose(){this._isDisposed||(g(this),this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(0!==this._toDispose.size)try{f(this._toDispose)}finally{this._toDispose.clear()}}add(e){if(!e)return e;if(e===this)throw new Error("Cannot register a disposable on itself!");return m(e,this),this._isDisposed?_.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(e),e}}_.DISABLE_DISPOSED_WARNING=!1;class C{constructor(){this._store=new _,d(this),m(this._store,this)}dispose(){g(this),this._store.dispose()}_register(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(e)}}C.None=Object.freeze({dispose(){}});class y{constructor(){this.dispose=()=>{},this.unset=()=>{},this.isset=()=>!1,d(this)}set(e){let t=e;return this.unset=()=>t=void 0,this.isset=()=>void 0!==t,this.dispose=()=>{t&&(t(),t=void 0,g(this))},this}}Symbol.iterator;class v{constructor(e){this.element=e,this.next=v.Undefined,this.prev=v.Undefined}}v.Undefined=new v(void 0);class w{constructor(){this._first=v.Undefined,this._last=v.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===v.Undefined}clear(){let e=this._first;for(;e!==v.Undefined;){const t=e.next;e.prev=v.Undefined,e.next=v.Undefined,e=t}this._first=v.Undefined,this._last=v.Undefined,this._size=0}unshift(e){return this._insert(e,!1)}push(e){return this._insert(e,!0)}_insert(e,t){const n=new v(e);if(this._first===v.Undefined)this._first=n,this._last=n;else if(t){const e=this._last;this._last=n,n.prev=e,e.next=n}else{const e=this._first;this._first=n,n.next=e,e.prev=n}this._size+=1;let r=!1;return()=>{r||(r=!0,this._remove(n))}}shift(){if(this._first!==v.Undefined){const e=this._first.element;return this._remove(this._first),e}}pop(){if(this._last!==v.Undefined){const e=this._last.element;return this._remove(this._last),e}}_remove(e){if(e.prev!==v.Undefined&&e.next!==v.Undefined){const t=e.prev;t.next=e.next,e.next.prev=t}else e.prev===v.Undefined&&e.next===v.Undefined?(this._first=v.Undefined,this._last=v.Undefined):e.next===v.Undefined?(this._last=this._last.prev,this._last.next=v.Undefined):e.prev===v.Undefined&&(this._first=this._first.next,this._first.prev=v.Undefined);this._size-=1}*[Symbol.iterator](){let e=this._first;for(;e!==v.Undefined;)yield e.element,e=e.next}}let L="undefined"!=typeof document&&document.location&&document.location.hash.indexOf("pseudo=true")>=0;function S(e,t){let n;return n=0===t.length?e:e.replace(/\{(\d+)\}/g,((e,n)=>{const r=n[0],i=t[r];let s=e;return"string"==typeof i?s=i:"number"!=typeof i&&"boolean"!=typeof i&&null!=i||(s=String(i)),s})),L&&(n="［"+n.replace(/[aouei]/g,"$&$&")+"］"),n}function N(e,t,...n){return S(t,n)}var E,A=n(155);const R="en";let x,M,k=!1,T=!1,O=!1,I=!1,D=!1,P=!1,F=!1,K=!1,V=!1,B=!1,q=null,U=null,W=null;const H="object"==typeof self?self:"object"==typeof n.g?n.g:{};let $;void 0!==H.vscode&&void 0!==H.vscode.process?$=H.vscode.process:void 0!==A&&($=A);const z="string"==typeof(null===(E=null==$?void 0:$.versions)||void 0===E?void 0:E.electron),j=z&&"renderer"===(null==$?void 0:$.type);if("object"!=typeof navigator||j)if("object"==typeof $){k="win32"===$.platform,T="darwin"===$.platform,O="linux"===$.platform,I=O&&!!$.env.SNAP&&!!$.env.SNAP_REVISION,F=z,V=!!$.env.CI||!!$.env.BUILD_ARTIFACTSTAGINGDIRECTORY,x=R,q=R;const e=$.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e),n=t.availableLanguages["*"];x=t.locale,U=t.osLocale,q=n||R,W=t._translationsConfigFile}catch(e){}D=!0}else console.error("Unable to resolve platform.");else{M=navigator.userAgent,k=M.indexOf("Windows")>=0,T=M.indexOf("Macintosh")>=0,K=(M.indexOf("Macintosh")>=0||M.indexOf("iPad")>=0||M.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,O=M.indexOf("Linux")>=0,B=(null==M?void 0:M.indexOf("Mobi"))>=0,P=!0;x=void N(0,"_")||R,q=x,U=navigator.language}let G=0;T?G=1:k?G=3:O&&(G=2);const Y=k,J=T,Q=(P&&H.importScripts,M),X="function"==typeof H.postMessage&&!H.importScripts;(()=>{if(X){const e=[];H.addEventListener("message",(t=>{if(t.data&&t.data.vscodeScheduleAsyncWork)for(let n=0,r=e.length;n<r;n++){const r=e[n];if(r.id===t.data.vscodeScheduleAsyncWork)return e.splice(n,1),void r.callback()}}));let t=0;return n=>{const r=++t;e.push({id:r,callback:n}),H.postMessage({vscodeScheduleAsyncWork:r},"*")}}})();const Z=!!(Q&&Q.indexOf("Chrome")>=0),ee=(Q&&Q.indexOf("Firefox"),!Z&&Q&&Q.indexOf("Safari"),Q&&Q.indexOf("Edg/"),Q&&Q.indexOf("Android"),H.performance&&"function"==typeof H.performance.now);class te{static create(e=!0){return new te(e)}constructor(e){this._highResolution=ee&&e,this._startTime=this._now(),this._stopTime=-1}stop(){this._stopTime=this._now()}elapsed(){return-1!==this._stopTime?this._stopTime-this._startTime:this._now()-this._startTime}_now(){return this._highResolution?H.performance.now():Date.now()}}var ne;!function(e){function t(e){false}function n(e){return(t,n=null,r)=>{let i,s=!1;return i=e((e=>{if(!s)return i?i.dispose():s=!0,t.call(n,e)}),null,r),s&&i.dispose(),i}}function r(e,t,n){return a(((n,r=null,i)=>e((e=>n.call(r,t(e))),null,i)),n)}function i(e,t,n){return a(((n,r=null,i)=>e((e=>{t(e),n.call(r,e)}),null,i)),n)}function s(e,t,n){return a(((n,r=null,i)=>e((e=>t(e)&&n.call(r,e)),null,i)),n)}function o(e,t,n,i){let s=n;return r(e,(e=>(s=t(s,e),s)),i)}function a(e,n){let r;const i={onWillAddFirstListener(){r=e(s.fire,s)},onDidRemoveLastListener(){null==r||r.dispose()}};n||t();const s=new ae(i);return null==n||n.add(s),s.event}function l(e,n,r=100,i=!1,s=!1,o,a){let l,u,h,c,d=0;const g={leakWarningThreshold:o,onWillAddFirstListener(){l=e((e=>{d++,u=n(u,e),i&&!h&&(m.fire(u),u=void 0),c=()=>{const e=u;u=void 0,h=void 0,(!i||d>1)&&m.fire(e),d=0},"number"==typeof r?(clearTimeout(h),h=setTimeout(c,r)):void 0===h&&(h=0,queueMicrotask(c))}))},onWillRemoveListener(){s&&d>0&&(null==c||c())},onDidRemoveLastListener(){c=void 0,l.dispose()}};a||t();const m=new ae(g);return null==a||a.add(m),m.event}function u(e,t=((e,t)=>e===t),n){let r,i=!0;return s(e,(e=>{const n=i||!t(e,r);return i=!1,r=e,n}),n)}e.None=()=>C.None,e.defer=function(e,t){return l(e,(()=>{}),0,void 0,!0,void 0,t)},e.once=n,e.map=r,e.forEach=i,e.filter=s,e.signal=function(e){return e},e.any=function(...e){return(t,n=null,r)=>p(...e.map((e=>e((e=>t.call(n,e)),null,r))))},e.reduce=o,e.debounce=l,e.accumulate=function(t,n=0,r){return e.debounce(t,((e,t)=>e?(e.push(t),e):[t]),n,void 0,!0,void 0,r)},e.latch=u,e.split=function(t,n,r){return[e.filter(t,n,r),e.filter(t,(e=>!n(e)),r)]},e.buffer=function(e,t=!1,n=[]){let r=n.slice(),i=e((e=>{r?r.push(e):o.fire(e)}));const s=()=>{null==r||r.forEach((e=>o.fire(e))),r=null},o=new ae({onWillAddFirstListener(){i||(i=e((e=>o.fire(e))))},onDidAddFirstListener(){r&&(t?setTimeout(s):s())},onDidRemoveLastListener(){i&&i.dispose(),i=null}});return o.event};class h{constructor(e){this.event=e,this.disposables=new _}map(e){return new h(r(this.event,e,this.disposables))}forEach(e){return new h(i(this.event,e,this.disposables))}filter(e){return new h(s(this.event,e,this.disposables))}reduce(e,t){return new h(o(this.event,e,t,this.disposables))}latch(){return new h(u(this.event,void 0,this.disposables))}debounce(e,t=100,n=!1,r=!1,i){return new h(l(this.event,e,t,n,r,i,this.disposables))}on(e,t,n){return this.event(e,t,n)}once(e,t,r){return n(this.event)(e,t,r)}dispose(){this.disposables.dispose()}}e.chain=function(e){return new h(e)},e.fromNodeEventEmitter=function(e,t,n=(e=>e)){const r=(...e)=>i.fire(n(...e)),i=new ae({onWillAddFirstListener:()=>e.on(t,r),onDidRemoveLastListener:()=>e.removeListener(t,r)});return i.event},e.fromDOMEventEmitter=function(e,t,n=(e=>e)){const r=(...e)=>i.fire(n(...e)),i=new ae({onWillAddFirstListener:()=>e.addEventListener(t,r),onDidRemoveLastListener:()=>e.removeEventListener(t,r)});return i.event},e.toPromise=function(e){return new Promise((t=>n(e)(t)))},e.runAndSubscribe=function(e,t){return t(void 0),e((e=>t(e)))},e.runAndSubscribeWithStore=function(e,t){let n=null;function r(e){null==n||n.dispose(),n=new _,t(e,n)}r(void 0);const i=e((e=>r(e)));return b((()=>{i.dispose(),null==n||n.dispose()}))};class c{constructor(e,n){this._observable=e,this._counter=0,this._hasChanged=!1;const r={onWillAddFirstListener:()=>{e.addObserver(this)},onDidRemoveLastListener:()=>{e.removeObserver(this)}};n||t(),this.emitter=new ae(r),n&&n.add(this.emitter)}beginUpdate(e){this._counter++}handlePossibleChange(e){}handleChange(e,t){this._hasChanged=!0}endUpdate(e){this._counter--,0===this._counter&&(this._observable.reportChanges(),this._hasChanged&&(this._hasChanged=!1,this.emitter.fire(this._observable.get())))}}e.fromObservable=function(e,t){return new c(e,t).emitter.event},e.fromObservableLight=function(e){return t=>{let n=0,r=!1;const i={beginUpdate(){n++},endUpdate(){n--,0===n&&(e.reportChanges(),r&&(r=!1,t()))},handlePossibleChange(){},handleChange(){r=!0}};return e.addObserver(i),{dispose(){e.removeObserver(i)}}}}}(ne||(ne={}));class re{constructor(e){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${e}_${re._idPool++}`,re.all.add(this)}start(e){this._stopWatch=new te(!0),this.listenerCount=e}stop(){if(this._stopWatch){const e=this._stopWatch.elapsed();this.durations.push(e),this.elapsedOverall+=e,this.invocationCount+=1,this._stopWatch=void 0}}}re.all=new Set,re._idPool=0;class ie{constructor(e,t=Math.random().toString(18).slice(2,5)){this.threshold=e,this.name=t,this._warnCountdown=0}dispose(){var e;null===(e=this._stacks)||void 0===e||e.clear()}check(e,t){const n=this.threshold;if(n<=0||t<n)return;this._stacks||(this._stacks=new Map);const r=this._stacks.get(e.value)||0;if(this._stacks.set(e.value,r+1),this._warnCountdown-=1,this._warnCountdown<=0){let e;this._warnCountdown=.5*n;let r=0;for(const[t,n]of this._stacks)(!e||r<n)&&(e=t,r=n);console.warn(`[${this.name}] potential listener LEAK detected, having ${t} listeners already. MOST frequent listener (${r}):`),console.warn(e)}return()=>{const t=this._stacks.get(e.value)||0;this._stacks.set(e.value,t-1)}}}class se{static create(){var e;return new se(null!==(e=(new Error).stack)&&void 0!==e?e:"")}constructor(e){this.value=e}print(){console.warn(this.value.split("\n").slice(2).join("\n"))}}class oe{constructor(e,t,n){this.callback=e,this.callbackThis=t,this.stack=n,this.subscription=new y}invoke(e){this.callback.call(this.callbackThis,e)}}class ae{constructor(e){var t,n,r,i,s;this._disposed=!1,this._options=e,this._leakageMon=(null===(t=this._options)||void 0===t?void 0:t.leakWarningThreshold)?new ie(null!==(r=null===(n=this._options)||void 0===n?void 0:n.leakWarningThreshold)&&void 0!==r?r:-1):void 0,this._perfMon=(null===(i=this._options)||void 0===i?void 0:i._profName)?new re(this._options._profName):void 0,this._deliveryQueue=null===(s=this._options)||void 0===s?void 0:s.deliveryQueue}dispose(){var e,t,n,r;this._disposed||(this._disposed=!0,this._listeners&&this._listeners.clear(),null===(e=this._deliveryQueue)||void 0===e||e.clear(this),null===(n=null===(t=this._options)||void 0===t?void 0:t.onDidRemoveLastListener)||void 0===n||n.call(t),null===(r=this._leakageMon)||void 0===r||r.dispose())}get event(){return this._event||(this._event=(e,t,n)=>{var r,i,s;if(this._listeners||(this._listeners=new w),this._leakageMon&&this._listeners.size>3*this._leakageMon.threshold)return console.warn(`[${this._leakageMon.name}] REFUSES to accept new listeners because it exceeded its threshold by far`),C.None;const o=this._listeners.isEmpty();let a,l;o&&(null===(r=this._options)||void 0===r?void 0:r.onWillAddFirstListener)&&this._options.onWillAddFirstListener(this),this._leakageMon&&this._listeners.size>=Math.ceil(.2*this._leakageMon.threshold)&&(l=se.create(),a=this._leakageMon.check(l,this._listeners.size+1));const u=new oe(e,t,l),h=this._listeners.push(u);o&&(null===(i=this._options)||void 0===i?void 0:i.onDidAddFirstListener)&&this._options.onDidAddFirstListener(this),(null===(s=this._options)||void 0===s?void 0:s.onDidAddListener)&&this._options.onDidAddListener(this,e,t);const c=u.subscription.set((()=>{var e,t;if(null==a||a(),!this._disposed&&(null===(t=null===(e=this._options)||void 0===e?void 0:e.onWillRemoveListener)||void 0===t||t.call(e,this),h(),this._options&&this._options.onDidRemoveLastListener)){this._listeners&&!this._listeners.isEmpty()||this._options.onDidRemoveLastListener(this)}}));return n instanceof _?n.add(c):Array.isArray(n)&&n.push(c),c}),this._event}fire(e){var t,n,r;if(this._listeners){this._deliveryQueue||(this._deliveryQueue=new ue(null===(t=this._options)||void 0===t?void 0:t.onListenerError));for(const t of this._listeners)this._deliveryQueue.push(this,t,e);null===(n=this._perfMon)||void 0===n||n.start(this._deliveryQueue.size),this._deliveryQueue.deliver(),null===(r=this._perfMon)||void 0===r||r.stop()}}hasListeners(){return!!this._listeners&&!this._listeners.isEmpty()}}class le{constructor(e=t){this._onListenerError=e,this._queue=new w}get size(){return this._queue.size}push(e,t,n){this._queue.push(new he(e,t,n))}clear(e){const t=new w;for(const n of this._queue)n.emitter!==e&&t.push(n);this._queue=t}deliver(){for(;this._queue.size>0;){const e=this._queue.shift();try{e.listener.invoke(e.event)}catch(e){this._onListenerError(e)}}}}class ue extends le{clear(e){this._queue.clear()}}class he{constructor(e,t,n){this.emitter=e,this.listener=t,this.event=n}}Object.prototype.hasOwnProperty;function ce(e){const t=[];for(const n of function(e){let t=[],n=Object.getPrototypeOf(e);for(;Object.prototype!==n;)t=t.concat(Object.getOwnPropertyNames(n)),n=Object.getPrototypeOf(n);return t}(e))"function"==typeof e[n]&&t.push(n);return t}class de{constructor(e){this.executor=e,this._didRun=!1}get value(){if(!this._didRun)try{this._value=this.executor()}catch(e){this._error=e}finally{this._didRun=!0}if(this._error)throw this._error;return this._value}get rawValue(){return this._value}}var ge;function me(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function fe(e){return e>=65&&e<=90}function pe(e){return 55296<=e&&e<=56319}function be(e){return 56320<=e&&e<=57343}function _e(e,t){return t-56320+(e-55296<<10)+65536}function Ce(e,t,n){const r=e.charCodeAt(n);if(pe(r)&&n+1<t){const t=e.charCodeAt(n+1);if(be(t))return _e(r,t)}return r}const ye=/^[\t\n\r\x20-\x7E]*$/;String.fromCharCode(65279);class ve{static getInstance(){return ve._INSTANCE||(ve._INSTANCE=new ve),ve._INSTANCE}constructor(){this._data=JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}getGraphemeBreakType(e){if(e<32)return 10===e?3:13===e?2:4;if(e<127)return 0;const t=this._data,n=t.length/3;let r=1;for(;r<=n;)if(e<t[3*r])r*=2;else{if(!(e>t[3*r+1]))return t[3*r+2];r=2*r+1}return 0}}ve._INSTANCE=null;class we{static getInstance(e){return we.cache.get(Array.from(e))}static getLocales(){return we._locales.value}constructor(e){this.confusableDictionary=e}isAmbiguous(e){return this.confusableDictionary.has(e)}getPrimaryConfusable(e){return this.confusableDictionary.get(e)}getConfusableCodePoints(){return new Set(this.confusableDictionary.keys())}}ge=we,we.ambiguousCharacterData=new de((()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,8218,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,8242,96,1370,96,1523,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71922,67,71913,67,65315,67,8557,67,8450,67,8493,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71919,87,71910,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,66293,90,71909,90,65338,90,8484,90,8488,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65297,49,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"cs":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"es":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"fr":[65374,126,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"it":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ja":[8211,45,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65292,44,65307,59],"ko":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pt-BR":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ru":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"zh-hans":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41],"zh-hant":[8211,45,65374,126,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65307,59]}'))),we.cache=new class{constructor(e){this.fn=e,this.lastCache=void 0,this.lastArgKey=void 0}get(e){const t=JSON.stringify(e);return this.lastArgKey!==t&&(this.lastArgKey=t,this.lastCache=this.fn(e)),this.lastCache}}((e=>{function t(e){const t=new Map;for(let n=0;n<e.length;n+=2)t.set(e[n],e[n+1]);return t}function n(e,t){if(!e)return t;const n=new Map;for(const[r,i]of e)t.has(r)&&n.set(r,i);return n}const r=ge.ambiguousCharacterData.value;let i,s=e.filter((e=>!e.startsWith("_")&&e in r));0===s.length&&(s=["_default"]);for(const e of s){i=n(i,t(r[e]))}const o=function(e,t){const n=new Map(e);for(const[e,r]of t)n.set(e,r);return n}(t(r._common),i);return new we(o)})),we._locales=new de((()=>Object.keys(we.ambiguousCharacterData.value).filter((e=>!e.startsWith("_")))));class Le{static getRawData(){return JSON.parse("[9,10,11,12,13,32,127,160,173,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8203,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12288,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999]")}static getData(){return this._data||(this._data=new Set(Le.getRawData())),this._data}static isInvisibleCharacter(e){return Le.getData().has(e)}static get codePoints(){return Le.getData()}}Le._data=void 0;const Se="$initialize";class Ne{constructor(e,t,n,r){this.vsWorker=e,this.req=t,this.method=n,this.args=r,this.type=0}}class Ee{constructor(e,t,n,r){this.vsWorker=e,this.seq=t,this.res=n,this.err=r,this.type=1}}class Ae{constructor(e,t,n,r){this.vsWorker=e,this.req=t,this.eventName=n,this.arg=r,this.type=2}}class Re{constructor(e,t,n){this.vsWorker=e,this.req=t,this.event=n,this.type=3}}class xe{constructor(e,t){this.vsWorker=e,this.req=t,this.type=4}}class Me{constructor(e){this._workerId=-1,this._handler=e,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(e){this._workerId=e}sendMessage(e,t){const n=String(++this._lastSentReq);return new Promise(((r,i)=>{this._pendingReplies[n]={resolve:r,reject:i},this._send(new Ne(this._workerId,n,e,t))}))}listen(e,t){let n=null;const r=new ae({onWillAddFirstListener:()=>{n=String(++this._lastSentReq),this._pendingEmitters.set(n,r),this._send(new Ae(this._workerId,n,e,t))},onDidRemoveLastListener:()=>{this._pendingEmitters.delete(n),this._send(new xe(this._workerId,n)),n=null}});return r.event}handleMessage(e){e&&e.vsWorker&&(-1!==this._workerId&&e.vsWorker!==this._workerId||this._handleMessage(e))}_handleMessage(e){switch(e.type){case 1:return this._handleReplyMessage(e);case 0:return this._handleRequestMessage(e);case 2:return this._handleSubscribeEventMessage(e);case 3:return this._handleEventMessage(e);case 4:return this._handleUnsubscribeEventMessage(e)}}_handleReplyMessage(e){if(!this._pendingReplies[e.seq])return void console.warn("Got reply to unknown seq");const t=this._pendingReplies[e.seq];if(delete this._pendingReplies[e.seq],e.err){let n=e.err;return e.err.$isError&&(n=new Error,n.name=e.err.name,n.message=e.err.message,n.stack=e.err.stack),void t.reject(n)}t.resolve(e.res)}_handleRequestMessage(e){const t=e.req;this._handler.handleMessage(e.method,e.args).then((e=>{this._send(new Ee(this._workerId,t,e,void 0))}),(e=>{e.detail instanceof Error&&(e.detail=r(e.detail)),this._send(new Ee(this._workerId,t,void 0,r(e)))}))}_handleSubscribeEventMessage(e){const t=e.req,n=this._handler.handleEvent(e.eventName,e.arg)((e=>{this._send(new Re(this._workerId,t,e))}));this._pendingEvents.set(t,n)}_handleEventMessage(e){this._pendingEmitters.has(e.req)?this._pendingEmitters.get(e.req).fire(e.event):console.warn("Got event for unknown req")}_handleUnsubscribeEventMessage(e){this._pendingEvents.has(e.req)?(this._pendingEvents.get(e.req).dispose(),this._pendingEvents.delete(e.req)):console.warn("Got unsubscribe for unknown req")}_send(e){const t=[];if(0===e.type)for(let n=0;n<e.args.length;n++)e.args[n]instanceof ArrayBuffer&&t.push(e.args[n]);else 1===e.type&&e.res instanceof ArrayBuffer&&t.push(e.res);this._handler.sendMessage(e,t)}}function ke(e){return"o"===e[0]&&"n"===e[1]&&fe(e.charCodeAt(2))}function Te(e){return/^onDynamic/.test(e)&&fe(e.charCodeAt(9))}function Oe(e,t,n){const r=e=>function(){const n=Array.prototype.slice.call(arguments,0);return t(e,n)},i=e=>function(t){return n(e,t)},s={};for(const t of e)Te(t)?s[t]=i(t):ke(t)?s[t]=n(t,void 0):s[t]=r(t);return s}class Ie{constructor(e,t){this._requestHandlerFactory=t,this._requestHandler=null,this._protocol=new Me({sendMessage:(t,n)=>{e(t,n)},handleMessage:(e,t)=>this._handleMessage(e,t),handleEvent:(e,t)=>this._handleEvent(e,t)})}onmessage(e){this._protocol.handleMessage(e)}_handleMessage(e,t){if(e===Se)return this.initialize(t[0],t[1],t[2],t[3]);if(!this._requestHandler||"function"!=typeof this._requestHandler[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._requestHandler[e].apply(this._requestHandler,t))}catch(e){return Promise.reject(e)}}_handleEvent(e,t){if(!this._requestHandler)throw new Error("Missing requestHandler");if(Te(e)){const n=this._requestHandler[e].call(this._requestHandler,t);if("function"!=typeof n)throw new Error(`Missing dynamic event ${e} on request handler.`);return n}if(ke(e)){const t=this._requestHandler[e];if("function"!=typeof t)throw new Error(`Missing event ${e} on request handler.`);return t}throw new Error(`Malformed event name ${e}`)}initialize(e,t,n,r){this._protocol.setWorkerId(e);const i=Oe(r,((e,t)=>this._protocol.sendMessage(e,t)),((e,t)=>this._protocol.listen(e,t)));return this._requestHandlerFactory?(this._requestHandler=this._requestHandlerFactory(i),Promise.resolve(ce(this._requestHandler))):(t&&(void 0!==t.baseUrl&&delete t.baseUrl,void 0!==t.paths&&void 0!==t.paths.vs&&delete t.paths.vs,void 0!==typeof t.trustedTypesPolicy&&delete t.trustedTypesPolicy,t.catchError=!0,globalThis.require.config(t)),new Promise(((e,t)=>{(0,globalThis.require)([n],(n=>{this._requestHandler=n.create(i),this._requestHandler?e(ce(this._requestHandler)):t(new Error("No RequestHandler!"))}),t)})))}}class De{constructor(e,t,n,r){this.originalStart=e,this.originalLength=t,this.modifiedStart=n,this.modifiedLength=r}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}}function Pe(e,t){return(t<<5)-t+e|0}function Fe(e,t){t=Pe(149417,t);for(let n=0,r=e.length;n<r;n++)t=Pe(e.charCodeAt(n),t);return t}function Ke(e,t,n=32){const r=n-t;return(e<<t|(~((1<<r)-1)&e)>>>r)>>>0}function Ve(e,t=0,n=e.byteLength,r=0){for(let i=0;i<n;i++)e[t+i]=r}function Be(e,t=32){return e instanceof ArrayBuffer?Array.from(new Uint8Array(e)).map((e=>e.toString(16).padStart(2,"0"))).join(""):function(e,t,n="0"){for(;e.length<t;)e=n+e;return e}((e>>>0).toString(16),t/4)}class qe{constructor(){this._h0=1732584193,this._h1=4023233417,this._h2=2562383102,this._h3=271733878,this._h4=3285377520,this._buff=new Uint8Array(67),this._buffDV=new DataView(this._buff.buffer),this._buffLen=0,this._totalLen=0,this._leftoverHighSurrogate=0,this._finished=!1}update(e){const t=e.length;if(0===t)return;const n=this._buff;let r,i,s=this._buffLen,o=this._leftoverHighSurrogate;for(0!==o?(r=o,i=-1,o=0):(r=e.charCodeAt(0),i=0);;){let a=r;if(pe(r)){if(!(i+1<t)){o=r;break}{const t=e.charCodeAt(i+1);be(t)?(i++,a=_e(r,t)):a=65533}}else be(r)&&(a=65533);if(s=this._push(n,s,a),i++,!(i<t))break;r=e.charCodeAt(i)}this._buffLen=s,this._leftoverHighSurrogate=o}_push(e,t,n){return n<128?e[t++]=n:n<2048?(e[t++]=192|(1984&n)>>>6,e[t++]=128|(63&n)>>>0):n<65536?(e[t++]=224|(61440&n)>>>12,e[t++]=128|(4032&n)>>>6,e[t++]=128|(63&n)>>>0):(e[t++]=240|(1835008&n)>>>18,e[t++]=128|(258048&n)>>>12,e[t++]=128|(4032&n)>>>6,e[t++]=128|(63&n)>>>0),t>=64&&(this._step(),t-=64,this._totalLen+=64,e[0]=e[64],e[1]=e[65],e[2]=e[66]),t}digest(){return this._finished||(this._finished=!0,this._leftoverHighSurrogate&&(this._leftoverHighSurrogate=0,this._buffLen=this._push(this._buff,this._buffLen,65533)),this._totalLen+=this._buffLen,this._wrapUp()),Be(this._h0)+Be(this._h1)+Be(this._h2)+Be(this._h3)+Be(this._h4)}_wrapUp(){this._buff[this._buffLen++]=128,Ve(this._buff,this._buffLen),this._buffLen>56&&(this._step(),Ve(this._buff));const e=8*this._totalLen;this._buffDV.setUint32(56,Math.floor(e/4294967296),!1),this._buffDV.setUint32(60,e%4294967296,!1),this._step()}_step(){const e=qe._bigBlock32,t=this._buffDV;for(let n=0;n<64;n+=4)e.setUint32(n,t.getUint32(n,!1),!1);for(let t=64;t<320;t+=4)e.setUint32(t,Ke(e.getUint32(t-12,!1)^e.getUint32(t-32,!1)^e.getUint32(t-56,!1)^e.getUint32(t-64,!1),1),!1);let n,r,i,s=this._h0,o=this._h1,a=this._h2,l=this._h3,u=this._h4;for(let t=0;t<80;t++)t<20?(n=o&a|~o&l,r=1518500249):t<40?(n=o^a^l,r=1859775393):t<60?(n=o&a|o&l|a&l,r=2400959708):(n=o^a^l,r=3395469782),i=Ke(s,5)+n+u+r+e.getUint32(4*t,!1)&4294967295,u=l,l=a,a=Ke(o,30),o=s,s=i;this._h0=this._h0+s&4294967295,this._h1=this._h1+o&4294967295,this._h2=this._h2+a&4294967295,this._h3=this._h3+l&4294967295,this._h4=this._h4+u&4294967295}}qe._bigBlock32=new DataView(new ArrayBuffer(320));class Ue{constructor(e){this.source=e}getElements(){const e=this.source,t=new Int32Array(e.length);for(let n=0,r=e.length;n<r;n++)t[n]=e.charCodeAt(n);return t}}function We(e,t,n){return new je(new Ue(e),new Ue(t)).ComputeDiff(n).changes}class He{static Assert(e,t){if(!e)throw new Error(t)}}class $e{static Copy(e,t,n,r,i){for(let s=0;s<i;s++)n[r+s]=e[t+s]}static Copy2(e,t,n,r,i){for(let s=0;s<i;s++)n[r+s]=e[t+s]}}class ze{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new De(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_originalCount++}AddModifiedElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}}class je{constructor(e,t,n=null){this.ContinueProcessingPredicate=n,this._originalSequence=e,this._modifiedSequence=t;const[r,i,s]=je._getElements(e),[o,a,l]=je._getElements(t);this._hasStrings=s&&l,this._originalStringElements=r,this._originalElementsOrHash=i,this._modifiedStringElements=o,this._modifiedElementsOrHash=a,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(e){return e.length>0&&"string"==typeof e[0]}static _getElements(e){const t=e.getElements();if(je._isStringArray(t)){const e=new Int32Array(t.length);for(let n=0,r=t.length;n<r;n++)e[n]=Fe(t[n],0);return[t,e,!0]}return t instanceof Int32Array?[[],t,!1]:[[],new Int32Array(t),!1]}ElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._modifiedElementsOrHash[t]&&(!this._hasStrings||this._originalStringElements[e]===this._modifiedStringElements[t])}ElementsAreStrictEqual(e,t){if(!this.ElementsAreEqual(e,t))return!1;return je._getStrictElement(this._originalSequence,e)===je._getStrictElement(this._modifiedSequence,t)}static _getStrictElement(e,t){return"function"==typeof e.getStrictElement?e.getStrictElement(t):null}OriginalElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._originalElementsOrHash[t]&&(!this._hasStrings||this._originalStringElements[e]===this._originalStringElements[t])}ModifiedElementsAreEqual(e,t){return this._modifiedElementsOrHash[e]===this._modifiedElementsOrHash[t]&&(!this._hasStrings||this._modifiedStringElements[e]===this._modifiedStringElements[t])}ComputeDiff(e){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,e)}_ComputeDiff(e,t,n,r,i){const s=[!1];let o=this.ComputeDiffRecursive(e,t,n,r,s);return i&&(o=this.PrettifyChanges(o)),{quitEarly:s[0],changes:o}}ComputeDiffRecursive(e,t,n,r,i){for(i[0]=!1;e<=t&&n<=r&&this.ElementsAreEqual(e,n);)e++,n++;for(;t>=e&&r>=n&&this.ElementsAreEqual(t,r);)t--,r--;if(e>t||n>r){let i;return n<=r?(He.Assert(e===t+1,"originalStart should only be one more than originalEnd"),i=[new De(e,0,n,r-n+1)]):e<=t?(He.Assert(n===r+1,"modifiedStart should only be one more than modifiedEnd"),i=[new De(e,t-e+1,n,0)]):(He.Assert(e===t+1,"originalStart should only be one more than originalEnd"),He.Assert(n===r+1,"modifiedStart should only be one more than modifiedEnd"),i=[]),i}const s=[0],o=[0],a=this.ComputeRecursionPoint(e,t,n,r,s,o,i),l=s[0],u=o[0];if(null!==a)return a;if(!i[0]){const s=this.ComputeDiffRecursive(e,l,n,u,i);let o=[];return o=i[0]?[new De(l+1,t-(l+1)+1,u+1,r-(u+1)+1)]:this.ComputeDiffRecursive(l+1,t,u+1,r,i),this.ConcatenateChanges(s,o)}return[new De(e,t-e+1,n,r-n+1)]}WALKTRACE(e,t,n,r,i,s,o,a,l,u,h,c,d,g,m,f,p,b){let _=null,C=null,y=new ze,v=t,w=n,L=d[0]-f[0]-r,S=-1073741824,N=this.m_forwardHistory.length-1;do{const t=L+e;t===v||t<w&&l[t-1]<l[t+1]?(g=(h=l[t+1])-L-r,h<S&&y.MarkNextChange(),S=h,y.AddModifiedElement(h+1,g),L=t+1-e):(g=(h=l[t-1]+1)-L-r,h<S&&y.MarkNextChange(),S=h-1,y.AddOriginalElement(h,g+1),L=t-1-e),N>=0&&(e=(l=this.m_forwardHistory[N])[0],v=1,w=l.length-1)}while(--N>=-1);if(_=y.getReverseChanges(),b[0]){let e=d[0]+1,t=f[0]+1;if(null!==_&&_.length>0){const n=_[_.length-1];e=Math.max(e,n.getOriginalEnd()),t=Math.max(t,n.getModifiedEnd())}C=[new De(e,c-e+1,t,m-t+1)]}else{y=new ze,v=s,w=o,L=d[0]-f[0]-a,S=1073741824,N=p?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{const e=L+i;e===v||e<w&&u[e-1]>=u[e+1]?(g=(h=u[e+1]-1)-L-a,h>S&&y.MarkNextChange(),S=h+1,y.AddOriginalElement(h+1,g+1),L=e+1-i):(g=(h=u[e-1])-L-a,h>S&&y.MarkNextChange(),S=h,y.AddModifiedElement(h+1,g+1),L=e-1-i),N>=0&&(i=(u=this.m_reverseHistory[N])[0],v=1,w=u.length-1)}while(--N>=-1);C=y.getChanges()}return this.ConcatenateChanges(_,C)}ComputeRecursionPoint(e,t,n,r,i,s,o){let a=0,l=0,u=0,h=0,c=0,d=0;e--,n--,i[0]=0,s[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];const g=t-e+(r-n),m=g+1,f=new Int32Array(m),p=new Int32Array(m),b=r-n,_=t-e,C=e-n,y=t-r,v=(_-b)%2==0;f[b]=e,p[_]=t,o[0]=!1;for(let w=1;w<=g/2+1;w++){let g=0,L=0;u=this.ClipDiagonalBound(b-w,w,b,m),h=this.ClipDiagonalBound(b+w,w,b,m);for(let e=u;e<=h;e+=2){a=e===u||e<h&&f[e-1]<f[e+1]?f[e+1]:f[e-1]+1,l=a-(e-b)-C;const n=a;for(;a<t&&l<r&&this.ElementsAreEqual(a+1,l+1);)a++,l++;if(f[e]=a,a+l>g+L&&(g=a,L=l),!v&&Math.abs(e-_)<=w-1&&a>=p[e])return i[0]=a,s[0]=l,n<=p[e]&&w<=1448?this.WALKTRACE(b,u,h,C,_,c,d,y,f,p,a,t,i,l,r,s,v,o):null}const S=(g-e+(L-n)-w)/2;if(null!==this.ContinueProcessingPredicate&&!this.ContinueProcessingPredicate(g,S))return o[0]=!0,i[0]=g,s[0]=L,S>0&&w<=1448?this.WALKTRACE(b,u,h,C,_,c,d,y,f,p,a,t,i,l,r,s,v,o):(e++,n++,[new De(e,t-e+1,n,r-n+1)]);c=this.ClipDiagonalBound(_-w,w,_,m),d=this.ClipDiagonalBound(_+w,w,_,m);for(let g=c;g<=d;g+=2){a=g===c||g<d&&p[g-1]>=p[g+1]?p[g+1]-1:p[g-1],l=a-(g-_)-y;const m=a;for(;a>e&&l>n&&this.ElementsAreEqual(a,l);)a--,l--;if(p[g]=a,v&&Math.abs(g-b)<=w&&a<=f[g])return i[0]=a,s[0]=l,m>=f[g]&&w<=1448?this.WALKTRACE(b,u,h,C,_,c,d,y,f,p,a,t,i,l,r,s,v,o):null}if(w<=1447){let e=new Int32Array(h-u+2);e[0]=b-u+1,$e.Copy2(f,u,e,1,h-u+1),this.m_forwardHistory.push(e),e=new Int32Array(d-c+2),e[0]=_-c+1,$e.Copy2(p,c,e,1,d-c+1),this.m_reverseHistory.push(e)}}return this.WALKTRACE(b,u,h,C,_,c,d,y,f,p,a,t,i,l,r,s,v,o)}PrettifyChanges(e){for(let t=0;t<e.length;t++){const n=e[t],r=t<e.length-1?e[t+1].originalStart:this._originalElementsOrHash.length,i=t<e.length-1?e[t+1].modifiedStart:this._modifiedElementsOrHash.length,s=n.originalLength>0,o=n.modifiedLength>0;for(;n.originalStart+n.originalLength<r&&n.modifiedStart+n.modifiedLength<i&&(!s||this.OriginalElementsAreEqual(n.originalStart,n.originalStart+n.originalLength))&&(!o||this.ModifiedElementsAreEqual(n.modifiedStart,n.modifiedStart+n.modifiedLength));){const e=this.ElementsAreStrictEqual(n.originalStart,n.modifiedStart);if(this.ElementsAreStrictEqual(n.originalStart+n.originalLength,n.modifiedStart+n.modifiedLength)&&!e)break;n.originalStart++,n.modifiedStart++}const a=[null];t<e.length-1&&this.ChangesOverlap(e[t],e[t+1],a)&&(e[t]=a[0],e.splice(t+1,1),t--)}for(let t=e.length-1;t>=0;t--){const n=e[t];let r=0,i=0;if(t>0){const n=e[t-1];r=n.originalStart+n.originalLength,i=n.modifiedStart+n.modifiedLength}const s=n.originalLength>0,o=n.modifiedLength>0;let a=0,l=this._boundaryScore(n.originalStart,n.originalLength,n.modifiedStart,n.modifiedLength);for(let e=1;;e++){const t=n.originalStart-e,u=n.modifiedStart-e;if(t<r||u<i)break;if(s&&!this.OriginalElementsAreEqual(t,t+n.originalLength))break;if(o&&!this.ModifiedElementsAreEqual(u,u+n.modifiedLength))break;const h=(t===r&&u===i?5:0)+this._boundaryScore(t,n.originalLength,u,n.modifiedLength);h>l&&(l=h,a=e)}n.originalStart-=a,n.modifiedStart-=a;const u=[null];t>0&&this.ChangesOverlap(e[t-1],e[t],u)&&(e[t-1]=u[0],e.splice(t,1),t++)}if(this._hasStrings)for(let t=1,n=e.length;t<n;t++){const n=e[t-1],r=e[t],i=r.originalStart-n.originalStart-n.originalLength,s=n.originalStart,o=r.originalStart+r.originalLength,a=o-s,l=n.modifiedStart,u=r.modifiedStart+r.modifiedLength,h=u-l;if(i<5&&a<20&&h<20){const e=this._findBetterContiguousSequence(s,a,l,h,i);if(e){const[t,s]=e;t===n.originalStart+n.originalLength&&s===n.modifiedStart+n.modifiedLength||(n.originalLength=t-n.originalStart,n.modifiedLength=s-n.modifiedStart,r.originalStart=t+i,r.modifiedStart=s+i,r.originalLength=o-r.originalStart,r.modifiedLength=u-r.modifiedStart)}}}return e}_findBetterContiguousSequence(e,t,n,r,i){if(t<i||r<i)return null;const s=e+t-i+1,o=n+r-i+1;let a=0,l=0,u=0;for(let t=e;t<s;t++)for(let e=n;e<o;e++){const n=this._contiguousSequenceScore(t,e,i);n>0&&n>a&&(a=n,l=t,u=e)}return a>0?[l,u]:null}_contiguousSequenceScore(e,t,n){let r=0;for(let i=0;i<n;i++){if(!this.ElementsAreEqual(e+i,t+i))return 0;r+=this._originalStringElements[e+i].length}return r}_OriginalIsBoundary(e){return e<=0||e>=this._originalElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._originalStringElements[e])}_OriginalRegionIsBoundary(e,t){if(this._OriginalIsBoundary(e)||this._OriginalIsBoundary(e-1))return!0;if(t>0){const n=e+t;if(this._OriginalIsBoundary(n-1)||this._OriginalIsBoundary(n))return!0}return!1}_ModifiedIsBoundary(e){return e<=0||e>=this._modifiedElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[e])}_ModifiedRegionIsBoundary(e,t){if(this._ModifiedIsBoundary(e)||this._ModifiedIsBoundary(e-1))return!0;if(t>0){const n=e+t;if(this._ModifiedIsBoundary(n-1)||this._ModifiedIsBoundary(n))return!0}return!1}_boundaryScore(e,t,n,r){return(this._OriginalRegionIsBoundary(e,t)?1:0)+(this._ModifiedRegionIsBoundary(n,r)?1:0)}ConcatenateChanges(e,t){const n=[];if(0===e.length||0===t.length)return t.length>0?t:e;if(this.ChangesOverlap(e[e.length-1],t[0],n)){const r=new Array(e.length+t.length-1);return $e.Copy(e,0,r,0,e.length-1),r[e.length-1]=n[0],$e.Copy(t,1,r,e.length,t.length-1),r}{const n=new Array(e.length+t.length);return $e.Copy(e,0,n,0,e.length),$e.Copy(t,0,n,e.length,t.length),n}}ChangesOverlap(e,t,n){if(He.Assert(e.originalStart<=t.originalStart,"Left change is not less than or equal to right change"),He.Assert(e.modifiedStart<=t.modifiedStart,"Left change is not less than or equal to right change"),e.originalStart+e.originalLength>=t.originalStart||e.modifiedStart+e.modifiedLength>=t.modifiedStart){const r=e.originalStart;let i=e.originalLength;const s=e.modifiedStart;let o=e.modifiedLength;return e.originalStart+e.originalLength>=t.originalStart&&(i=t.originalStart+t.originalLength-e.originalStart),e.modifiedStart+e.modifiedLength>=t.modifiedStart&&(o=t.modifiedStart+t.modifiedLength-e.modifiedStart),n[0]=new De(r,i,s,o),!0}return n[0]=null,!1}ClipDiagonalBound(e,t,n,r){if(e>=0&&e<r)return e;const i=t%2==0;if(e<0){return i===(n%2==0)?0:1}return i===((r-n-1)%2==0)?r-1:r-2}}var Ge=n(155);let Ye;if(void 0!==H.vscode&&void 0!==H.vscode.process){const e=H.vscode.process;Ye={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd:()=>e.cwd()}}else Ye=void 0!==Ge?{get platform(){return Ge.platform},get arch(){return Ge.arch},get env(){return Ge.env},cwd:()=>Ge.env.VSCODE_CWD||Ge.cwd()}:{get platform(){return Y?"win32":J?"darwin":"linux"},get arch(){},get env(){return{}},cwd:()=>"/"};const Je=Ye.cwd,Qe=Ye.env,Xe=Ye.platform,Ze=46,et=47,tt=92,nt=58;class rt extends Error{constructor(e,t,n){let r;"string"==typeof t&&0===t.indexOf("not ")?(r="must not be",t=t.replace(/^not /,"")):r="must be";const i=-1!==e.indexOf(".")?"property":"argument";let s=`The "${e}" ${i} ${r} of type ${t}`;s+=". Received type "+typeof n,super(s),this.code="ERR_INVALID_ARG_TYPE"}}function it(e,t){if("string"!=typeof e)throw new rt(t,"string",e)}const st="win32"===Xe;function ot(e){return e===et||e===tt}function at(e){return e===et}function lt(e){return e>=65&&e<=90||e>=97&&e<=122}function ut(e,t,n,r){let i="",s=0,o=-1,a=0,l=0;for(let u=0;u<=e.length;++u){if(u<e.length)l=e.charCodeAt(u);else{if(r(l))break;l=et}if(r(l)){if(o===u-1||1===a);else if(2===a){if(i.length<2||2!==s||i.charCodeAt(i.length-1)!==Ze||i.charCodeAt(i.length-2)!==Ze){if(i.length>2){const e=i.lastIndexOf(n);-1===e?(i="",s=0):(i=i.slice(0,e),s=i.length-1-i.lastIndexOf(n)),o=u,a=0;continue}if(0!==i.length){i="",s=0,o=u,a=0;continue}}t&&(i+=i.length>0?`${n}..`:"..",s=2)}else i.length>0?i+=`${n}${e.slice(o+1,u)}`:i=e.slice(o+1,u),s=u-o-1;o=u,a=0}else l===Ze&&-1!==a?++a:a=-1}return i}function ht(e,t){!function(e,t){if(null===e||"object"!=typeof e)throw new rt(t,"Object",e)}(t,"pathObject");const n=t.dir||t.root,r=t.base||`${t.name||""}${t.ext||""}`;return n?n===t.root?`${n}${r}`:`${n}${e}${r}`:r}const ct={resolve(...e){let t="",n="",r=!1;for(let i=e.length-1;i>=-1;i--){let s;if(i>=0){if(s=e[i],it(s,"path"),0===s.length)continue}else 0===t.length?s=Je():(s=Qe[`=${t}`]||Je(),(void 0===s||s.slice(0,2).toLowerCase()!==t.toLowerCase()&&s.charCodeAt(2)===tt)&&(s=`${t}\\`));const o=s.length;let a=0,l="",u=!1;const h=s.charCodeAt(0);if(1===o)ot(h)&&(a=1,u=!0);else if(ot(h))if(u=!0,ot(s.charCodeAt(1))){let e=2,t=e;for(;e<o&&!ot(s.charCodeAt(e));)e++;if(e<o&&e!==t){const n=s.slice(t,e);for(t=e;e<o&&ot(s.charCodeAt(e));)e++;if(e<o&&e!==t){for(t=e;e<o&&!ot(s.charCodeAt(e));)e++;e!==o&&e===t||(l=`\\\\${n}\\${s.slice(t,e)}`,a=e)}}}else a=1;else lt(h)&&s.charCodeAt(1)===nt&&(l=s.slice(0,2),a=2,o>2&&ot(s.charCodeAt(2))&&(u=!0,a=3));if(l.length>0)if(t.length>0){if(l.toLowerCase()!==t.toLowerCase())continue}else t=l;if(r){if(t.length>0)break}else if(n=`${s.slice(a)}\\${n}`,r=u,u&&t.length>0)break}return n=ut(n,!r,"\\",ot),r?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){it(e,"path");const t=e.length;if(0===t)return".";let n,r=0,i=!1;const s=e.charCodeAt(0);if(1===t)return at(s)?"\\":e;if(ot(s))if(i=!0,ot(e.charCodeAt(1))){let i=2,s=i;for(;i<t&&!ot(e.charCodeAt(i));)i++;if(i<t&&i!==s){const o=e.slice(s,i);for(s=i;i<t&&ot(e.charCodeAt(i));)i++;if(i<t&&i!==s){for(s=i;i<t&&!ot(e.charCodeAt(i));)i++;if(i===t)return`\\\\${o}\\${e.slice(s)}\\`;i!==s&&(n=`\\\\${o}\\${e.slice(s,i)}`,r=i)}}}else r=1;else lt(s)&&e.charCodeAt(1)===nt&&(n=e.slice(0,2),r=2,t>2&&ot(e.charCodeAt(2))&&(i=!0,r=3));let o=r<t?ut(e.slice(r),!i,"\\",ot):"";return 0!==o.length||i||(o="."),o.length>0&&ot(e.charCodeAt(t-1))&&(o+="\\"),void 0===n?i?`\\${o}`:o:i?`${n}\\${o}`:`${n}${o}`},isAbsolute(e){it(e,"path");const t=e.length;if(0===t)return!1;const n=e.charCodeAt(0);return ot(n)||t>2&&lt(n)&&e.charCodeAt(1)===nt&&ot(e.charCodeAt(2))},join(...e){if(0===e.length)return".";let t,n;for(let r=0;r<e.length;++r){const i=e[r];it(i,"path"),i.length>0&&(void 0===t?t=n=i:t+=`\\${i}`)}if(void 0===t)return".";let r=!0,i=0;if("string"==typeof n&&ot(n.charCodeAt(0))){++i;const e=n.length;e>1&&ot(n.charCodeAt(1))&&(++i,e>2&&(ot(n.charCodeAt(2))?++i:r=!1))}if(r){for(;i<t.length&&ot(t.charCodeAt(i));)i++;i>=2&&(t=`\\${t.slice(i)}`)}return ct.normalize(t)},relative(e,t){if(it(e,"from"),it(t,"to"),e===t)return"";const n=ct.resolve(e),r=ct.resolve(t);if(n===r)return"";if((e=n.toLowerCase())===(t=r.toLowerCase()))return"";let i=0;for(;i<e.length&&e.charCodeAt(i)===tt;)i++;let s=e.length;for(;s-1>i&&e.charCodeAt(s-1)===tt;)s--;const o=s-i;let a=0;for(;a<t.length&&t.charCodeAt(a)===tt;)a++;let l=t.length;for(;l-1>a&&t.charCodeAt(l-1)===tt;)l--;const u=l-a,h=o<u?o:u;let c=-1,d=0;for(;d<h;d++){const n=e.charCodeAt(i+d);if(n!==t.charCodeAt(a+d))break;n===tt&&(c=d)}if(d!==h){if(-1===c)return r}else{if(u>h){if(t.charCodeAt(a+d)===tt)return r.slice(a+d+1);if(2===d)return r.slice(a+d)}o>h&&(e.charCodeAt(i+d)===tt?c=d:2===d&&(c=3)),-1===c&&(c=0)}let g="";for(d=i+c+1;d<=s;++d)d!==s&&e.charCodeAt(d)!==tt||(g+=0===g.length?"..":"\\..");return a+=c,g.length>0?`${g}${r.slice(a,l)}`:(r.charCodeAt(a)===tt&&++a,r.slice(a,l))},toNamespacedPath(e){if("string"!=typeof e||0===e.length)return e;const t=ct.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===tt){if(t.charCodeAt(1)===tt){const e=t.charCodeAt(2);if(63!==e&&e!==Ze)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(lt(t.charCodeAt(0))&&t.charCodeAt(1)===nt&&t.charCodeAt(2)===tt)return`\\\\?\\${t}`;return e},dirname(e){it(e,"path");const t=e.length;if(0===t)return".";let n=-1,r=0;const i=e.charCodeAt(0);if(1===t)return ot(i)?e:".";if(ot(i)){if(n=r=1,ot(e.charCodeAt(1))){let i=2,s=i;for(;i<t&&!ot(e.charCodeAt(i));)i++;if(i<t&&i!==s){for(s=i;i<t&&ot(e.charCodeAt(i));)i++;if(i<t&&i!==s){for(s=i;i<t&&!ot(e.charCodeAt(i));)i++;if(i===t)return e;i!==s&&(n=r=i+1)}}}}else lt(i)&&e.charCodeAt(1)===nt&&(n=t>2&&ot(e.charCodeAt(2))?3:2,r=n);let s=-1,o=!0;for(let n=t-1;n>=r;--n)if(ot(e.charCodeAt(n))){if(!o){s=n;break}}else o=!1;if(-1===s){if(-1===n)return".";s=n}return e.slice(0,s)},basename(e,t){void 0!==t&&it(t,"ext"),it(e,"path");let n,r=0,i=-1,s=!0;if(e.length>=2&&lt(e.charCodeAt(0))&&e.charCodeAt(1)===nt&&(r=2),void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(n=e.length-1;n>=r;--n){const l=e.charCodeAt(n);if(ot(l)){if(!s){r=n+1;break}}else-1===a&&(s=!1,a=n+1),o>=0&&(l===t.charCodeAt(o)?-1==--o&&(i=n):(o=-1,i=a))}return r===i?i=a:-1===i&&(i=e.length),e.slice(r,i)}for(n=e.length-1;n>=r;--n)if(ot(e.charCodeAt(n))){if(!s){r=n+1;break}}else-1===i&&(s=!1,i=n+1);return-1===i?"":e.slice(r,i)},extname(e){it(e,"path");let t=0,n=-1,r=0,i=-1,s=!0,o=0;e.length>=2&&e.charCodeAt(1)===nt&&lt(e.charCodeAt(0))&&(t=r=2);for(let a=e.length-1;a>=t;--a){const t=e.charCodeAt(a);if(ot(t)){if(!s){r=a+1;break}}else-1===i&&(s=!1,i=a+1),t===Ze?-1===n?n=a:1!==o&&(o=1):-1!==n&&(o=-1)}return-1===n||-1===i||0===o||1===o&&n===i-1&&n===r+1?"":e.slice(n,i)},format:ht.bind(null,"\\"),parse(e){it(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;const n=e.length;let r=0,i=e.charCodeAt(0);if(1===n)return ot(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(ot(i)){if(r=1,ot(e.charCodeAt(1))){let t=2,i=t;for(;t<n&&!ot(e.charCodeAt(t));)t++;if(t<n&&t!==i){for(i=t;t<n&&ot(e.charCodeAt(t));)t++;if(t<n&&t!==i){for(i=t;t<n&&!ot(e.charCodeAt(t));)t++;t===n?r=t:t!==i&&(r=t+1)}}}}else if(lt(i)&&e.charCodeAt(1)===nt){if(n<=2)return t.root=t.dir=e,t;if(r=2,ot(e.charCodeAt(2))){if(3===n)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));let s=-1,o=r,a=-1,l=!0,u=e.length-1,h=0;for(;u>=r;--u)if(i=e.charCodeAt(u),ot(i)){if(!l){o=u+1;break}}else-1===a&&(l=!1,a=u+1),i===Ze?-1===s?s=u:1!==h&&(h=1):-1!==s&&(h=-1);return-1!==a&&(-1===s||0===h||1===h&&s===a-1&&s===o+1?t.base=t.name=e.slice(o,a):(t.name=e.slice(o,s),t.base=e.slice(o,a),t.ext=e.slice(s,a))),t.dir=o>0&&o!==r?e.slice(0,o-1):t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},dt=(()=>{if(st){const e=/\\/g;return()=>{const t=Je().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>Je()})(),gt={resolve(...e){let t="",n=!1;for(let r=e.length-1;r>=-1&&!n;r--){const i=r>=0?e[r]:dt();it(i,"path"),0!==i.length&&(t=`${i}/${t}`,n=i.charCodeAt(0)===et)}return t=ut(t,!n,"/",at),n?`/${t}`:t.length>0?t:"."},normalize(e){if(it(e,"path"),0===e.length)return".";const t=e.charCodeAt(0)===et,n=e.charCodeAt(e.length-1)===et;return 0===(e=ut(e,!t,"/",at)).length?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute:e=>(it(e,"path"),e.length>0&&e.charCodeAt(0)===et),join(...e){if(0===e.length)return".";let t;for(let n=0;n<e.length;++n){const r=e[n];it(r,"path"),r.length>0&&(void 0===t?t=r:t+=`/${r}`)}return void 0===t?".":gt.normalize(t)},relative(e,t){if(it(e,"from"),it(t,"to"),e===t)return"";if((e=gt.resolve(e))===(t=gt.resolve(t)))return"";const n=e.length,r=n-1,i=t.length-1,s=r<i?r:i;let o=-1,a=0;for(;a<s;a++){const n=e.charCodeAt(1+a);if(n!==t.charCodeAt(1+a))break;n===et&&(o=a)}if(a===s)if(i>s){if(t.charCodeAt(1+a)===et)return t.slice(1+a+1);if(0===a)return t.slice(1+a)}else r>s&&(e.charCodeAt(1+a)===et?o=a:0===a&&(o=0));let l="";for(a=1+o+1;a<=n;++a)a!==n&&e.charCodeAt(a)!==et||(l+=0===l.length?"..":"/..");return`${l}${t.slice(1+o)}`},toNamespacedPath:e=>e,dirname(e){if(it(e,"path"),0===e.length)return".";const t=e.charCodeAt(0)===et;let n=-1,r=!0;for(let t=e.length-1;t>=1;--t)if(e.charCodeAt(t)===et){if(!r){n=t;break}}else r=!1;return-1===n?t?"/":".":t&&1===n?"//":e.slice(0,n)},basename(e,t){void 0!==t&&it(t,"ext"),it(e,"path");let n,r=0,i=-1,s=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(n=e.length-1;n>=0;--n){const l=e.charCodeAt(n);if(l===et){if(!s){r=n+1;break}}else-1===a&&(s=!1,a=n+1),o>=0&&(l===t.charCodeAt(o)?-1==--o&&(i=n):(o=-1,i=a))}return r===i?i=a:-1===i&&(i=e.length),e.slice(r,i)}for(n=e.length-1;n>=0;--n)if(e.charCodeAt(n)===et){if(!s){r=n+1;break}}else-1===i&&(s=!1,i=n+1);return-1===i?"":e.slice(r,i)},extname(e){it(e,"path");let t=-1,n=0,r=-1,i=!0,s=0;for(let o=e.length-1;o>=0;--o){const a=e.charCodeAt(o);if(a!==et)-1===r&&(i=!1,r=o+1),a===Ze?-1===t?t=o:1!==s&&(s=1):-1!==t&&(s=-1);else if(!i){n=o+1;break}}return-1===t||-1===r||0===s||1===s&&t===r-1&&t===n+1?"":e.slice(t,r)},format:ht.bind(null,"/"),parse(e){it(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;const n=e.charCodeAt(0)===et;let r;n?(t.root="/",r=1):r=0;let i=-1,s=0,o=-1,a=!0,l=e.length-1,u=0;for(;l>=r;--l){const t=e.charCodeAt(l);if(t!==et)-1===o&&(a=!1,o=l+1),t===Ze?-1===i?i=l:1!==u&&(u=1):-1!==i&&(u=-1);else if(!a){s=l+1;break}}if(-1!==o){const r=0===s&&n?1:s;-1===i||0===u||1===u&&i===o-1&&i===s+1?t.base=t.name=e.slice(r,o):(t.name=e.slice(r,i),t.base=e.slice(r,o),t.ext=e.slice(i,o))}return s>0?t.dir=e.slice(0,s-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};gt.win32=ct.win32=ct,gt.posix=ct.posix=gt;st?ct.normalize:gt.normalize,st?ct.resolve:gt.resolve,st?ct.relative:gt.relative,st?ct.dirname:gt.dirname,st?ct.basename:gt.basename,st?ct.extname:gt.extname,st?ct.sep:gt.sep;const mt=/^\w[\w\d+.-]*$/,ft=/^\//,pt=/^\/\//;const bt="",_t="/",Ct=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class yt{static isUri(e){return e instanceof yt||!!e&&("string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString)}constructor(e,t,n,r,i,s=!1){"object"==typeof e?(this.scheme=e.scheme||bt,this.authority=e.authority||bt,this.path=e.path||bt,this.query=e.query||bt,this.fragment=e.fragment||bt):(this.scheme=function(e,t){return e||t?e:"file"}(e,s),this.authority=t||bt,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==_t&&(t=_t+t):t=_t}return t}(this.scheme,n||bt),this.query=r||bt,this.fragment=i||bt,function(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!mt.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!ft.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(pt.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}(this,s))}get fsPath(){return Et(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:r,query:i,fragment:s}=e;return void 0===t?t=this.scheme:null===t&&(t=bt),void 0===n?n=this.authority:null===n&&(n=bt),void 0===r?r=this.path:null===r&&(r=bt),void 0===i?i=this.query:null===i&&(i=bt),void 0===s?s=this.fragment:null===s&&(s=bt),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&s===this.fragment?this:new wt(t,n,r,i,s)}static parse(e,t=!1){const n=Ct.exec(e);return n?new wt(n[2]||bt,Mt(n[4]||bt),Mt(n[5]||bt),Mt(n[7]||bt),Mt(n[9]||bt),t):new wt(bt,bt,bt,bt,bt)}static file(e){let t=bt;if(Y&&(e=e.replace(/\\/g,_t)),e[0]===_t&&e[1]===_t){const n=e.indexOf(_t,2);-1===n?(t=e.substring(2),e=_t):(t=e.substring(2,n),e=e.substring(n)||_t)}return new wt("file",t,e,bt,bt)}static from(e,t){return new wt(e.scheme,e.authority,e.path,e.query,e.fragment,t)}static joinPath(e,...t){if(!e.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let n;return n=Y&&"file"===e.scheme?yt.file(ct.join(Et(e,!0),...t)).path:gt.join(e.path,...t),e.with({path:n})}toString(e=!1){return At(this,e)}toJSON(){return this}static revive(e){var t,n;if(e){if(e instanceof yt)return e;{const r=new wt(e);return r._formatted=null!==(t=e.external)&&void 0!==t?t:null,r._fsPath=e._sep===vt&&null!==(n=e.fsPath)&&void 0!==n?n:null,r}}return e}}const vt=Y?1:void 0;class wt extends yt{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=Et(this,!1)),this._fsPath}toString(e=!1){return e?At(this,!0):(this._formatted||(this._formatted=At(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=vt),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}const Lt={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function St(e,t,n){let r,i=-1;for(let s=0;s<e.length;s++){const o=e.charCodeAt(s);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||t&&47===o||n&&91===o||n&&93===o||n&&58===o)-1!==i&&(r+=encodeURIComponent(e.substring(i,s)),i=-1),void 0!==r&&(r+=e.charAt(s));else{void 0===r&&(r=e.substr(0,s));const t=Lt[o];void 0!==t?(-1!==i&&(r+=encodeURIComponent(e.substring(i,s)),i=-1),r+=t):-1===i&&(i=s)}}return-1!==i&&(r+=encodeURIComponent(e.substring(i))),void 0!==r?r:e}function Nt(e){let t;for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=Lt[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function Et(e,t){let n;return n=e.authority&&e.path.length>1&&"file"===e.scheme?`//${e.authority}${e.path}`:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,Y&&(n=n.replace(/\//g,"\\")),n}function At(e,t){const n=t?Nt:St;let r="",{scheme:i,authority:s,path:o,query:a,fragment:l}=e;if(i&&(r+=i,r+=":"),(s||"file"===i)&&(r+=_t,r+=_t),s){let e=s.indexOf("@");if(-1!==e){const t=s.substr(0,e);s=s.substr(e+1),e=t.lastIndexOf(":"),-1===e?r+=n(t,!1,!1):(r+=n(t.substr(0,e),!1,!1),r+=":",r+=n(t.substr(e+1),!1,!0)),r+="@"}s=s.toLowerCase(),e=s.lastIndexOf(":"),-1===e?r+=n(s,!1,!0):(r+=n(s.substr(0,e),!1,!0),r+=s.substr(e))}if(o){if(o.length>=3&&47===o.charCodeAt(0)&&58===o.charCodeAt(2)){const e=o.charCodeAt(1);e>=65&&e<=90&&(o=`/${String.fromCharCode(e+32)}:${o.substr(3)}`)}else if(o.length>=2&&58===o.charCodeAt(1)){const e=o.charCodeAt(0);e>=65&&e<=90&&(o=`${String.fromCharCode(e+32)}:${o.substr(2)}`)}r+=n(o,!0,!1)}return a&&(r+="?",r+=n(a,!1,!1)),l&&(r+="#",r+=t?l:St(l,!1,!1)),r}function Rt(e){try{return decodeURIComponent(e)}catch(t){return e.length>3?e.substr(0,3)+Rt(e.substr(3)):e}}const xt=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function Mt(e){return e.match(xt)?e.replace(xt,(e=>Rt(e))):e}class kt{constructor(e,t){this.lineNumber=e,this.column=t}with(e=this.lineNumber,t=this.column){return e===this.lineNumber&&t===this.column?this:new kt(e,t)}delta(e=0,t=0){return this.with(this.lineNumber+e,this.column+t)}equals(e){return kt.equals(this,e)}static equals(e,t){return!e&&!t||!!e&&!!t&&e.lineNumber===t.lineNumber&&e.column===t.column}isBefore(e){return kt.isBefore(this,e)}static isBefore(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<t.column}isBeforeOrEqual(e){return kt.isBeforeOrEqual(this,e)}static isBeforeOrEqual(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<=t.column}static compare(e,t){const n=0|e.lineNumber,r=0|t.lineNumber;if(n===r){return(0|e.column)-(0|t.column)}return n-r}clone(){return new kt(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(e){return new kt(e.lineNumber,e.column)}static isIPosition(e){return e&&"number"==typeof e.lineNumber&&"number"==typeof e.column}}class Tt{constructor(e,t,n,r){e>n||e===n&&t>r?(this.startLineNumber=n,this.startColumn=r,this.endLineNumber=e,this.endColumn=t):(this.startLineNumber=e,this.startColumn=t,this.endLineNumber=n,this.endColumn=r)}isEmpty(){return Tt.isEmpty(this)}static isEmpty(e){return e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn}containsPosition(e){return Tt.containsPosition(this,e)}static containsPosition(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber)&&(!(t.lineNumber===e.startLineNumber&&t.column<e.startColumn)&&!(t.lineNumber===e.endLineNumber&&t.column>e.endColumn))}static strictContainsPosition(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber)&&(!(t.lineNumber===e.startLineNumber&&t.column<=e.startColumn)&&!(t.lineNumber===e.endLineNumber&&t.column>=e.endColumn))}containsRange(e){return Tt.containsRange(this,e)}static containsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber)&&(!(t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber)&&(!(t.startLineNumber===e.startLineNumber&&t.startColumn<e.startColumn)&&!(t.endLineNumber===e.endLineNumber&&t.endColumn>e.endColumn)))}strictContainsRange(e){return Tt.strictContainsRange(this,e)}static strictContainsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber)&&(!(t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber)&&(!(t.startLineNumber===e.startLineNumber&&t.startColumn<=e.startColumn)&&!(t.endLineNumber===e.endLineNumber&&t.endColumn>=e.endColumn)))}plusRange(e){return Tt.plusRange(this,e)}static plusRange(e,t){let n,r,i,s;return t.startLineNumber<e.startLineNumber?(n=t.startLineNumber,r=t.startColumn):t.startLineNumber===e.startLineNumber?(n=t.startLineNumber,r=Math.min(t.startColumn,e.startColumn)):(n=e.startLineNumber,r=e.startColumn),t.endLineNumber>e.endLineNumber?(i=t.endLineNumber,s=t.endColumn):t.endLineNumber===e.endLineNumber?(i=t.endLineNumber,s=Math.max(t.endColumn,e.endColumn)):(i=e.endLineNumber,s=e.endColumn),new Tt(n,r,i,s)}intersectRanges(e){return Tt.intersectRanges(this,e)}static intersectRanges(e,t){let n=e.startLineNumber,r=e.startColumn,i=e.endLineNumber,s=e.endColumn;const o=t.startLineNumber,a=t.startColumn,l=t.endLineNumber,u=t.endColumn;return n<o?(n=o,r=a):n===o&&(r=Math.max(r,a)),i>l?(i=l,s=u):i===l&&(s=Math.min(s,u)),n>i||n===i&&r>s?null:new Tt(n,r,i,s)}equalsRange(e){return Tt.equalsRange(this,e)}static equalsRange(e,t){return!e&&!t||!!e&&!!t&&e.startLineNumber===t.startLineNumber&&e.startColumn===t.startColumn&&e.endLineNumber===t.endLineNumber&&e.endColumn===t.endColumn}getEndPosition(){return Tt.getEndPosition(this)}static getEndPosition(e){return new kt(e.endLineNumber,e.endColumn)}getStartPosition(){return Tt.getStartPosition(this)}static getStartPosition(e){return new kt(e.startLineNumber,e.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(e,t){return new Tt(this.startLineNumber,this.startColumn,e,t)}setStartPosition(e,t){return new Tt(e,t,this.endLineNumber,this.endColumn)}collapseToStart(){return Tt.collapseToStart(this)}static collapseToStart(e){return new Tt(e.startLineNumber,e.startColumn,e.startLineNumber,e.startColumn)}collapseToEnd(){return Tt.collapseToEnd(this)}static collapseToEnd(e){return new Tt(e.endLineNumber,e.endColumn,e.endLineNumber,e.endColumn)}delta(e){return new Tt(this.startLineNumber+e,this.startColumn,this.endLineNumber+e,this.endColumn)}static fromPositions(e,t=e){return new Tt(e.lineNumber,e.column,t.lineNumber,t.column)}static lift(e){return e?new Tt(e.startLineNumber,e.startColumn,e.endLineNumber,e.endColumn):null}static isIRange(e){return e&&"number"==typeof e.startLineNumber&&"number"==typeof e.startColumn&&"number"==typeof e.endLineNumber&&"number"==typeof e.endColumn}static areIntersectingOrTouching(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<t.startColumn)&&!(t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<e.startColumn)}static areIntersecting(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<=t.startColumn)&&!(t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<=e.startColumn)}static compareRangesUsingStarts(e,t){if(e&&t){const n=0|e.startLineNumber,r=0|t.startLineNumber;if(n===r){const n=0|e.startColumn,r=0|t.startColumn;if(n===r){const n=0|e.endLineNumber,r=0|t.endLineNumber;if(n===r){return(0|e.endColumn)-(0|t.endColumn)}return n-r}return n-r}return n-r}return(e?1:0)-(t?1:0)}static compareRangesUsingEnds(e,t){return e.endLineNumber===t.endLineNumber?e.endColumn===t.endColumn?e.startLineNumber===t.startLineNumber?e.startColumn-t.startColumn:e.startLineNumber-t.startLineNumber:e.endColumn-t.endColumn:e.endLineNumber-t.endLineNumber}static spansMultipleLines(e){return e.endLineNumber>e.startLineNumber}toJSON(){return this}}var Ot;!function(e){e.isLessThan=function(e){return e<0},e.isGreaterThan=function(e){return e>0},e.isNeitherLessOrGreaterThan=function(e){return 0===e},e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0}(Ot||(Ot={}));class It{constructor(e){this.iterate=e}toArray(){const e=[];return this.iterate((t=>(e.push(t),!0))),e}filter(e){return new It((t=>this.iterate((n=>!e(n)||t(n)))))}map(e){return new It((t=>this.iterate((n=>t(e(n))))))}findLast(e){let t;return this.iterate((n=>(e(n)&&(t=n),!0))),t}findLastMaxBy(e){let t,n=!0;return this.iterate((r=>((n||Ot.isGreaterThan(e(r,t)))&&(n=!1,t=r),!0))),t}}function Dt(e){return e<0?0:e>255?255:0|e}function Pt(e){return e<0?0:e>4294967295?4294967295:0|e}It.empty=new It((e=>{}));class Ft{constructor(e){this.values=e,this.prefixSum=new Uint32Array(e.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(e,t){e=Pt(e);const n=this.values,r=this.prefixSum,i=t.length;return 0!==i&&(this.values=new Uint32Array(n.length+i),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e),e+i),this.values.set(t,e),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}setValue(e,t){return e=Pt(e),t=Pt(t),this.values[e]!==t&&(this.values[e]=t,e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),!0)}removeValues(e,t){e=Pt(e),t=Pt(t);const n=this.values,r=this.prefixSum;if(e>=n.length)return!1;const i=n.length-e;return t>=i&&(t=i),0!==t&&(this.values=new Uint32Array(n.length-t),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e+t),e),this.prefixSum=new Uint32Array(this.values.length),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return 0===this.values.length?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(e){return e<0?0:(e=Pt(e),this._getPrefixSum(e))}_getPrefixSum(e){if(e<=this.prefixSumValidIndex[0])return this.prefixSum[e];let t=this.prefixSumValidIndex[0]+1;0===t&&(this.prefixSum[0]=this.values[0],t++),e>=this.values.length&&(e=this.values.length-1);for(let n=t;n<=e;n++)this.prefixSum[n]=this.prefixSum[n-1]+this.values[n];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],e),this.prefixSum[e]}getIndexOf(e){e=Math.floor(e),this.getTotalSum();let t=0,n=this.values.length-1,r=0,i=0,s=0;for(;t<=n;)if(r=t+(n-t)/2|0,i=this.prefixSum[r],s=i-this.values[r],e<s)n=r-1;else{if(!(e>=i))break;t=r+1}return new Kt(r,e-s)}}class Kt{constructor(e,t){this.index=e,this.remainder=t,this._prefixSumIndexOfResultBrand=void 0,this.index=e,this.remainder=t}}class Vt{constructor(e,t,n,r){this._uri=e,this._lines=t,this._eol=n,this._versionId=r,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return null===this._cachedTextValue&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(e){e.eol&&e.eol!==this._eol&&(this._eol=e.eol,this._lineStarts=null);const t=e.changes;for(const e of t)this._acceptDeleteRange(e.range),this._acceptInsertText(new kt(e.range.startLineNumber,e.range.startColumn),e.text);this._versionId=e.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){const e=this._eol.length,t=this._lines.length,n=new Uint32Array(t);for(let r=0;r<t;r++)n[r]=this._lines[r].length+e;this._lineStarts=new Ft(n)}}_setLineText(e,t){this._lines[e]=t,this._lineStarts&&this._lineStarts.setValue(e,this._lines[e].length+this._eol.length)}_acceptDeleteRange(e){if(e.startLineNumber!==e.endLineNumber)this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.endLineNumber-1].substring(e.endColumn-1)),this._lines.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber);else{if(e.startColumn===e.endColumn)return;this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.startLineNumber-1].substring(e.endColumn-1))}}_acceptInsertText(e,t){if(0===t.length)return;const n=t.split(/\r\n|\r|\n/);if(1===n.length)return void this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]+this._lines[e.lineNumber-1].substring(e.column-1));n[n.length-1]+=this._lines[e.lineNumber-1].substring(e.column-1),this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]);const r=new Uint32Array(n.length-1);for(let t=1;t<n.length;t++)this._lines.splice(e.lineNumber+t-1,0,n[t]),r[t-1]=n[t].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(e.lineNumber,r)}}const Bt=function(e=""){let t="(-?\\d*\\.\\d\\w*)|([^";for(const n of"`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?")e.indexOf(n)>=0||(t+="\\"+n);return t+="\\s]+)",new RegExp(t,"g")}();const qt=new w;function Ut(e,t,n,r,i){if(i||(i=h.first(qt)),n.length>i.maxLen){let s=e-i.maxLen/2;return s<0?s=0:r+=s,Ut(e,t,n=n.substring(s,e+i.maxLen/2),r,i)}const s=Date.now(),o=e-1-r;let a=-1,l=null;for(let e=1;!(Date.now()-s>=i.timeBudget);e++){const r=o-i.windowSize*e;t.lastIndex=Math.max(0,r);const s=Wt(t,n,o,a);if(!s&&l)break;if(l=s,r<=0)break;a=r}if(l){const e={word:l[0],startColumn:r+1+l.index,endColumn:r+1+l.index+l[0].length};return t.lastIndex=0,e}return null}function Wt(e,t,n,r){let i;for(;i=e.exec(t);){const t=i.index||0;if(t<=n&&e.lastIndex>=n)return i;if(r>0&&t>r)return null}return null}qt.unshift({maxLen:1e3,windowSize:15,timeBudget:150});class Ht{constructor(e){const t=Dt(e);this._defaultValue=t,this._asciiMap=Ht._createAsciiMap(t),this._map=new Map}static _createAsciiMap(e){const t=new Uint8Array(256);return t.fill(e),t}set(e,t){const n=Dt(t);e>=0&&e<256?this._asciiMap[e]=n:this._map.set(e,n)}get(e){return e>=0&&e<256?this._asciiMap[e]:this._map.get(e)||this._defaultValue}clear(){this._asciiMap.fill(this._defaultValue),this._map.clear()}}class $t{constructor(e,t,n){const r=new Uint8Array(e*t);for(let i=0,s=e*t;i<s;i++)r[i]=n;this._data=r,this.rows=e,this.cols=t}get(e,t){return this._data[e*this.cols+t]}set(e,t,n){this._data[e*this.cols+t]=n}}class zt{constructor(e){let t=0,n=0;for(let r=0,i=e.length;r<i;r++){const[i,s,o]=e[r];s>t&&(t=s),i>n&&(n=i),o>n&&(n=o)}t++,n++;const r=new $t(n,t,0);for(let t=0,n=e.length;t<n;t++){const[n,i,s]=e[t];r.set(n,i,s)}this._states=r,this._maxCharCode=t}nextState(e,t){return t<0||t>=this._maxCharCode?0:this._states.get(e,t)}}let jt=null;let Gt=null;class Yt{static _createLink(e,t,n,r,i){let s=i-1;do{const n=t.charCodeAt(s);if(2!==e.get(n))break;s--}while(s>r);if(r>0){const e=t.charCodeAt(r-1),n=t.charCodeAt(s);(40===e&&41===n||91===e&&93===n||123===e&&125===n)&&s--}return{range:{startLineNumber:n,startColumn:r+1,endLineNumber:n,endColumn:s+2},url:t.substring(r,s+1)}}static computeLinks(e,t=function(){return null===jt&&(jt=new zt([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),jt}()){const n=function(){if(null===Gt){Gt=new Ht(0);const e=" \t<>'\"、。｡､，．：；‘〈「『〔（［｛｢｣｝］）〕』」〉’｀～…";for(let t=0;t<e.length;t++)Gt.set(e.charCodeAt(t),1);const t=".,;:";for(let e=0;e<t.length;e++)Gt.set(t.charCodeAt(e),2)}return Gt}(),r=[];for(let i=1,s=e.getLineCount();i<=s;i++){const s=e.getLineContent(i),o=s.length;let a=0,l=0,u=0,h=1,c=!1,d=!1,g=!1,m=!1;for(;a<o;){let e=!1;const o=s.charCodeAt(a);if(13===h){let t;switch(o){case 40:c=!0,t=0;break;case 41:t=c?0:1;break;case 91:g=!0,d=!0,t=0;break;case 93:g=!1,t=d?0:1;break;case 123:m=!0,t=0;break;case 125:t=m?0:1;break;case 39:case 34:case 96:t=u===o?1:39===u||34===u||96===u?0:1;break;case 42:t=42===u?1:0;break;case 124:t=124===u?1:0;break;case 32:t=g?0:1;break;default:t=n.get(o)}1===t&&(r.push(Yt._createLink(n,s,i,l,a)),e=!0)}else if(12===h){let t;91===o?(d=!0,t=0):t=n.get(o),1===t?e=!0:h=13}else h=t.nextState(h,o),0===h&&(e=!0);e&&(h=1,c=!1,d=!1,m=!1,l=a+1,u=o),a++}13===h&&r.push(Yt._createLink(n,s,i,l,o))}return r}}class Jt{constructor(){this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}navigateValueSet(e,t,n,r,i){if(e&&t){const n=this.doNavigateValueSet(t,i);if(n)return{range:e,value:n}}if(n&&r){const e=this.doNavigateValueSet(r,i);if(e)return{range:n,value:e}}return null}doNavigateValueSet(e,t){const n=this.numberReplace(e,t);return null!==n?n:this.textReplace(e,t)}numberReplace(e,t){const n=Math.pow(10,e.length-(e.lastIndexOf(".")+1));let r=Number(e);const i=parseFloat(e);return isNaN(r)||isNaN(i)||r!==i?null:0!==r||t?(r=Math.floor(r*n),r+=t?n:-n,String(r/n)):null}textReplace(e,t){return this.valueSetsReplace(this._defaultValueSet,e,t)}valueSetsReplace(e,t,n){let r=null;for(let i=0,s=e.length;null===r&&i<s;i++)r=this.valueSetReplace(e[i],t,n);return r}valueSetReplace(e,t,n){let r=e.indexOf(t);return r>=0?(r+=n?1:-1,r<0?r=e.length-1:r%=e.length,e[r]):null}}Jt.INSTANCE=new Jt;const Qt=Object.freeze((function(e,t){const n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}}));var Xt;!function(e){e.isCancellationToken=function(t){return t===e.None||t===e.Cancelled||(t instanceof Zt||!(!t||"object"!=typeof t)&&("boolean"==typeof t.isCancellationRequested&&"function"==typeof t.onCancellationRequested))},e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:ne.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Qt})}(Xt||(Xt={}));class Zt{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?Qt:(this._emitter||(this._emitter=new ae),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}}class en{constructor(e){this._token=void 0,this._parentListener=void 0,this._parentListener=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this._token||(this._token=new Zt),this._token}cancel(){this._token?this._token instanceof Zt&&this._token.cancel():this._token=Xt.Cancelled}dispose(e=!1){var t;e&&this.cancel(),null===(t=this._parentListener)||void 0===t||t.dispose(),this._token?this._token instanceof Zt&&this._token.dispose():this._token=Xt.None}}class tn{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(e,t){this._keyCodeToStr[e]=t,this._strToKeyCode[t.toLowerCase()]=e}keyCodeToStr(e){return this._keyCodeToStr[e]}strToKeyCode(e){return this._strToKeyCode[e.toLowerCase()]||0}}const nn=new tn,rn=new tn,sn=new tn,on=new Array(230),an={},ln=[],un=Object.create(null),hn=Object.create(null),cn=[],dn=[];for(let e=0;e<=193;e++)cn[e]=-1;for(let e=0;e<=132;e++)dn[e]=-1;var gn;!function(){const e="",t=[[1,0,"None",0,"unknown",0,"VK_UNKNOWN",e,e],[1,1,"Hyper",0,e,0,e,e,e],[1,2,"Super",0,e,0,e,e,e],[1,3,"Fn",0,e,0,e,e,e],[1,4,"FnLock",0,e,0,e,e,e],[1,5,"Suspend",0,e,0,e,e,e],[1,6,"Resume",0,e,0,e,e,e],[1,7,"Turbo",0,e,0,e,e,e],[1,8,"Sleep",0,e,0,"VK_SLEEP",e,e],[1,9,"WakeUp",0,e,0,e,e,e],[0,10,"KeyA",31,"A",65,"VK_A",e,e],[0,11,"KeyB",32,"B",66,"VK_B",e,e],[0,12,"KeyC",33,"C",67,"VK_C",e,e],[0,13,"KeyD",34,"D",68,"VK_D",e,e],[0,14,"KeyE",35,"E",69,"VK_E",e,e],[0,15,"KeyF",36,"F",70,"VK_F",e,e],[0,16,"KeyG",37,"G",71,"VK_G",e,e],[0,17,"KeyH",38,"H",72,"VK_H",e,e],[0,18,"KeyI",39,"I",73,"VK_I",e,e],[0,19,"KeyJ",40,"J",74,"VK_J",e,e],[0,20,"KeyK",41,"K",75,"VK_K",e,e],[0,21,"KeyL",42,"L",76,"VK_L",e,e],[0,22,"KeyM",43,"M",77,"VK_M",e,e],[0,23,"KeyN",44,"N",78,"VK_N",e,e],[0,24,"KeyO",45,"O",79,"VK_O",e,e],[0,25,"KeyP",46,"P",80,"VK_P",e,e],[0,26,"KeyQ",47,"Q",81,"VK_Q",e,e],[0,27,"KeyR",48,"R",82,"VK_R",e,e],[0,28,"KeyS",49,"S",83,"VK_S",e,e],[0,29,"KeyT",50,"T",84,"VK_T",e,e],[0,30,"KeyU",51,"U",85,"VK_U",e,e],[0,31,"KeyV",52,"V",86,"VK_V",e,e],[0,32,"KeyW",53,"W",87,"VK_W",e,e],[0,33,"KeyX",54,"X",88,"VK_X",e,e],[0,34,"KeyY",55,"Y",89,"VK_Y",e,e],[0,35,"KeyZ",56,"Z",90,"VK_Z",e,e],[0,36,"Digit1",22,"1",49,"VK_1",e,e],[0,37,"Digit2",23,"2",50,"VK_2",e,e],[0,38,"Digit3",24,"3",51,"VK_3",e,e],[0,39,"Digit4",25,"4",52,"VK_4",e,e],[0,40,"Digit5",26,"5",53,"VK_5",e,e],[0,41,"Digit6",27,"6",54,"VK_6",e,e],[0,42,"Digit7",28,"7",55,"VK_7",e,e],[0,43,"Digit8",29,"8",56,"VK_8",e,e],[0,44,"Digit9",30,"9",57,"VK_9",e,e],[0,45,"Digit0",21,"0",48,"VK_0",e,e],[1,46,"Enter",3,"Enter",13,"VK_RETURN",e,e],[1,47,"Escape",9,"Escape",27,"VK_ESCAPE",e,e],[1,48,"Backspace",1,"Backspace",8,"VK_BACK",e,e],[1,49,"Tab",2,"Tab",9,"VK_TAB",e,e],[1,50,"Space",10,"Space",32,"VK_SPACE",e,e],[0,51,"Minus",88,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[0,52,"Equal",86,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[0,53,"BracketLeft",92,"[",219,"VK_OEM_4","[","OEM_4"],[0,54,"BracketRight",94,"]",221,"VK_OEM_6","]","OEM_6"],[0,55,"Backslash",93,"\\",220,"VK_OEM_5","\\","OEM_5"],[0,56,"IntlHash",0,e,0,e,e,e],[0,57,"Semicolon",85,";",186,"VK_OEM_1",";","OEM_1"],[0,58,"Quote",95,"'",222,"VK_OEM_7","'","OEM_7"],[0,59,"Backquote",91,"`",192,"VK_OEM_3","`","OEM_3"],[0,60,"Comma",87,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[0,61,"Period",89,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[0,62,"Slash",90,"/",191,"VK_OEM_2","/","OEM_2"],[1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL",e,e],[1,64,"F1",59,"F1",112,"VK_F1",e,e],[1,65,"F2",60,"F2",113,"VK_F2",e,e],[1,66,"F3",61,"F3",114,"VK_F3",e,e],[1,67,"F4",62,"F4",115,"VK_F4",e,e],[1,68,"F5",63,"F5",116,"VK_F5",e,e],[1,69,"F6",64,"F6",117,"VK_F6",e,e],[1,70,"F7",65,"F7",118,"VK_F7",e,e],[1,71,"F8",66,"F8",119,"VK_F8",e,e],[1,72,"F9",67,"F9",120,"VK_F9",e,e],[1,73,"F10",68,"F10",121,"VK_F10",e,e],[1,74,"F11",69,"F11",122,"VK_F11",e,e],[1,75,"F12",70,"F12",123,"VK_F12",e,e],[1,76,"PrintScreen",0,e,0,e,e,e],[1,77,"ScrollLock",84,"ScrollLock",145,"VK_SCROLL",e,e],[1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE",e,e],[1,79,"Insert",19,"Insert",45,"VK_INSERT",e,e],[1,80,"Home",14,"Home",36,"VK_HOME",e,e],[1,81,"PageUp",11,"PageUp",33,"VK_PRIOR",e,e],[1,82,"Delete",20,"Delete",46,"VK_DELETE",e,e],[1,83,"End",13,"End",35,"VK_END",e,e],[1,84,"PageDown",12,"PageDown",34,"VK_NEXT",e,e],[1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",e],[1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",e],[1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",e],[1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",e],[1,89,"NumLock",83,"NumLock",144,"VK_NUMLOCK",e,e],[1,90,"NumpadDivide",113,"NumPad_Divide",111,"VK_DIVIDE",e,e],[1,91,"NumpadMultiply",108,"NumPad_Multiply",106,"VK_MULTIPLY",e,e],[1,92,"NumpadSubtract",111,"NumPad_Subtract",109,"VK_SUBTRACT",e,e],[1,93,"NumpadAdd",109,"NumPad_Add",107,"VK_ADD",e,e],[1,94,"NumpadEnter",3,e,0,e,e,e],[1,95,"Numpad1",99,"NumPad1",97,"VK_NUMPAD1",e,e],[1,96,"Numpad2",100,"NumPad2",98,"VK_NUMPAD2",e,e],[1,97,"Numpad3",101,"NumPad3",99,"VK_NUMPAD3",e,e],[1,98,"Numpad4",102,"NumPad4",100,"VK_NUMPAD4",e,e],[1,99,"Numpad5",103,"NumPad5",101,"VK_NUMPAD5",e,e],[1,100,"Numpad6",104,"NumPad6",102,"VK_NUMPAD6",e,e],[1,101,"Numpad7",105,"NumPad7",103,"VK_NUMPAD7",e,e],[1,102,"Numpad8",106,"NumPad8",104,"VK_NUMPAD8",e,e],[1,103,"Numpad9",107,"NumPad9",105,"VK_NUMPAD9",e,e],[1,104,"Numpad0",98,"NumPad0",96,"VK_NUMPAD0",e,e],[1,105,"NumpadDecimal",112,"NumPad_Decimal",110,"VK_DECIMAL",e,e],[0,106,"IntlBackslash",97,"OEM_102",226,"VK_OEM_102",e,e],[1,107,"ContextMenu",58,"ContextMenu",93,e,e,e],[1,108,"Power",0,e,0,e,e,e],[1,109,"NumpadEqual",0,e,0,e,e,e],[1,110,"F13",71,"F13",124,"VK_F13",e,e],[1,111,"F14",72,"F14",125,"VK_F14",e,e],[1,112,"F15",73,"F15",126,"VK_F15",e,e],[1,113,"F16",74,"F16",127,"VK_F16",e,e],[1,114,"F17",75,"F17",128,"VK_F17",e,e],[1,115,"F18",76,"F18",129,"VK_F18",e,e],[1,116,"F19",77,"F19",130,"VK_F19",e,e],[1,117,"F20",78,"F20",131,"VK_F20",e,e],[1,118,"F21",79,"F21",132,"VK_F21",e,e],[1,119,"F22",80,"F22",133,"VK_F22",e,e],[1,120,"F23",81,"F23",134,"VK_F23",e,e],[1,121,"F24",82,"F24",135,"VK_F24",e,e],[1,122,"Open",0,e,0,e,e,e],[1,123,"Help",0,e,0,e,e,e],[1,124,"Select",0,e,0,e,e,e],[1,125,"Again",0,e,0,e,e,e],[1,126,"Undo",0,e,0,e,e,e],[1,127,"Cut",0,e,0,e,e,e],[1,128,"Copy",0,e,0,e,e,e],[1,129,"Paste",0,e,0,e,e,e],[1,130,"Find",0,e,0,e,e,e],[1,131,"AudioVolumeMute",117,"AudioVolumeMute",173,"VK_VOLUME_MUTE",e,e],[1,132,"AudioVolumeUp",118,"AudioVolumeUp",175,"VK_VOLUME_UP",e,e],[1,133,"AudioVolumeDown",119,"AudioVolumeDown",174,"VK_VOLUME_DOWN",e,e],[1,134,"NumpadComma",110,"NumPad_Separator",108,"VK_SEPARATOR",e,e],[0,135,"IntlRo",115,"ABNT_C1",193,"VK_ABNT_C1",e,e],[1,136,"KanaMode",0,e,0,e,e,e],[0,137,"IntlYen",0,e,0,e,e,e],[1,138,"Convert",0,e,0,e,e,e],[1,139,"NonConvert",0,e,0,e,e,e],[1,140,"Lang1",0,e,0,e,e,e],[1,141,"Lang2",0,e,0,e,e,e],[1,142,"Lang3",0,e,0,e,e,e],[1,143,"Lang4",0,e,0,e,e,e],[1,144,"Lang5",0,e,0,e,e,e],[1,145,"Abort",0,e,0,e,e,e],[1,146,"Props",0,e,0,e,e,e],[1,147,"NumpadParenLeft",0,e,0,e,e,e],[1,148,"NumpadParenRight",0,e,0,e,e,e],[1,149,"NumpadBackspace",0,e,0,e,e,e],[1,150,"NumpadMemoryStore",0,e,0,e,e,e],[1,151,"NumpadMemoryRecall",0,e,0,e,e,e],[1,152,"NumpadMemoryClear",0,e,0,e,e,e],[1,153,"NumpadMemoryAdd",0,e,0,e,e,e],[1,154,"NumpadMemorySubtract",0,e,0,e,e,e],[1,155,"NumpadClear",131,"Clear",12,"VK_CLEAR",e,e],[1,156,"NumpadClearEntry",0,e,0,e,e,e],[1,0,e,5,"Ctrl",17,"VK_CONTROL",e,e],[1,0,e,4,"Shift",16,"VK_SHIFT",e,e],[1,0,e,6,"Alt",18,"VK_MENU",e,e],[1,0,e,57,"Meta",91,"VK_COMMAND",e,e],[1,157,"ControlLeft",5,e,0,"VK_LCONTROL",e,e],[1,158,"ShiftLeft",4,e,0,"VK_LSHIFT",e,e],[1,159,"AltLeft",6,e,0,"VK_LMENU",e,e],[1,160,"MetaLeft",57,e,0,"VK_LWIN",e,e],[1,161,"ControlRight",5,e,0,"VK_RCONTROL",e,e],[1,162,"ShiftRight",4,e,0,"VK_RSHIFT",e,e],[1,163,"AltRight",6,e,0,"VK_RMENU",e,e],[1,164,"MetaRight",57,e,0,"VK_RWIN",e,e],[1,165,"BrightnessUp",0,e,0,e,e,e],[1,166,"BrightnessDown",0,e,0,e,e,e],[1,167,"MediaPlay",0,e,0,e,e,e],[1,168,"MediaRecord",0,e,0,e,e,e],[1,169,"MediaFastForward",0,e,0,e,e,e],[1,170,"MediaRewind",0,e,0,e,e,e],[1,171,"MediaTrackNext",124,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK",e,e],[1,172,"MediaTrackPrevious",125,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK",e,e],[1,173,"MediaStop",126,"MediaStop",178,"VK_MEDIA_STOP",e,e],[1,174,"Eject",0,e,0,e,e,e],[1,175,"MediaPlayPause",127,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE",e,e],[1,176,"MediaSelect",128,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT",e,e],[1,177,"LaunchMail",129,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL",e,e],[1,178,"LaunchApp2",130,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2",e,e],[1,179,"LaunchApp1",0,e,0,"VK_MEDIA_LAUNCH_APP1",e,e],[1,180,"SelectTask",0,e,0,e,e,e],[1,181,"LaunchScreenSaver",0,e,0,e,e,e],[1,182,"BrowserSearch",120,"BrowserSearch",170,"VK_BROWSER_SEARCH",e,e],[1,183,"BrowserHome",121,"BrowserHome",172,"VK_BROWSER_HOME",e,e],[1,184,"BrowserBack",122,"BrowserBack",166,"VK_BROWSER_BACK",e,e],[1,185,"BrowserForward",123,"BrowserForward",167,"VK_BROWSER_FORWARD",e,e],[1,186,"BrowserStop",0,e,0,"VK_BROWSER_STOP",e,e],[1,187,"BrowserRefresh",0,e,0,"VK_BROWSER_REFRESH",e,e],[1,188,"BrowserFavorites",0,e,0,"VK_BROWSER_FAVORITES",e,e],[1,189,"ZoomToggle",0,e,0,e,e,e],[1,190,"MailReply",0,e,0,e,e,e],[1,191,"MailForward",0,e,0,e,e,e],[1,192,"MailSend",0,e,0,e,e,e],[1,0,e,114,"KeyInComposition",229,e,e,e],[1,0,e,116,"ABNT_C2",194,"VK_ABNT_C2",e,e],[1,0,e,96,"OEM_8",223,"VK_OEM_8",e,e],[1,0,e,0,e,0,"VK_KANA",e,e],[1,0,e,0,e,0,"VK_HANGUL",e,e],[1,0,e,0,e,0,"VK_JUNJA",e,e],[1,0,e,0,e,0,"VK_FINAL",e,e],[1,0,e,0,e,0,"VK_HANJA",e,e],[1,0,e,0,e,0,"VK_KANJI",e,e],[1,0,e,0,e,0,"VK_CONVERT",e,e],[1,0,e,0,e,0,"VK_NONCONVERT",e,e],[1,0,e,0,e,0,"VK_ACCEPT",e,e],[1,0,e,0,e,0,"VK_MODECHANGE",e,e],[1,0,e,0,e,0,"VK_SELECT",e,e],[1,0,e,0,e,0,"VK_PRINT",e,e],[1,0,e,0,e,0,"VK_EXECUTE",e,e],[1,0,e,0,e,0,"VK_SNAPSHOT",e,e],[1,0,e,0,e,0,"VK_HELP",e,e],[1,0,e,0,e,0,"VK_APPS",e,e],[1,0,e,0,e,0,"VK_PROCESSKEY",e,e],[1,0,e,0,e,0,"VK_PACKET",e,e],[1,0,e,0,e,0,"VK_DBE_SBCSCHAR",e,e],[1,0,e,0,e,0,"VK_DBE_DBCSCHAR",e,e],[1,0,e,0,e,0,"VK_ATTN",e,e],[1,0,e,0,e,0,"VK_CRSEL",e,e],[1,0,e,0,e,0,"VK_EXSEL",e,e],[1,0,e,0,e,0,"VK_EREOF",e,e],[1,0,e,0,e,0,"VK_PLAY",e,e],[1,0,e,0,e,0,"VK_ZOOM",e,e],[1,0,e,0,e,0,"VK_NONAME",e,e],[1,0,e,0,e,0,"VK_PA1",e,e],[1,0,e,0,e,0,"VK_OEM_CLEAR",e,e]],n=[],r=[];for(const e of t){const[t,i,s,o,a,l,u,h,c]=e;if(r[i]||(r[i]=!0,ln[i]=s,un[s]=i,hn[s.toLowerCase()]=i,t&&(cn[i]=o,0!==o&&3!==o&&5!==o&&4!==o&&6!==o&&57!==o&&(dn[o]=i))),!n[o]){if(n[o]=!0,!a)throw new Error(`String representation missing for key code ${o} around scan code ${s}`);nn.define(o,a),rn.define(o,h||a),sn.define(o,c||h||a)}l&&(on[l]=o),u&&(an[u]=o)}dn[3]=46}(),function(e){e.toString=function(e){return nn.keyCodeToStr(e)},e.fromString=function(e){return nn.strToKeyCode(e)},e.toUserSettingsUS=function(e){return rn.keyCodeToStr(e)},e.toUserSettingsGeneral=function(e){return sn.keyCodeToStr(e)},e.fromUserSettings=function(e){return rn.strToKeyCode(e)||sn.strToKeyCode(e)},e.toElectronAccelerator=function(e){if(e>=98&&e<=113)return null;switch(e){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return nn.keyCodeToStr(e)}}(gn||(gn={}));class mn extends Tt{constructor(e,t,n,r){super(e,t,n,r),this.selectionStartLineNumber=e,this.selectionStartColumn=t,this.positionLineNumber=n,this.positionColumn=r}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(e){return mn.selectionsEqual(this,e)}static selectionsEqual(e,t){return e.selectionStartLineNumber===t.selectionStartLineNumber&&e.selectionStartColumn===t.selectionStartColumn&&e.positionLineNumber===t.positionLineNumber&&e.positionColumn===t.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(e,t){return 0===this.getDirection()?new mn(this.startLineNumber,this.startColumn,e,t):new mn(e,t,this.startLineNumber,this.startColumn)}getPosition(){return new kt(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new kt(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(e,t){return 0===this.getDirection()?new mn(e,t,this.endLineNumber,this.endColumn):new mn(this.endLineNumber,this.endColumn,e,t)}static fromPositions(e,t=e){return new mn(e.lineNumber,e.column,t.lineNumber,t.column)}static fromRange(e,t){return 0===t?new mn(e.startLineNumber,e.startColumn,e.endLineNumber,e.endColumn):new mn(e.endLineNumber,e.endColumn,e.startLineNumber,e.startColumn)}static liftSelection(e){return new mn(e.selectionStartLineNumber,e.selectionStartColumn,e.positionLineNumber,e.positionColumn)}static selectionsArrEqual(e,t){if(e&&!t||!e&&t)return!1;if(!e&&!t)return!0;if(e.length!==t.length)return!1;for(let n=0,r=e.length;n<r;n++)if(!this.selectionsEqual(e[n],t[n]))return!1;return!0}static isISelection(e){return e&&"number"==typeof e.selectionStartLineNumber&&"number"==typeof e.selectionStartColumn&&"number"==typeof e.positionLineNumber&&"number"==typeof e.positionColumn}static createWithDirection(e,t,n,r,i){return 0===i?new mn(e,t,n,r):new mn(n,r,e,t)}}function fn(e){return"string"==typeof e}const pn=Object.create(null);function bn(e,t){if(fn(t)){const n=pn[t];if(void 0===n)throw new Error(`${e} references an unknown codicon: ${t}`);t=n}return pn[e]=t,{id:e}}const _n={add:bn("add",6e4),plus:bn("plus",6e4),gistNew:bn("gist-new",6e4),repoCreate:bn("repo-create",6e4),lightbulb:bn("lightbulb",60001),lightBulb:bn("light-bulb",60001),repo:bn("repo",60002),repoDelete:bn("repo-delete",60002),gistFork:bn("gist-fork",60003),repoForked:bn("repo-forked",60003),gitPullRequest:bn("git-pull-request",60004),gitPullRequestAbandoned:bn("git-pull-request-abandoned",60004),recordKeys:bn("record-keys",60005),keyboard:bn("keyboard",60005),tag:bn("tag",60006),tagAdd:bn("tag-add",60006),tagRemove:bn("tag-remove",60006),person:bn("person",60007),personFollow:bn("person-follow",60007),personOutline:bn("person-outline",60007),personFilled:bn("person-filled",60007),gitBranch:bn("git-branch",60008),gitBranchCreate:bn("git-branch-create",60008),gitBranchDelete:bn("git-branch-delete",60008),sourceControl:bn("source-control",60008),mirror:bn("mirror",60009),mirrorPublic:bn("mirror-public",60009),star:bn("star",60010),starAdd:bn("star-add",60010),starDelete:bn("star-delete",60010),starEmpty:bn("star-empty",60010),comment:bn("comment",60011),commentAdd:bn("comment-add",60011),alert:bn("alert",60012),warning:bn("warning",60012),search:bn("search",60013),searchSave:bn("search-save",60013),logOut:bn("log-out",60014),signOut:bn("sign-out",60014),logIn:bn("log-in",60015),signIn:bn("sign-in",60015),eye:bn("eye",60016),eyeUnwatch:bn("eye-unwatch",60016),eyeWatch:bn("eye-watch",60016),circleFilled:bn("circle-filled",60017),primitiveDot:bn("primitive-dot",60017),closeDirty:bn("close-dirty",60017),debugBreakpoint:bn("debug-breakpoint",60017),debugBreakpointDisabled:bn("debug-breakpoint-disabled",60017),debugHint:bn("debug-hint",60017),primitiveSquare:bn("primitive-square",60018),edit:bn("edit",60019),pencil:bn("pencil",60019),info:bn("info",60020),issueOpened:bn("issue-opened",60020),gistPrivate:bn("gist-private",60021),gitForkPrivate:bn("git-fork-private",60021),lock:bn("lock",60021),mirrorPrivate:bn("mirror-private",60021),close:bn("close",60022),removeClose:bn("remove-close",60022),x:bn("x",60022),repoSync:bn("repo-sync",60023),sync:bn("sync",60023),clone:bn("clone",60024),desktopDownload:bn("desktop-download",60024),beaker:bn("beaker",60025),microscope:bn("microscope",60025),vm:bn("vm",60026),deviceDesktop:bn("device-desktop",60026),file:bn("file",60027),fileText:bn("file-text",60027),more:bn("more",60028),ellipsis:bn("ellipsis",60028),kebabHorizontal:bn("kebab-horizontal",60028),mailReply:bn("mail-reply",60029),reply:bn("reply",60029),organization:bn("organization",60030),organizationFilled:bn("organization-filled",60030),organizationOutline:bn("organization-outline",60030),newFile:bn("new-file",60031),fileAdd:bn("file-add",60031),newFolder:bn("new-folder",60032),fileDirectoryCreate:bn("file-directory-create",60032),trash:bn("trash",60033),trashcan:bn("trashcan",60033),history:bn("history",60034),clock:bn("clock",60034),folder:bn("folder",60035),fileDirectory:bn("file-directory",60035),symbolFolder:bn("symbol-folder",60035),logoGithub:bn("logo-github",60036),markGithub:bn("mark-github",60036),github:bn("github",60036),terminal:bn("terminal",60037),console:bn("console",60037),repl:bn("repl",60037),zap:bn("zap",60038),symbolEvent:bn("symbol-event",60038),error:bn("error",60039),stop:bn("stop",60039),variable:bn("variable",60040),symbolVariable:bn("symbol-variable",60040),array:bn("array",60042),symbolArray:bn("symbol-array",60042),symbolModule:bn("symbol-module",60043),symbolPackage:bn("symbol-package",60043),symbolNamespace:bn("symbol-namespace",60043),symbolObject:bn("symbol-object",60043),symbolMethod:bn("symbol-method",60044),symbolFunction:bn("symbol-function",60044),symbolConstructor:bn("symbol-constructor",60044),symbolBoolean:bn("symbol-boolean",60047),symbolNull:bn("symbol-null",60047),symbolNumeric:bn("symbol-numeric",60048),symbolNumber:bn("symbol-number",60048),symbolStructure:bn("symbol-structure",60049),symbolStruct:bn("symbol-struct",60049),symbolParameter:bn("symbol-parameter",60050),symbolTypeParameter:bn("symbol-type-parameter",60050),symbolKey:bn("symbol-key",60051),symbolText:bn("symbol-text",60051),symbolReference:bn("symbol-reference",60052),goToFile:bn("go-to-file",60052),symbolEnum:bn("symbol-enum",60053),symbolValue:bn("symbol-value",60053),symbolRuler:bn("symbol-ruler",60054),symbolUnit:bn("symbol-unit",60054),activateBreakpoints:bn("activate-breakpoints",60055),archive:bn("archive",60056),arrowBoth:bn("arrow-both",60057),arrowDown:bn("arrow-down",60058),arrowLeft:bn("arrow-left",60059),arrowRight:bn("arrow-right",60060),arrowSmallDown:bn("arrow-small-down",60061),arrowSmallLeft:bn("arrow-small-left",60062),arrowSmallRight:bn("arrow-small-right",60063),arrowSmallUp:bn("arrow-small-up",60064),arrowUp:bn("arrow-up",60065),bell:bn("bell",60066),bold:bn("bold",60067),book:bn("book",60068),bookmark:bn("bookmark",60069),debugBreakpointConditionalUnverified:bn("debug-breakpoint-conditional-unverified",60070),debugBreakpointConditional:bn("debug-breakpoint-conditional",60071),debugBreakpointConditionalDisabled:bn("debug-breakpoint-conditional-disabled",60071),debugBreakpointDataUnverified:bn("debug-breakpoint-data-unverified",60072),debugBreakpointData:bn("debug-breakpoint-data",60073),debugBreakpointDataDisabled:bn("debug-breakpoint-data-disabled",60073),debugBreakpointLogUnverified:bn("debug-breakpoint-log-unverified",60074),debugBreakpointLog:bn("debug-breakpoint-log",60075),debugBreakpointLogDisabled:bn("debug-breakpoint-log-disabled",60075),briefcase:bn("briefcase",60076),broadcast:bn("broadcast",60077),browser:bn("browser",60078),bug:bn("bug",60079),calendar:bn("calendar",60080),caseSensitive:bn("case-sensitive",60081),check:bn("check",60082),checklist:bn("checklist",60083),chevronDown:bn("chevron-down",60084),dropDownButton:bn("drop-down-button",60084),chevronLeft:bn("chevron-left",60085),chevronRight:bn("chevron-right",60086),chevronUp:bn("chevron-up",60087),chromeClose:bn("chrome-close",60088),chromeMaximize:bn("chrome-maximize",60089),chromeMinimize:bn("chrome-minimize",60090),chromeRestore:bn("chrome-restore",60091),circle:bn("circle",60092),circleOutline:bn("circle-outline",60092),debugBreakpointUnverified:bn("debug-breakpoint-unverified",60092),circleSlash:bn("circle-slash",60093),circuitBoard:bn("circuit-board",60094),clearAll:bn("clear-all",60095),clippy:bn("clippy",60096),closeAll:bn("close-all",60097),cloudDownload:bn("cloud-download",60098),cloudUpload:bn("cloud-upload",60099),code:bn("code",60100),collapseAll:bn("collapse-all",60101),colorMode:bn("color-mode",60102),commentDiscussion:bn("comment-discussion",60103),compareChanges:bn("compare-changes",60157),creditCard:bn("credit-card",60105),dash:bn("dash",60108),dashboard:bn("dashboard",60109),database:bn("database",60110),debugContinue:bn("debug-continue",60111),debugDisconnect:bn("debug-disconnect",60112),debugPause:bn("debug-pause",60113),debugRestart:bn("debug-restart",60114),debugStart:bn("debug-start",60115),debugStepInto:bn("debug-step-into",60116),debugStepOut:bn("debug-step-out",60117),debugStepOver:bn("debug-step-over",60118),debugStop:bn("debug-stop",60119),debug:bn("debug",60120),deviceCameraVideo:bn("device-camera-video",60121),deviceCamera:bn("device-camera",60122),deviceMobile:bn("device-mobile",60123),diffAdded:bn("diff-added",60124),diffIgnored:bn("diff-ignored",60125),diffModified:bn("diff-modified",60126),diffRemoved:bn("diff-removed",60127),diffRenamed:bn("diff-renamed",60128),diff:bn("diff",60129),discard:bn("discard",60130),editorLayout:bn("editor-layout",60131),emptyWindow:bn("empty-window",60132),exclude:bn("exclude",60133),extensions:bn("extensions",60134),eyeClosed:bn("eye-closed",60135),fileBinary:bn("file-binary",60136),fileCode:bn("file-code",60137),fileMedia:bn("file-media",60138),filePdf:bn("file-pdf",60139),fileSubmodule:bn("file-submodule",60140),fileSymlinkDirectory:bn("file-symlink-directory",60141),fileSymlinkFile:bn("file-symlink-file",60142),fileZip:bn("file-zip",60143),files:bn("files",60144),filter:bn("filter",60145),flame:bn("flame",60146),foldDown:bn("fold-down",60147),foldUp:bn("fold-up",60148),fold:bn("fold",60149),folderActive:bn("folder-active",60150),folderOpened:bn("folder-opened",60151),gear:bn("gear",60152),gift:bn("gift",60153),gistSecret:bn("gist-secret",60154),gist:bn("gist",60155),gitCommit:bn("git-commit",60156),gitCompare:bn("git-compare",60157),gitMerge:bn("git-merge",60158),githubAction:bn("github-action",60159),githubAlt:bn("github-alt",60160),globe:bn("globe",60161),grabber:bn("grabber",60162),graph:bn("graph",60163),gripper:bn("gripper",60164),heart:bn("heart",60165),home:bn("home",60166),horizontalRule:bn("horizontal-rule",60167),hubot:bn("hubot",60168),inbox:bn("inbox",60169),issueClosed:bn("issue-closed",60324),issueReopened:bn("issue-reopened",60171),issues:bn("issues",60172),italic:bn("italic",60173),jersey:bn("jersey",60174),json:bn("json",60175),bracket:bn("bracket",60175),kebabVertical:bn("kebab-vertical",60176),key:bn("key",60177),law:bn("law",60178),lightbulbAutofix:bn("lightbulb-autofix",60179),linkExternal:bn("link-external",60180),link:bn("link",60181),listOrdered:bn("list-ordered",60182),listUnordered:bn("list-unordered",60183),liveShare:bn("live-share",60184),loading:bn("loading",60185),location:bn("location",60186),mailRead:bn("mail-read",60187),mail:bn("mail",60188),markdown:bn("markdown",60189),megaphone:bn("megaphone",60190),mention:bn("mention",60191),milestone:bn("milestone",60192),mortarBoard:bn("mortar-board",60193),move:bn("move",60194),multipleWindows:bn("multiple-windows",60195),mute:bn("mute",60196),noNewline:bn("no-newline",60197),note:bn("note",60198),octoface:bn("octoface",60199),openPreview:bn("open-preview",60200),package_:bn("package",60201),paintcan:bn("paintcan",60202),pin:bn("pin",60203),play:bn("play",60204),run:bn("run",60204),plug:bn("plug",60205),preserveCase:bn("preserve-case",60206),preview:bn("preview",60207),project:bn("project",60208),pulse:bn("pulse",60209),question:bn("question",60210),quote:bn("quote",60211),radioTower:bn("radio-tower",60212),reactions:bn("reactions",60213),references:bn("references",60214),refresh:bn("refresh",60215),regex:bn("regex",60216),remoteExplorer:bn("remote-explorer",60217),remote:bn("remote",60218),remove:bn("remove",60219),replaceAll:bn("replace-all",60220),replace:bn("replace",60221),repoClone:bn("repo-clone",60222),repoForcePush:bn("repo-force-push",60223),repoPull:bn("repo-pull",60224),repoPush:bn("repo-push",60225),report:bn("report",60226),requestChanges:bn("request-changes",60227),rocket:bn("rocket",60228),rootFolderOpened:bn("root-folder-opened",60229),rootFolder:bn("root-folder",60230),rss:bn("rss",60231),ruby:bn("ruby",60232),saveAll:bn("save-all",60233),saveAs:bn("save-as",60234),save:bn("save",60235),screenFull:bn("screen-full",60236),screenNormal:bn("screen-normal",60237),searchStop:bn("search-stop",60238),server:bn("server",60240),settingsGear:bn("settings-gear",60241),settings:bn("settings",60242),shield:bn("shield",60243),smiley:bn("smiley",60244),sortPrecedence:bn("sort-precedence",60245),splitHorizontal:bn("split-horizontal",60246),splitVertical:bn("split-vertical",60247),squirrel:bn("squirrel",60248),starFull:bn("star-full",60249),starHalf:bn("star-half",60250),symbolClass:bn("symbol-class",60251),symbolColor:bn("symbol-color",60252),symbolCustomColor:bn("symbol-customcolor",60252),symbolConstant:bn("symbol-constant",60253),symbolEnumMember:bn("symbol-enum-member",60254),symbolField:bn("symbol-field",60255),symbolFile:bn("symbol-file",60256),symbolInterface:bn("symbol-interface",60257),symbolKeyword:bn("symbol-keyword",60258),symbolMisc:bn("symbol-misc",60259),symbolOperator:bn("symbol-operator",60260),symbolProperty:bn("symbol-property",60261),wrench:bn("wrench",60261),wrenchSubaction:bn("wrench-subaction",60261),symbolSnippet:bn("symbol-snippet",60262),tasklist:bn("tasklist",60263),telescope:bn("telescope",60264),textSize:bn("text-size",60265),threeBars:bn("three-bars",60266),thumbsdown:bn("thumbsdown",60267),thumbsup:bn("thumbsup",60268),tools:bn("tools",60269),triangleDown:bn("triangle-down",60270),triangleLeft:bn("triangle-left",60271),triangleRight:bn("triangle-right",60272),triangleUp:bn("triangle-up",60273),twitter:bn("twitter",60274),unfold:bn("unfold",60275),unlock:bn("unlock",60276),unmute:bn("unmute",60277),unverified:bn("unverified",60278),verified:bn("verified",60279),versions:bn("versions",60280),vmActive:bn("vm-active",60281),vmOutline:bn("vm-outline",60282),vmRunning:bn("vm-running",60283),watch:bn("watch",60284),whitespace:bn("whitespace",60285),wholeWord:bn("whole-word",60286),window:bn("window",60287),wordWrap:bn("word-wrap",60288),zoomIn:bn("zoom-in",60289),zoomOut:bn("zoom-out",60290),listFilter:bn("list-filter",60291),listFlat:bn("list-flat",60292),listSelection:bn("list-selection",60293),selection:bn("selection",60293),listTree:bn("list-tree",60294),debugBreakpointFunctionUnverified:bn("debug-breakpoint-function-unverified",60295),debugBreakpointFunction:bn("debug-breakpoint-function",60296),debugBreakpointFunctionDisabled:bn("debug-breakpoint-function-disabled",60296),debugStackframeActive:bn("debug-stackframe-active",60297),circleSmallFilled:bn("circle-small-filled",60298),debugStackframeDot:bn("debug-stackframe-dot",60298),debugStackframe:bn("debug-stackframe",60299),debugStackframeFocused:bn("debug-stackframe-focused",60299),debugBreakpointUnsupported:bn("debug-breakpoint-unsupported",60300),symbolString:bn("symbol-string",60301),debugReverseContinue:bn("debug-reverse-continue",60302),debugStepBack:bn("debug-step-back",60303),debugRestartFrame:bn("debug-restart-frame",60304),callIncoming:bn("call-incoming",60306),callOutgoing:bn("call-outgoing",60307),menu:bn("menu",60308),expandAll:bn("expand-all",60309),feedback:bn("feedback",60310),groupByRefType:bn("group-by-ref-type",60311),ungroupByRefType:bn("ungroup-by-ref-type",60312),account:bn("account",60313),bellDot:bn("bell-dot",60314),debugConsole:bn("debug-console",60315),library:bn("library",60316),output:bn("output",60317),runAll:bn("run-all",60318),syncIgnored:bn("sync-ignored",60319),pinned:bn("pinned",60320),githubInverted:bn("github-inverted",60321),debugAlt:bn("debug-alt",60305),serverProcess:bn("server-process",60322),serverEnvironment:bn("server-environment",60323),pass:bn("pass",60324),stopCircle:bn("stop-circle",60325),playCircle:bn("play-circle",60326),record:bn("record",60327),debugAltSmall:bn("debug-alt-small",60328),vmConnect:bn("vm-connect",60329),cloud:bn("cloud",60330),merge:bn("merge",60331),exportIcon:bn("export",60332),graphLeft:bn("graph-left",60333),magnet:bn("magnet",60334),notebook:bn("notebook",60335),redo:bn("redo",60336),checkAll:bn("check-all",60337),pinnedDirty:bn("pinned-dirty",60338),passFilled:bn("pass-filled",60339),circleLargeFilled:bn("circle-large-filled",60340),circleLarge:bn("circle-large",60341),circleLargeOutline:bn("circle-large-outline",60341),combine:bn("combine",60342),gather:bn("gather",60342),table:bn("table",60343),variableGroup:bn("variable-group",60344),typeHierarchy:bn("type-hierarchy",60345),typeHierarchySub:bn("type-hierarchy-sub",60346),typeHierarchySuper:bn("type-hierarchy-super",60347),gitPullRequestCreate:bn("git-pull-request-create",60348),runAbove:bn("run-above",60349),runBelow:bn("run-below",60350),notebookTemplate:bn("notebook-template",60351),debugRerun:bn("debug-rerun",60352),workspaceTrusted:bn("workspace-trusted",60353),workspaceUntrusted:bn("workspace-untrusted",60354),workspaceUnspecified:bn("workspace-unspecified",60355),terminalCmd:bn("terminal-cmd",60356),terminalDebian:bn("terminal-debian",60357),terminalLinux:bn("terminal-linux",60358),terminalPowershell:bn("terminal-powershell",60359),terminalTmux:bn("terminal-tmux",60360),terminalUbuntu:bn("terminal-ubuntu",60361),terminalBash:bn("terminal-bash",60362),arrowSwap:bn("arrow-swap",60363),copy:bn("copy",60364),personAdd:bn("person-add",60365),filterFilled:bn("filter-filled",60366),wand:bn("wand",60367),debugLineByLine:bn("debug-line-by-line",60368),inspect:bn("inspect",60369),layers:bn("layers",60370),layersDot:bn("layers-dot",60371),layersActive:bn("layers-active",60372),compass:bn("compass",60373),compassDot:bn("compass-dot",60374),compassActive:bn("compass-active",60375),azure:bn("azure",60376),issueDraft:bn("issue-draft",60377),gitPullRequestClosed:bn("git-pull-request-closed",60378),gitPullRequestDraft:bn("git-pull-request-draft",60379),debugAll:bn("debug-all",60380),debugCoverage:bn("debug-coverage",60381),runErrors:bn("run-errors",60382),folderLibrary:bn("folder-library",60383),debugContinueSmall:bn("debug-continue-small",60384),beakerStop:bn("beaker-stop",60385),graphLine:bn("graph-line",60386),graphScatter:bn("graph-scatter",60387),pieChart:bn("pie-chart",60388),bracketDot:bn("bracket-dot",60389),bracketError:bn("bracket-error",60390),lockSmall:bn("lock-small",60391),azureDevops:bn("azure-devops",60392),verifiedFilled:bn("verified-filled",60393),newLine:bn("newline",60394),layout:bn("layout",60395),layoutActivitybarLeft:bn("layout-activitybar-left",60396),layoutActivitybarRight:bn("layout-activitybar-right",60397),layoutPanelLeft:bn("layout-panel-left",60398),layoutPanelCenter:bn("layout-panel-center",60399),layoutPanelJustify:bn("layout-panel-justify",60400),layoutPanelRight:bn("layout-panel-right",60401),layoutPanel:bn("layout-panel",60402),layoutSidebarLeft:bn("layout-sidebar-left",60403),layoutSidebarRight:bn("layout-sidebar-right",60404),layoutStatusbar:bn("layout-statusbar",60405),layoutMenubar:bn("layout-menubar",60406),layoutCentered:bn("layout-centered",60407),layoutSidebarRightOff:bn("layout-sidebar-right-off",60416),layoutPanelOff:bn("layout-panel-off",60417),layoutSidebarLeftOff:bn("layout-sidebar-left-off",60418),target:bn("target",60408),indent:bn("indent",60409),recordSmall:bn("record-small",60410),errorSmall:bn("error-small",60411),arrowCircleDown:bn("arrow-circle-down",60412),arrowCircleLeft:bn("arrow-circle-left",60413),arrowCircleRight:bn("arrow-circle-right",60414),arrowCircleUp:bn("arrow-circle-up",60415),heartFilled:bn("heart-filled",60420),map:bn("map",60421),mapFilled:bn("map-filled",60422),circleSmall:bn("circle-small",60423),bellSlash:bn("bell-slash",60424),bellSlashDot:bn("bell-slash-dot",60425),commentUnresolved:bn("comment-unresolved",60426),gitPullRequestGoToChanges:bn("git-pull-request-go-to-changes",60427),gitPullRequestNewChanges:bn("git-pull-request-new-changes",60428),searchFuzzy:bn("search-fuzzy",60429),commentDraft:bn("comment-draft",60430),send:bn("send",60431),sparkle:bn("sparkle",60432),insert:bn("insert",60433),dialogError:bn("dialog-error","error"),dialogWarning:bn("dialog-warning","warning"),dialogInfo:bn("dialog-info","info"),dialogClose:bn("dialog-close","close"),treeItemExpanded:bn("tree-item-expanded","chevron-down"),treeFilterOnTypeOn:bn("tree-filter-on-type-on","list-filter"),treeFilterOnTypeOff:bn("tree-filter-on-type-off","list-selection"),treeFilterClear:bn("tree-filter-clear","close"),treeItemLoading:bn("tree-item-loading","loading"),menuSelection:bn("menu-selection","check"),menuSubmenu:bn("menu-submenu","chevron-right"),menuBarMore:bn("menubar-more","more"),scrollbarButtonLeft:bn("scrollbar-button-left","triangle-left"),scrollbarButtonRight:bn("scrollbar-button-right","triangle-right"),scrollbarButtonUp:bn("scrollbar-button-up","triangle-up"),scrollbarButtonDown:bn("scrollbar-button-down","triangle-down"),toolBarMore:bn("toolbar-more","more"),quickInputBack:bn("quick-input-back","arrow-left")};var Cn,yn,vn,wn,Ln,Sn,Nn,En=function(e,t,n,r){return new(n||(n=Promise))((function(i,s){function o(e){try{l(r.next(e))}catch(e){s(e)}}function a(e){try{l(r.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,a)}l((r=r.apply(e,t||[])).next())}))};class An extends C{get isResolved(){return this._isResolved}constructor(e,t,n){super(),this._registry=e,this._languageId=t,this._factory=n,this._isDisposed=!1,this._resolvePromise=null,this._isResolved=!1}dispose(){this._isDisposed=!0,super.dispose()}resolve(){return En(this,void 0,void 0,(function*(){return this._resolvePromise||(this._resolvePromise=this._create()),this._resolvePromise}))}_create(){return En(this,void 0,void 0,(function*(){const e=yield this._factory.tokenizationSupport;this._isResolved=!0,e&&!this._isDisposed&&this._register(this._registry.register(this._languageId,e))}))}}class Rn{constructor(e,t,n){this.offset=e,this.type=t,this.language=n,this._tokenBrand=void 0}toString(){return"("+this.offset+", "+this.type+")"}}!function(e){const t=new Map;t.set(0,_n.symbolMethod),t.set(1,_n.symbolFunction),t.set(2,_n.symbolConstructor),t.set(3,_n.symbolField),t.set(4,_n.symbolVariable),t.set(5,_n.symbolClass),t.set(6,_n.symbolStruct),t.set(7,_n.symbolInterface),t.set(8,_n.symbolModule),t.set(9,_n.symbolProperty),t.set(10,_n.symbolEvent),t.set(11,_n.symbolOperator),t.set(12,_n.symbolUnit),t.set(13,_n.symbolValue),t.set(15,_n.symbolEnum),t.set(14,_n.symbolConstant),t.set(15,_n.symbolEnum),t.set(16,_n.symbolEnumMember),t.set(17,_n.symbolKeyword),t.set(27,_n.symbolSnippet),t.set(18,_n.symbolText),t.set(19,_n.symbolColor),t.set(20,_n.symbolFile),t.set(21,_n.symbolReference),t.set(22,_n.symbolCustomColor),t.set(23,_n.symbolFolder),t.set(24,_n.symbolTypeParameter),t.set(25,_n.account),t.set(26,_n.issues),e.toIcon=function(e){let n=t.get(e);return n||(console.info("No codicon found for CompletionItemKind "+e),n=_n.symbolProperty),n};const n=new Map;n.set("method",0),n.set("function",1),n.set("constructor",2),n.set("field",3),n.set("variable",4),n.set("class",5),n.set("struct",6),n.set("interface",7),n.set("module",8),n.set("property",9),n.set("event",10),n.set("operator",11),n.set("unit",12),n.set("value",13),n.set("constant",14),n.set("enum",15),n.set("enum-member",16),n.set("enumMember",16),n.set("keyword",17),n.set("snippet",27),n.set("text",18),n.set("color",19),n.set("file",20),n.set("reference",21),n.set("customcolor",22),n.set("folder",23),n.set("type-parameter",24),n.set("typeParameter",24),n.set("account",25),n.set("issue",26),e.fromString=function(e,t){let r=n.get(e);return void 0!==r||t||(r=9),r}}(Cn||(Cn={})),function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"}(yn||(yn={}));!function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"}(vn||(vn={})),function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"}(wn||(wn={})),function(e){const t=new Map;t.set(0,_n.symbolFile),t.set(1,_n.symbolModule),t.set(2,_n.symbolNamespace),t.set(3,_n.symbolPackage),t.set(4,_n.symbolClass),t.set(5,_n.symbolMethod),t.set(6,_n.symbolProperty),t.set(7,_n.symbolField),t.set(8,_n.symbolConstructor),t.set(9,_n.symbolEnum),t.set(10,_n.symbolInterface),t.set(11,_n.symbolFunction),t.set(12,_n.symbolVariable),t.set(13,_n.symbolConstant),t.set(14,_n.symbolString),t.set(15,_n.symbolNumber),t.set(16,_n.symbolBoolean),t.set(17,_n.symbolArray),t.set(18,_n.symbolObject),t.set(19,_n.symbolKey),t.set(20,_n.symbolNull),t.set(21,_n.symbolEnumMember),t.set(22,_n.symbolStruct),t.set(23,_n.symbolEvent),t.set(24,_n.symbolOperator),t.set(25,_n.symbolTypeParameter),e.toIcon=function(e){let n=t.get(e);return n||(console.info("No codicon found for SymbolKind "+e),n=_n.symbolProperty),n}}(Ln||(Ln={}));class xn{static fromValue(e){switch(e){case"comment":return xn.Comment;case"imports":return xn.Imports;case"region":return xn.Region}return new xn(e)}constructor(e){this.value=e}}xn.Comment=new xn("comment"),xn.Imports=new xn("imports"),xn.Region=new xn("region"),function(e){e.is=function(e){return!(!e||"object"!=typeof e)&&("string"==typeof e.id&&"string"==typeof e.title)}}(Sn||(Sn={})),function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"}(Nn||(Nn={}));new class{constructor(){this._tokenizationSupports=new Map,this._factories=new Map,this._onDidChange=new ae,this.onDidChange=this._onDidChange.event,this._colorMap=null}handleChange(e){this._onDidChange.fire({changedLanguages:e,changedColorMap:!1})}register(e,t){return this._tokenizationSupports.set(e,t),this.handleChange([e]),b((()=>{this._tokenizationSupports.get(e)===t&&(this._tokenizationSupports.delete(e),this.handleChange([e]))}))}get(e){return this._tokenizationSupports.get(e)||null}registerFactory(e,t){var n;null===(n=this._factories.get(e))||void 0===n||n.dispose();const r=new An(this,e,t);return this._factories.set(e,r),b((()=>{const t=this._factories.get(e);t&&t===r&&(this._factories.delete(e),t.dispose())}))}getOrCreate(e){return En(this,void 0,void 0,(function*(){const t=this.get(e);if(t)return t;const n=this._factories.get(e);return!n||n.isResolved?null:(yield n.resolve(),this.get(e))}))}isResolved(e){if(this.get(e))return!0;const t=this._factories.get(e);return!(t&&!t.isResolved)}setColorMap(e){this._colorMap=e,this._onDidChange.fire({changedLanguages:Array.from(this._tokenizationSupports.keys()),changedColorMap:!0})}getColorMap(){return this._colorMap}getDefaultBackground(){return this._colorMap&&this._colorMap.length>2?this._colorMap[2]:null}};var Mn,kn,Tn,On,In,Dn,Pn,Fn,Kn,Vn,Bn,qn,Un,Wn,Hn,$n,zn,jn,Gn,Yn,Jn,Qn,Xn,Zn,er,tr,nr,rr,ir,sr,or,ar,lr,ur,hr,cr,dr,gr,mr;!function(e){e[e.Unknown=0]="Unknown",e[e.Disabled=1]="Disabled",e[e.Enabled=2]="Enabled"}(Mn||(Mn={})),function(e){e[e.Invoke=1]="Invoke",e[e.Auto=2]="Auto"}(kn||(kn={})),function(e){e[e.None=0]="None",e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"}(Tn||(Tn={})),function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Snippet=27]="Snippet"}(On||(On={})),function(e){e[e.Deprecated=1]="Deprecated"}(In||(In={})),function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"}(Dn||(Dn={})),function(e){e[e.EXACT=0]="EXACT",e[e.ABOVE=1]="ABOVE",e[e.BELOW=2]="BELOW"}(Pn||(Pn={})),function(e){e[e.NotSet=0]="NotSet",e[e.ContentFlush=1]="ContentFlush",e[e.RecoverFromMarkers=2]="RecoverFromMarkers",e[e.Explicit=3]="Explicit",e[e.Paste=4]="Paste",e[e.Undo=5]="Undo",e[e.Redo=6]="Redo"}(Fn||(Fn={})),function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"}(Kn||(Kn={})),function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"}(Vn||(Vn={})),function(e){e[e.None=0]="None",e[e.Keep=1]="Keep",e[e.Brackets=2]="Brackets",e[e.Advanced=3]="Advanced",e[e.Full=4]="Full"}(Bn||(Bn={})),function(e){e[e.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",e[e.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",e[e.accessibilitySupport=2]="accessibilitySupport",e[e.accessibilityPageSize=3]="accessibilityPageSize",e[e.ariaLabel=4]="ariaLabel",e[e.autoClosingBrackets=5]="autoClosingBrackets",e[e.screenReaderAnnounceInlineSuggestion=6]="screenReaderAnnounceInlineSuggestion",e[e.autoClosingDelete=7]="autoClosingDelete",e[e.autoClosingOvertype=8]="autoClosingOvertype",e[e.autoClosingQuotes=9]="autoClosingQuotes",e[e.autoIndent=10]="autoIndent",e[e.automaticLayout=11]="automaticLayout",e[e.autoSurround=12]="autoSurround",e[e.bracketPairColorization=13]="bracketPairColorization",e[e.guides=14]="guides",e[e.codeLens=15]="codeLens",e[e.codeLensFontFamily=16]="codeLensFontFamily",e[e.codeLensFontSize=17]="codeLensFontSize",e[e.colorDecorators=18]="colorDecorators",e[e.colorDecoratorsLimit=19]="colorDecoratorsLimit",e[e.columnSelection=20]="columnSelection",e[e.comments=21]="comments",e[e.contextmenu=22]="contextmenu",e[e.copyWithSyntaxHighlighting=23]="copyWithSyntaxHighlighting",e[e.cursorBlinking=24]="cursorBlinking",e[e.cursorSmoothCaretAnimation=25]="cursorSmoothCaretAnimation",e[e.cursorStyle=26]="cursorStyle",e[e.cursorSurroundingLines=27]="cursorSurroundingLines",e[e.cursorSurroundingLinesStyle=28]="cursorSurroundingLinesStyle",e[e.cursorWidth=29]="cursorWidth",e[e.disableLayerHinting=30]="disableLayerHinting",e[e.disableMonospaceOptimizations=31]="disableMonospaceOptimizations",e[e.domReadOnly=32]="domReadOnly",e[e.dragAndDrop=33]="dragAndDrop",e[e.dropIntoEditor=34]="dropIntoEditor",e[e.emptySelectionClipboard=35]="emptySelectionClipboard",e[e.experimentalWhitespaceRendering=36]="experimentalWhitespaceRendering",e[e.extraEditorClassName=37]="extraEditorClassName",e[e.fastScrollSensitivity=38]="fastScrollSensitivity",e[e.find=39]="find",e[e.fixedOverflowWidgets=40]="fixedOverflowWidgets",e[e.folding=41]="folding",e[e.foldingStrategy=42]="foldingStrategy",e[e.foldingHighlight=43]="foldingHighlight",e[e.foldingImportsByDefault=44]="foldingImportsByDefault",e[e.foldingMaximumRegions=45]="foldingMaximumRegions",e[e.unfoldOnClickAfterEndOfLine=46]="unfoldOnClickAfterEndOfLine",e[e.fontFamily=47]="fontFamily",e[e.fontInfo=48]="fontInfo",e[e.fontLigatures=49]="fontLigatures",e[e.fontSize=50]="fontSize",e[e.fontWeight=51]="fontWeight",e[e.fontVariations=52]="fontVariations",e[e.formatOnPaste=53]="formatOnPaste",e[e.formatOnType=54]="formatOnType",e[e.glyphMargin=55]="glyphMargin",e[e.gotoLocation=56]="gotoLocation",e[e.hideCursorInOverviewRuler=57]="hideCursorInOverviewRuler",e[e.hover=58]="hover",e[e.inDiffEditor=59]="inDiffEditor",e[e.inlineSuggest=60]="inlineSuggest",e[e.letterSpacing=61]="letterSpacing",e[e.lightbulb=62]="lightbulb",e[e.lineDecorationsWidth=63]="lineDecorationsWidth",e[e.lineHeight=64]="lineHeight",e[e.lineNumbers=65]="lineNumbers",e[e.lineNumbersMinChars=66]="lineNumbersMinChars",e[e.linkedEditing=67]="linkedEditing",e[e.links=68]="links",e[e.matchBrackets=69]="matchBrackets",e[e.minimap=70]="minimap",e[e.mouseStyle=71]="mouseStyle",e[e.mouseWheelScrollSensitivity=72]="mouseWheelScrollSensitivity",e[e.mouseWheelZoom=73]="mouseWheelZoom",e[e.multiCursorMergeOverlapping=74]="multiCursorMergeOverlapping",e[e.multiCursorModifier=75]="multiCursorModifier",e[e.multiCursorPaste=76]="multiCursorPaste",e[e.multiCursorLimit=77]="multiCursorLimit",e[e.occurrencesHighlight=78]="occurrencesHighlight",e[e.overviewRulerBorder=79]="overviewRulerBorder",e[e.overviewRulerLanes=80]="overviewRulerLanes",e[e.padding=81]="padding",e[e.pasteAs=82]="pasteAs",e[e.parameterHints=83]="parameterHints",e[e.peekWidgetDefaultFocus=84]="peekWidgetDefaultFocus",e[e.definitionLinkOpensInPeek=85]="definitionLinkOpensInPeek",e[e.quickSuggestions=86]="quickSuggestions",e[e.quickSuggestionsDelay=87]="quickSuggestionsDelay",e[e.readOnly=88]="readOnly",e[e.renameOnType=89]="renameOnType",e[e.renderControlCharacters=90]="renderControlCharacters",e[e.renderFinalNewline=91]="renderFinalNewline",e[e.renderLineHighlight=92]="renderLineHighlight",e[e.renderLineHighlightOnlyWhenFocus=93]="renderLineHighlightOnlyWhenFocus",e[e.renderValidationDecorations=94]="renderValidationDecorations",e[e.renderWhitespace=95]="renderWhitespace",e[e.revealHorizontalRightPadding=96]="revealHorizontalRightPadding",e[e.roundedSelection=97]="roundedSelection",e[e.rulers=98]="rulers",e[e.scrollbar=99]="scrollbar",e[e.scrollBeyondLastColumn=100]="scrollBeyondLastColumn",e[e.scrollBeyondLastLine=101]="scrollBeyondLastLine",e[e.scrollPredominantAxis=102]="scrollPredominantAxis",e[e.selectionClipboard=103]="selectionClipboard",e[e.selectionHighlight=104]="selectionHighlight",e[e.selectOnLineNumbers=105]="selectOnLineNumbers",e[e.showFoldingControls=106]="showFoldingControls",e[e.showUnused=107]="showUnused",e[e.snippetSuggestions=108]="snippetSuggestions",e[e.smartSelect=109]="smartSelect",e[e.smoothScrolling=110]="smoothScrolling",e[e.stickyScroll=111]="stickyScroll",e[e.stickyTabStops=112]="stickyTabStops",e[e.stopRenderingLineAfter=113]="stopRenderingLineAfter",e[e.suggest=114]="suggest",e[e.suggestFontSize=115]="suggestFontSize",e[e.suggestLineHeight=116]="suggestLineHeight",e[e.suggestOnTriggerCharacters=117]="suggestOnTriggerCharacters",e[e.suggestSelection=118]="suggestSelection",e[e.tabCompletion=119]="tabCompletion",e[e.tabIndex=120]="tabIndex",e[e.unicodeHighlighting=121]="unicodeHighlighting",e[e.unusualLineTerminators=122]="unusualLineTerminators",e[e.useShadowDOM=123]="useShadowDOM",e[e.useTabStops=124]="useTabStops",e[e.wordBreak=125]="wordBreak",e[e.wordSeparators=126]="wordSeparators",e[e.wordWrap=127]="wordWrap",e[e.wordWrapBreakAfterCharacters=128]="wordWrapBreakAfterCharacters",e[e.wordWrapBreakBeforeCharacters=129]="wordWrapBreakBeforeCharacters",e[e.wordWrapColumn=130]="wordWrapColumn",e[e.wordWrapOverride1=131]="wordWrapOverride1",e[e.wordWrapOverride2=132]="wordWrapOverride2",e[e.wrappingIndent=133]="wrappingIndent",e[e.wrappingStrategy=134]="wrappingStrategy",e[e.showDeprecated=135]="showDeprecated",e[e.inlayHints=136]="inlayHints",e[e.editorClassName=137]="editorClassName",e[e.pixelRatio=138]="pixelRatio",e[e.tabFocusMode=139]="tabFocusMode",e[e.layoutInfo=140]="layoutInfo",e[e.wrappingInfo=141]="wrappingInfo",e[e.defaultColorDecorators=142]="defaultColorDecorators"}(qn||(qn={})),function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"}(Un||(Un={})),function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"}(Wn||(Wn={})),function(e){e[e.Left=1]="Left",e[e.Right=2]="Right"}(Hn||(Hn={})),function(e){e[e.None=0]="None",e[e.Indent=1]="Indent",e[e.IndentOutdent=2]="IndentOutdent",e[e.Outdent=3]="Outdent"}($n||($n={})),function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"}(zn||(zn={})),function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"}(jn||(jn={})),function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"}(Gn||(Gn={})),function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.Unknown=0]="Unknown",e[e.Backspace=1]="Backspace",e[e.Tab=2]="Tab",e[e.Enter=3]="Enter",e[e.Shift=4]="Shift",e[e.Ctrl=5]="Ctrl",e[e.Alt=6]="Alt",e[e.PauseBreak=7]="PauseBreak",e[e.CapsLock=8]="CapsLock",e[e.Escape=9]="Escape",e[e.Space=10]="Space",e[e.PageUp=11]="PageUp",e[e.PageDown=12]="PageDown",e[e.End=13]="End",e[e.Home=14]="Home",e[e.LeftArrow=15]="LeftArrow",e[e.UpArrow=16]="UpArrow",e[e.RightArrow=17]="RightArrow",e[e.DownArrow=18]="DownArrow",e[e.Insert=19]="Insert",e[e.Delete=20]="Delete",e[e.Digit0=21]="Digit0",e[e.Digit1=22]="Digit1",e[e.Digit2=23]="Digit2",e[e.Digit3=24]="Digit3",e[e.Digit4=25]="Digit4",e[e.Digit5=26]="Digit5",e[e.Digit6=27]="Digit6",e[e.Digit7=28]="Digit7",e[e.Digit8=29]="Digit8",e[e.Digit9=30]="Digit9",e[e.KeyA=31]="KeyA",e[e.KeyB=32]="KeyB",e[e.KeyC=33]="KeyC",e[e.KeyD=34]="KeyD",e[e.KeyE=35]="KeyE",e[e.KeyF=36]="KeyF",e[e.KeyG=37]="KeyG",e[e.KeyH=38]="KeyH",e[e.KeyI=39]="KeyI",e[e.KeyJ=40]="KeyJ",e[e.KeyK=41]="KeyK",e[e.KeyL=42]="KeyL",e[e.KeyM=43]="KeyM",e[e.KeyN=44]="KeyN",e[e.KeyO=45]="KeyO",e[e.KeyP=46]="KeyP",e[e.KeyQ=47]="KeyQ",e[e.KeyR=48]="KeyR",e[e.KeyS=49]="KeyS",e[e.KeyT=50]="KeyT",e[e.KeyU=51]="KeyU",e[e.KeyV=52]="KeyV",e[e.KeyW=53]="KeyW",e[e.KeyX=54]="KeyX",e[e.KeyY=55]="KeyY",e[e.KeyZ=56]="KeyZ",e[e.Meta=57]="Meta",e[e.ContextMenu=58]="ContextMenu",e[e.F1=59]="F1",e[e.F2=60]="F2",e[e.F3=61]="F3",e[e.F4=62]="F4",e[e.F5=63]="F5",e[e.F6=64]="F6",e[e.F7=65]="F7",e[e.F8=66]="F8",e[e.F9=67]="F9",e[e.F10=68]="F10",e[e.F11=69]="F11",e[e.F12=70]="F12",e[e.F13=71]="F13",e[e.F14=72]="F14",e[e.F15=73]="F15",e[e.F16=74]="F16",e[e.F17=75]="F17",e[e.F18=76]="F18",e[e.F19=77]="F19",e[e.F20=78]="F20",e[e.F21=79]="F21",e[e.F22=80]="F22",e[e.F23=81]="F23",e[e.F24=82]="F24",e[e.NumLock=83]="NumLock",e[e.ScrollLock=84]="ScrollLock",e[e.Semicolon=85]="Semicolon",e[e.Equal=86]="Equal",e[e.Comma=87]="Comma",e[e.Minus=88]="Minus",e[e.Period=89]="Period",e[e.Slash=90]="Slash",e[e.Backquote=91]="Backquote",e[e.BracketLeft=92]="BracketLeft",e[e.Backslash=93]="Backslash",e[e.BracketRight=94]="BracketRight",e[e.Quote=95]="Quote",e[e.OEM_8=96]="OEM_8",e[e.IntlBackslash=97]="IntlBackslash",e[e.Numpad0=98]="Numpad0",e[e.Numpad1=99]="Numpad1",e[e.Numpad2=100]="Numpad2",e[e.Numpad3=101]="Numpad3",e[e.Numpad4=102]="Numpad4",e[e.Numpad5=103]="Numpad5",e[e.Numpad6=104]="Numpad6",e[e.Numpad7=105]="Numpad7",e[e.Numpad8=106]="Numpad8",e[e.Numpad9=107]="Numpad9",e[e.NumpadMultiply=108]="NumpadMultiply",e[e.NumpadAdd=109]="NumpadAdd",e[e.NUMPAD_SEPARATOR=110]="NUMPAD_SEPARATOR",e[e.NumpadSubtract=111]="NumpadSubtract",e[e.NumpadDecimal=112]="NumpadDecimal",e[e.NumpadDivide=113]="NumpadDivide",e[e.KEY_IN_COMPOSITION=114]="KEY_IN_COMPOSITION",e[e.ABNT_C1=115]="ABNT_C1",e[e.ABNT_C2=116]="ABNT_C2",e[e.AudioVolumeMute=117]="AudioVolumeMute",e[e.AudioVolumeUp=118]="AudioVolumeUp",e[e.AudioVolumeDown=119]="AudioVolumeDown",e[e.BrowserSearch=120]="BrowserSearch",e[e.BrowserHome=121]="BrowserHome",e[e.BrowserBack=122]="BrowserBack",e[e.BrowserForward=123]="BrowserForward",e[e.MediaTrackNext=124]="MediaTrackNext",e[e.MediaTrackPrevious=125]="MediaTrackPrevious",e[e.MediaStop=126]="MediaStop",e[e.MediaPlayPause=127]="MediaPlayPause",e[e.LaunchMediaPlayer=128]="LaunchMediaPlayer",e[e.LaunchMail=129]="LaunchMail",e[e.LaunchApp2=130]="LaunchApp2",e[e.Clear=131]="Clear",e[e.MAX_VALUE=132]="MAX_VALUE"}(Yn||(Yn={})),function(e){e[e.Hint=1]="Hint",e[e.Info=2]="Info",e[e.Warning=4]="Warning",e[e.Error=8]="Error"}(Jn||(Jn={})),function(e){e[e.Unnecessary=1]="Unnecessary",e[e.Deprecated=2]="Deprecated"}(Qn||(Qn={})),function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"}(Xn||(Xn={})),function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.TEXTAREA=1]="TEXTAREA",e[e.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",e[e.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",e[e.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",e[e.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",e[e.CONTENT_TEXT=6]="CONTENT_TEXT",e[e.CONTENT_EMPTY=7]="CONTENT_EMPTY",e[e.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",e[e.CONTENT_WIDGET=9]="CONTENT_WIDGET",e[e.OVERVIEW_RULER=10]="OVERVIEW_RULER",e[e.SCROLLBAR=11]="SCROLLBAR",e[e.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",e[e.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR"}(Zn||(Zn={})),function(e){e[e.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",e[e.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",e[e.TOP_CENTER=2]="TOP_CENTER"}(er||(er={})),function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"}(tr||(tr={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right",e[e.None=2]="None",e[e.LeftOfInjectedText=3]="LeftOfInjectedText",e[e.RightOfInjectedText=4]="RightOfInjectedText"}(nr||(nr={})),function(e){e[e.Off=0]="Off",e[e.On=1]="On",e[e.Relative=2]="Relative",e[e.Interval=3]="Interval",e[e.Custom=4]="Custom"}(rr||(rr={})),function(e){e[e.None=0]="None",e[e.Text=1]="Text",e[e.Blocks=2]="Blocks"}(ir||(ir={})),function(e){e[e.Smooth=0]="Smooth",e[e.Immediate=1]="Immediate"}(sr||(sr={})),function(e){e[e.Auto=1]="Auto",e[e.Hidden=2]="Hidden",e[e.Visible=3]="Visible"}(or||(or={})),function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"}(ar||(ar={})),function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"}(lr||(lr={})),function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"}(ur||(ur={})),function(e){e[e.Deprecated=1]="Deprecated"}(hr||(hr={})),function(e){e[e.Hidden=0]="Hidden",e[e.Blink=1]="Blink",e[e.Smooth=2]="Smooth",e[e.Phase=3]="Phase",e[e.Expand=4]="Expand",e[e.Solid=5]="Solid"}(cr||(cr={})),function(e){e[e.Line=1]="Line",e[e.Block=2]="Block",e[e.Underline=3]="Underline",e[e.LineThin=4]="LineThin",e[e.BlockOutline=5]="BlockOutline",e[e.UnderlineThin=6]="UnderlineThin"}(dr||(dr={})),function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"}(gr||(gr={})),function(e){e[e.None=0]="None",e[e.Same=1]="Same",e[e.Indent=2]="Indent",e[e.DeepIndent=3]="DeepIndent"}(mr||(mr={}));class fr{static chord(e,t){return function(e,t){return(e|(65535&t)<<16>>>0)>>>0}(e,t)}}fr.CtrlCmd=2048,fr.Shift=1024,fr.Alt=512,fr.WinCtrl=256;class pr extends Ht{constructor(e){super(0);for(let t=0,n=e.length;t<n;t++)this.set(e.charCodeAt(t),2);this.set(32,1),this.set(9,1)}}!function(e){const t={}}((e=>new pr(e)));var br,_r,Cr,yr;!function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"}(br||(br={})),function(e){e[e.Left=1]="Left",e[e.Right=2]="Right"}(_r||(_r={})),function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"}(Cr||(Cr={})),function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"}(yr||(yr={}));function vr(e,t,n,r,i){return function(e,t,n,r,i){if(0===r)return!0;const s=t.charCodeAt(r-1);if(0!==e.get(s))return!0;if(13===s||10===s)return!0;if(i>0){const n=t.charCodeAt(r);if(0!==e.get(n))return!0}return!1}(e,t,0,r,i)&&function(e,t,n,r,i){if(r+i===n)return!0;const s=t.charCodeAt(r+i);if(0!==e.get(s))return!0;if(13===s||10===s)return!0;if(i>0){const n=t.charCodeAt(r+i-1);if(0!==e.get(n))return!0}return!1}(e,t,n,r,i)}class wr{constructor(e,t){this._wordSeparators=e,this._searchRegex=t,this._prevMatchStartIndex=-1,this._prevMatchLength=0}reset(e){this._searchRegex.lastIndex=e,this._prevMatchStartIndex=-1,this._prevMatchLength=0}next(e){const t=e.length;let n;do{if(this._prevMatchStartIndex+this._prevMatchLength===t)return null;if(n=this._searchRegex.exec(e),!n)return null;const r=n.index,i=n[0].length;if(r===this._prevMatchStartIndex&&i===this._prevMatchLength){if(0===i){Ce(e,t,this._searchRegex.lastIndex)>65535?this._searchRegex.lastIndex+=2:this._searchRegex.lastIndex+=1;continue}return null}if(this._prevMatchStartIndex=r,this._prevMatchLength=i,!this._wordSeparators||vr(this._wordSeparators,e,t,r,i))return n}while(n);return null}}function Lr(e,t="Unreachable"){throw new Error(t)}function Sr(e){e()||(e(),t(new l("Assertion Failed")))}function Nr(e,t){let n=0;for(;n<e.length-1;){if(!t(e[n],e[n+1]))return!1;n++}return!0}class Er{static computeUnicodeHighlights(e,t,n){const r=n?n.startLineNumber:1,i=n?n.endLineNumber:e.getLineCount(),s=new Ar(t),o=s.getCandidateCodePoints();let a;a="allNonBasicAscii"===o?new RegExp("[^\\t\\n\\r\\x20-\\x7E]","g"):new RegExp(""+`[${me(Array.from(o).map((e=>String.fromCodePoint(e))).join(""))}]`,"g");const l=new wr(null,a),u=[];let h,c=!1,d=0,g=0,m=0;e:for(let t=r,n=i;t<=n;t++){const n=e.getLineContent(t),r=n.length;l.reset(0);do{if(h=l.next(n),h){let e=h.index,i=h.index+h[0].length;if(e>0){pe(n.charCodeAt(e-1))&&e--}if(i+1<r){pe(n.charCodeAt(i-1))&&i++}const o=n.substring(e,i);let a=Ut(e+1,Bt,n,0);a&&a.endColumn<=e+1&&(a=null);const l=s.shouldHighlightNonBasicASCII(o,a?a.word:null);if(0!==l){3===l?d++:2===l?g++:1===l?m++:Lr();const n=1e3;if(u.length>=n){c=!0;break e}u.push(new Tt(t,e+1,t,i+1))}}}while(h)}return{ranges:u,hasMore:c,ambiguousCharacterCount:d,invisibleCharacterCount:g,nonBasicAsciiCharacterCount:m}}static computeUnicodeHighlightReason(e,t){const n=new Ar(t);switch(n.shouldHighlightNonBasicASCII(e,null)){case 0:return null;case 2:return{kind:1};case 3:{const r=e.codePointAt(0),i=n.ambiguousCharacters.getPrimaryConfusable(r),s=we.getLocales().filter((e=>!we.getInstance(new Set([...t.allowedLocales,e])).isAmbiguous(r)));return{kind:0,confusableWith:String.fromCodePoint(i),notAmbiguousInLocales:s}}case 1:return{kind:2}}}}class Ar{constructor(e){this.options=e,this.allowedCodePoints=new Set(e.allowedCodePoints),this.ambiguousCharacters=we.getInstance(new Set(e.allowedLocales))}getCandidateCodePoints(){if(this.options.nonBasicASCII)return"allNonBasicAscii";const e=new Set;if(this.options.invisibleCharacters)for(const t of Le.codePoints)Rr(String.fromCodePoint(t))||e.add(t);if(this.options.ambiguousCharacters)for(const t of this.ambiguousCharacters.getConfusableCodePoints())e.add(t);for(const t of this.allowedCodePoints)e.delete(t);return e}shouldHighlightNonBasicASCII(e,t){const n=e.codePointAt(0);if(this.allowedCodePoints.has(n))return 0;if(this.options.nonBasicASCII)return 1;let r=!1,i=!1;if(t)for(const e of t){const t=e.codePointAt(0),n=(s=e,ye.test(s));r=r||n,n||this.ambiguousCharacters.isAmbiguous(t)||Le.isInvisibleCharacter(t)||(i=!0)}var s;return!r&&i?0:this.options.invisibleCharacters&&!Rr(e)&&Le.isInvisibleCharacter(n)?2:this.options.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(n)?3:0}}function Rr(e){return" "===e||"\n"===e||"\t"===e}class xr{static fromRange(e){return new xr(e.startLineNumber,e.endLineNumber)}static joinMany(e){if(0===e.length)return[];let t=e[0];for(let n=1;n<e.length;n++)t=this.join(t,e[n]);return t}static join(e,t){if(0===e.length)return t;if(0===t.length)return e;const n=[];let r=0,i=0,s=null;for(;r<e.length||i<t.length;){let o=null;if(r<e.length&&i<t.length){const n=e[r],s=t[i];n.startLineNumber<s.startLineNumber?(o=n,r++):(o=s,i++)}else r<e.length?(o=e[r],r++):(o=t[i],i++);null===s?s=o:s.endLineNumberExclusive>=o.startLineNumber?s=new xr(s.startLineNumber,Math.max(s.endLineNumberExclusive,o.endLineNumberExclusive)):(n.push(s),s=o)}return null!==s&&n.push(s),n}static ofLength(e,t){return new xr(e,e+t)}constructor(e,t){if(e>t)throw new l(`startLineNumber ${e} cannot be after endLineNumberExclusive ${t}`);this.startLineNumber=e,this.endLineNumberExclusive=t}contains(e){return this.startLineNumber<=e&&e<this.endLineNumberExclusive}get isEmpty(){return this.startLineNumber===this.endLineNumberExclusive}delta(e){return new xr(this.startLineNumber+e,this.endLineNumberExclusive+e)}get length(){return this.endLineNumberExclusive-this.startLineNumber}join(e){return new xr(Math.min(this.startLineNumber,e.startLineNumber),Math.max(this.endLineNumberExclusive,e.endLineNumberExclusive))}toString(){return`[${this.startLineNumber},${this.endLineNumberExclusive})`}intersect(e){const t=Math.max(this.startLineNumber,e.startLineNumber),n=Math.min(this.endLineNumberExclusive,e.endLineNumberExclusive);if(t<=n)return new xr(t,n)}intersectsStrict(e){return this.startLineNumber<e.endLineNumberExclusive&&e.startLineNumber<this.endLineNumberExclusive}overlapOrTouch(e){return this.startLineNumber<=e.endLineNumberExclusive&&e.startLineNumber<=this.endLineNumberExclusive}equals(e){return this.startLineNumber===e.startLineNumber&&this.endLineNumberExclusive===e.endLineNumberExclusive}toInclusiveRange(){return this.isEmpty?null:new Tt(this.startLineNumber,1,this.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER)}toExclusiveRange(){return new Tt(this.startLineNumber,1,this.endLineNumberExclusive,1)}}class Mr{constructor(e,t){this.changes=e,this.hitTimeout=t}}class kr{static inverse(e,t,n){const r=[];let i=1,s=1;for(const t of e){const e=new kr(new xr(i,t.originalRange.startLineNumber),new xr(s,t.modifiedRange.startLineNumber),void 0);e.modifiedRange.isEmpty||r.push(e),i=t.originalRange.endLineNumberExclusive,s=t.modifiedRange.endLineNumberExclusive}const o=new kr(new xr(i,t+1),new xr(s,n+1),void 0);return o.modifiedRange.isEmpty||r.push(o),r}constructor(e,t,n){this.originalRange=e,this.modifiedRange=t,this.innerChanges=n}toString(){return`{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`}get changedLineCount(){return Math.max(this.originalRange.length,this.modifiedRange.length)}}class Tr{constructor(e,t){this.originalRange=e,this.modifiedRange=t}toString(){return`{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`}}function Or(e,t,n,r){return new je(e,t,n).ComputeDiff(r)}class Ir{constructor(e){const t=[],n=[];for(let r=0,i=e.length;r<i;r++)t[r]=Vr(e[r],1),n[r]=Br(e[r],1);this.lines=e,this._startColumns=t,this._endColumns=n}getElements(){const e=[];for(let t=0,n=this.lines.length;t<n;t++)e[t]=this.lines[t].substring(this._startColumns[t]-1,this._endColumns[t]-1);return e}getStrictElement(e){return this.lines[e]}getStartLineNumber(e){return e+1}getEndLineNumber(e){return e+1}createCharSequence(e,t,n){const r=[],i=[],s=[];let o=0;for(let a=t;a<=n;a++){const t=this.lines[a],l=e?this._startColumns[a]:1,u=e?this._endColumns[a]:t.length+1;for(let e=l;e<u;e++)r[o]=t.charCodeAt(e-1),i[o]=a+1,s[o]=e,o++;!e&&a<n&&(r[o]=10,i[o]=a+1,s[o]=t.length+1,o++)}return new Dr(r,i,s)}}class Dr{constructor(e,t,n){this._charCodes=e,this._lineNumbers=t,this._columns=n}toString(){return"["+this._charCodes.map(((e,t)=>(10===e?"\\n":String.fromCharCode(e))+`-(${this._lineNumbers[t]},${this._columns[t]})`)).join(", ")+"]"}_assertIndex(e,t){if(e<0||e>=t.length)throw new Error("Illegal index")}getElements(){return this._charCodes}getStartLineNumber(e){return e>0&&e===this._lineNumbers.length?this.getEndLineNumber(e-1):(this._assertIndex(e,this._lineNumbers),this._lineNumbers[e])}getEndLineNumber(e){return-1===e?this.getStartLineNumber(e+1):(this._assertIndex(e,this._lineNumbers),10===this._charCodes[e]?this._lineNumbers[e]+1:this._lineNumbers[e])}getStartColumn(e){return e>0&&e===this._columns.length?this.getEndColumn(e-1):(this._assertIndex(e,this._columns),this._columns[e])}getEndColumn(e){return-1===e?this.getStartColumn(e+1):(this._assertIndex(e,this._columns),10===this._charCodes[e]?1:this._columns[e]+1)}}class Pr{constructor(e,t,n,r,i,s,o,a){this.originalStartLineNumber=e,this.originalStartColumn=t,this.originalEndLineNumber=n,this.originalEndColumn=r,this.modifiedStartLineNumber=i,this.modifiedStartColumn=s,this.modifiedEndLineNumber=o,this.modifiedEndColumn=a}static createFromDiffChange(e,t,n){const r=t.getStartLineNumber(e.originalStart),i=t.getStartColumn(e.originalStart),s=t.getEndLineNumber(e.originalStart+e.originalLength-1),o=t.getEndColumn(e.originalStart+e.originalLength-1),a=n.getStartLineNumber(e.modifiedStart),l=n.getStartColumn(e.modifiedStart),u=n.getEndLineNumber(e.modifiedStart+e.modifiedLength-1),h=n.getEndColumn(e.modifiedStart+e.modifiedLength-1);return new Pr(r,i,s,o,a,l,u,h)}}class Fr{constructor(e,t,n,r,i){this.originalStartLineNumber=e,this.originalEndLineNumber=t,this.modifiedStartLineNumber=n,this.modifiedEndLineNumber=r,this.charChanges=i}static createFromDiffResult(e,t,n,r,i,s,o){let a,l,u,h,c;if(0===t.originalLength?(a=n.getStartLineNumber(t.originalStart)-1,l=0):(a=n.getStartLineNumber(t.originalStart),l=n.getEndLineNumber(t.originalStart+t.originalLength-1)),0===t.modifiedLength?(u=r.getStartLineNumber(t.modifiedStart)-1,h=0):(u=r.getStartLineNumber(t.modifiedStart),h=r.getEndLineNumber(t.modifiedStart+t.modifiedLength-1)),s&&t.originalLength>0&&t.originalLength<20&&t.modifiedLength>0&&t.modifiedLength<20&&i()){const s=n.createCharSequence(e,t.originalStart,t.originalStart+t.originalLength-1),a=r.createCharSequence(e,t.modifiedStart,t.modifiedStart+t.modifiedLength-1);if(s.getElements().length>0&&a.getElements().length>0){let e=Or(s,a,i,!0).changes;o&&(e=function(e){if(e.length<=1)return e;const t=[e[0]];let n=t[0];for(let r=1,i=e.length;r<i;r++){const i=e[r],s=i.originalStart-(n.originalStart+n.originalLength),o=i.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(s,o)<3?(n.originalLength=i.originalStart+i.originalLength-n.originalStart,n.modifiedLength=i.modifiedStart+i.modifiedLength-n.modifiedStart):(t.push(i),n=i)}return t}(e)),c=[];for(let t=0,n=e.length;t<n;t++)c.push(Pr.createFromDiffChange(e[t],s,a))}}return new Fr(a,l,u,h,c)}}class Kr{constructor(e,t,n){this.shouldComputeCharChanges=n.shouldComputeCharChanges,this.shouldPostProcessCharChanges=n.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=n.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=n.shouldMakePrettyDiff,this.originalLines=e,this.modifiedLines=t,this.original=new Ir(e),this.modified=new Ir(t),this.continueLineDiff=qr(n.maxComputationTime),this.continueCharDiff=qr(0===n.maxComputationTime?0:Math.min(n.maxComputationTime,5e3))}computeDiff(){if(1===this.original.lines.length&&0===this.original.lines[0].length)return 1===this.modified.lines.length&&0===this.modified.lines[0].length?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:void 0}]};if(1===this.modified.lines.length&&0===this.modified.lines[0].length)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:void 0}]};const e=Or(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),t=e.changes,n=e.quitEarly;if(this.shouldIgnoreTrimWhitespace){const e=[];for(let n=0,r=t.length;n<r;n++)e.push(Fr.createFromDiffResult(this.shouldIgnoreTrimWhitespace,t[n],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:n,changes:e}}const r=[];let i=0,s=0;for(let e=-1,n=t.length;e<n;e++){const o=e+1<n?t[e+1]:null,a=o?o.originalStart:this.originalLines.length,l=o?o.modifiedStart:this.modifiedLines.length;for(;i<a&&s<l;){const e=this.originalLines[i],t=this.modifiedLines[s];if(e!==t){{let n=Vr(e,1),o=Vr(t,1);for(;n>1&&o>1;){if(e.charCodeAt(n-2)!==t.charCodeAt(o-2))break;n--,o--}(n>1||o>1)&&this._pushTrimWhitespaceCharChange(r,i+1,1,n,s+1,1,o)}{let n=Br(e,1),o=Br(t,1);const a=e.length+1,l=t.length+1;for(;n<a&&o<l;){if(e.charCodeAt(n-1)!==e.charCodeAt(o-1))break;n++,o++}(n<a||o<l)&&this._pushTrimWhitespaceCharChange(r,i+1,n,a,s+1,o,l)}}i++,s++}o&&(r.push(Fr.createFromDiffResult(this.shouldIgnoreTrimWhitespace,o,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),i+=o.originalLength,s+=o.modifiedLength)}return{quitEarly:n,changes:r}}_pushTrimWhitespaceCharChange(e,t,n,r,i,s,o){if(this._mergeTrimWhitespaceCharChange(e,t,n,r,i,s,o))return;let a;this.shouldComputeCharChanges&&(a=[new Pr(t,n,t,r,i,s,i,o)]),e.push(new Fr(t,t,i,i,a))}_mergeTrimWhitespaceCharChange(e,t,n,r,i,s,o){const a=e.length;if(0===a)return!1;const l=e[a-1];return 0!==l.originalEndLineNumber&&0!==l.modifiedEndLineNumber&&(l.originalEndLineNumber===t&&l.modifiedEndLineNumber===i?(this.shouldComputeCharChanges&&l.charChanges&&l.charChanges.push(new Pr(t,n,t,r,i,s,i,o)),!0):l.originalEndLineNumber+1===t&&l.modifiedEndLineNumber+1===i&&(l.originalEndLineNumber=t,l.modifiedEndLineNumber=i,this.shouldComputeCharChanges&&l.charChanges&&l.charChanges.push(new Pr(t,n,t,r,i,s,i,o)),!0))}}function Vr(e,t){const n=function(e){for(let t=0,n=e.length;t<n;t++){const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}return-1}(e);return-1===n?t:n+1}function Br(e,t){const n=function(e,t=e.length-1){for(let n=t;n>=0;n--){const t=e.charCodeAt(n);if(32!==t&&9!==t)return n}return-1}(e);return-1===n?t:n+2}function qr(e){if(0===e)return()=>!0;const t=Date.now();return()=>Date.now()-t<e}class Ur{static addRange(e,t){let n=0;for(;n<t.length&&t[n].endExclusive<e.start;)n++;let r=n;for(;r<t.length&&t[r].start<=e.endExclusive;)r++;if(n===r)t.splice(n,0,e);else{const i=Math.min(e.start,t[n].start),s=Math.max(e.endExclusive,t[r-1].endExclusive);t.splice(n,r-n,new Ur(i,s))}}static tryCreate(e,t){if(!(e>t))return new Ur(e,t)}constructor(e,t){if(this.start=e,this.endExclusive=t,e>t)throw new l(`Invalid range: ${this.toString()}`)}get isEmpty(){return this.start===this.endExclusive}delta(e){return new Ur(this.start+e,this.endExclusive+e)}get length(){return this.endExclusive-this.start}toString(){return`[${this.start}, ${this.endExclusive})`}equals(e){return this.start===e.start&&this.endExclusive===e.endExclusive}containsRange(e){return this.start<=e.start&&e.endExclusive<=this.endExclusive}join(e){return new Ur(Math.min(this.start,e.start),Math.max(this.endExclusive,e.endExclusive))}intersect(e){const t=Math.max(this.start,e.start),n=Math.min(this.endExclusive,e.endExclusive);if(t<=n)return new Ur(t,n)}}class Wr{static trivial(e,t){return new Wr([new Hr(new Ur(0,e.length),new Ur(0,t.length))],!1)}static trivialTimedOut(e,t){return new Wr([new Hr(new Ur(0,e.length),new Ur(0,t.length))],!0)}constructor(e,t){this.diffs=e,this.hitTimeout=t}}class Hr{constructor(e,t){this.seq1Range=e,this.seq2Range=t}reverse(){return new Hr(this.seq2Range,this.seq1Range)}toString(){return`${this.seq1Range} <-> ${this.seq2Range}`}join(e){return new Hr(this.seq1Range.join(e.seq1Range),this.seq2Range.join(e.seq2Range))}}class $r{isValid(){return!0}}$r.instance=new $r;class zr{constructor(e){if(this.timeout=e,this.startTime=Date.now(),this.valid=!0,e<=0)throw new l("timeout must be positive")}isValid(){return!(Date.now()-this.startTime<this.timeout)&&this.valid&&(this.valid=!1),this.valid}}class jr{constructor(e,t){this.width=e,this.height=t,this.array=[],this.array=new Array(e*t)}get(e,t){return this.array[e+t*this.width]}set(e,t,n){this.array[e+t*this.width]=n}}class Gr{compute(e,t,n=$r.instance,r){if(0===e.length||0===t.length)return Wr.trivial(e,t);const i=new jr(e.length,t.length),s=new jr(e.length,t.length),o=new jr(e.length,t.length);for(let a=0;a<e.length;a++)for(let l=0;l<t.length;l++){if(!n.isValid())return Wr.trivialTimedOut(e,t);const u=0===a?0:i.get(a-1,l),h=0===l?0:i.get(a,l-1);let c;e.getElement(a)===t.getElement(l)?(c=0===a||0===l?0:i.get(a-1,l-1),a>0&&l>0&&3===s.get(a-1,l-1)&&(c+=o.get(a-1,l-1)),c+=r?r(a,l):1):c=-1;const d=Math.max(u,h,c);if(d===c){const e=a>0&&l>0?o.get(a-1,l-1):0;o.set(a,l,e+1),s.set(a,l,3)}else d===u?(o.set(a,l,0),s.set(a,l,1)):d===h&&(o.set(a,l,0),s.set(a,l,2));i.set(a,l,d)}const a=[];let l=e.length,u=t.length;function h(e,t){e+1===l&&t+1===u||a.push(new Hr(new Ur(e+1,l),new Ur(t+1,u))),l=e,u=t}let c=e.length-1,d=t.length-1;for(;c>=0&&d>=0;)3===s.get(c,d)?(h(c,d),c--,d--):1===s.get(c,d)?c--:d--;return h(-1,-1),a.reverse(),new Wr(a,!1)}}function Yr(e,t,n){let r=n;return r=function(e,t,n){const r=[];n.length>0&&r.push(n[0]);for(let e=1;e<n.length;e++){const i=r[r.length-1],s=n[e];if(s.seq1Range.isEmpty){let e=!0;const n=s.seq1Range.start-i.seq1Range.endExclusive;for(let r=1;r<=n;r++)if(t.getElement(s.seq2Range.start-r)!==t.getElement(s.seq2Range.endExclusive-r)){e=!1;break}if(e){r[r.length-1]=new Hr(i.seq1Range,new Ur(i.seq2Range.start,s.seq2Range.endExclusive-n));continue}}r.push(s)}return r}(0,t,r),r=function(e,t,n){if(!e.getBoundaryScore||!t.getBoundaryScore)return n;for(let r=0;r<n.length;r++){const i=n[r];if(i.seq1Range.isEmpty){const s=r>0?n[r-1].seq2Range.endExclusive:-1,o=r+1<n.length?n[r+1].seq2Range.start:t.length;n[r]=Jr(i,e,t,o,s)}else if(i.seq2Range.isEmpty){const s=r>0?n[r-1].seq1Range.endExclusive:-1,o=r+1<n.length?n[r+1].seq1Range.start:e.length;n[r]=Jr(i.reverse(),t,e,o,s).reverse()}}return n}(e,t,r),r}function Jr(e,t,n,r,i){let s=1;for(;e.seq2Range.start-s>i&&n.getElement(e.seq2Range.start-s)===n.getElement(e.seq2Range.endExclusive-s)&&s<20;)s++;s--;let o=0;for(;e.seq2Range.start+o<r&&n.getElement(e.seq2Range.start+o)===n.getElement(e.seq2Range.endExclusive+o)&&o<20;)o++;if(0===s&&0===o)return e;let a=0,l=-1;for(let r=-s;r<=o;r++){const i=e.seq2Range.start+r,s=e.seq2Range.endExclusive+r,o=e.seq1Range.start+r,u=t.getBoundaryScore(o)+n.getBoundaryScore(i)+n.getBoundaryScore(s);u>l&&(l=u,a=r)}return 0!==a?new Hr(e.seq1Range.delta(a),e.seq2Range.delta(a)):e}class Qr{compute(e,t,n=$r.instance){if(0===e.length||0===t.length)return Wr.trivial(e,t);function r(n,r){for(;n<e.length&&r<t.length&&e.getElement(n)===t.getElement(r);)n++,r++;return n}let i=0;const s=new Zr;s.set(0,r(0,0));const o=new ei;o.set(0,0===s.get(0)?null:new Xr(null,0,0,s.get(0)));let a=0;e:for(;;)for(i++,a=-i;a<=i;a+=2){if(!n.isValid())return Wr.trivialTimedOut(e,t);const l=a===i?-1:s.get(a+1),u=a===-i?-1:s.get(a-1)+1,h=Math.min(Math.max(l,u),e.length),c=h-a,d=r(h,c);s.set(a,d);const g=h===l?o.get(a+1):o.get(a-1);if(o.set(a,d!==h?new Xr(g,h,c,d-h):g),s.get(a)===e.length&&s.get(a)-a===t.length)break e}let l=o.get(a);const u=[];let h=e.length,c=t.length;for(;;){const e=l?l.x+l.length:0,t=l?l.y+l.length:0;if(e===h&&t===c||u.push(new Hr(new Ur(e,h),new Ur(t,c))),!l)break;h=l.x,c=l.y,l=l.prev}return u.reverse(),new Wr(u,!1)}}class Xr{constructor(e,t,n,r){this.prev=e,this.x=t,this.y=n,this.length=r}}class Zr{constructor(){this.positiveArr=new Int32Array(10),this.negativeArr=new Int32Array(10)}get(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]}set(e,t){if(e<0){if((e=-e-1)>=this.negativeArr.length){const e=this.negativeArr;this.negativeArr=new Int32Array(2*e.length),this.negativeArr.set(e)}this.negativeArr[e]=t}else{if(e>=this.positiveArr.length){const e=this.positiveArr;this.positiveArr=new Int32Array(2*e.length),this.positiveArr.set(e)}this.positiveArr[e]=t}}}class ei{constructor(){this.positiveArr=[],this.negativeArr=[]}get(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]}set(e,t){e<0?(e=-e-1,this.negativeArr[e]=t):this.positiveArr[e]=t}}class ti{constructor(e,t){this.trimmedHash=e,this.lines=t}getElement(e){return this.trimmedHash[e]}get length(){return this.trimmedHash.length}getBoundaryScore(e){return 1e3-((0===e?0:ni(this.lines[e-1]))+(e===this.lines.length?0:ni(this.lines[e])))}}function ni(e){let t=0;for(;t<e.length&&(32===e.charCodeAt(t)||9===e.charCodeAt(t));)t++;return t}class ri{constructor(e,t,n){this.lines=e,this.considerWhitespaceChanges=n,this.elements=[],this.firstCharOffsetByLineMinusOne=[],this.offsetByLine=[];let r=!1;t.start>0&&t.endExclusive>=e.length&&(t=new Ur(t.start-1,t.endExclusive),r=!0),this.lineRange=t;for(let t=this.lineRange.start;t<this.lineRange.endExclusive;t++){let i=e[t],s=0;if(r)s=i.length,i="",r=!1;else if(!n){const e=i.trimStart();s=i.length-e.length,i=e.trimEnd()}this.offsetByLine.push(s);for(let e=0;e<i.length;e++)this.elements.push(i.charCodeAt(e));t<e.length-1&&(this.elements.push("\n".charCodeAt(0)),this.firstCharOffsetByLineMinusOne[t-this.lineRange.start]=this.elements.length)}this.offsetByLine.push(0)}toString(){return`Slice: "${this.text}"`}get text(){return[...this.elements].map((e=>String.fromCharCode(e))).join("")}getElement(e){return this.elements[e]}get length(){return this.elements.length}getBoundaryScore(e){const t=ai(e>0?this.elements[e-1]:-1),n=ai(e<this.elements.length?this.elements[e]:-1);if(6===t&&7===n)return 0;let r=0;return t!==n&&(r+=10,1===n&&(r+=1)),r+=oi(t),r+=oi(n),r}translateOffset(e){if(this.lineRange.isEmpty)return new kt(this.lineRange.start+1,1);let t=0,n=this.firstCharOffsetByLineMinusOne.length;for(;t<n;){const r=Math.floor((t+n)/2);this.firstCharOffsetByLineMinusOne[r]>e?n=r:t=r+1}const r=0===t?0:this.firstCharOffsetByLineMinusOne[t-1];return new kt(this.lineRange.start+t+1,e-r+1+this.offsetByLine[t])}translateRange(e){return Tt.fromPositions(this.translateOffset(e.start),this.translateOffset(e.endExclusive))}findWordContaining(e){if(e<0||e>=this.elements.length)return;if(!ii(this.elements[e]))return;let t=e;for(;t>0&&ii(this.elements[t-1]);)t--;let n=e;for(;n<this.elements.length&&ii(this.elements[n]);)n++;return new Ur(t,n)}}function ii(e){return e>=97&&e<=122||e>=65&&e<=90||e>=48&&e<=57}const si={0:0,1:0,2:0,3:10,4:2,5:3,6:10,7:10};function oi(e){return si[e]}function ai(e){return 10===e?7:13===e?6:function(e){return 32===e||9===e}(e)?5:e>=97&&e<=122?0:e>=65&&e<=90?1:e>=48&&e<=57?2:-1===e?3:4}const li={legacy:new class{computeDiff(e,t,n){var r;const i=new Kr(e,t,{maxComputationTime:n.maxComputationTimeMs,shouldIgnoreTrimWhitespace:n.ignoreTrimWhitespace,shouldComputeCharChanges:!0,shouldMakePrettyDiff:!0,shouldPostProcessCharChanges:!0}).computeDiff(),s=[];let o=null;for(const e of i.changes){let t,n;t=0===e.originalEndLineNumber?new xr(e.originalStartLineNumber+1,e.originalStartLineNumber+1):new xr(e.originalStartLineNumber,e.originalEndLineNumber+1),n=0===e.modifiedEndLineNumber?new xr(e.modifiedStartLineNumber+1,e.modifiedStartLineNumber+1):new xr(e.modifiedStartLineNumber,e.modifiedEndLineNumber+1);let i=new kr(t,n,null===(r=e.charChanges)||void 0===r?void 0:r.map((e=>new Tr(new Tt(e.originalStartLineNumber,e.originalStartColumn,e.originalEndLineNumber,e.originalEndColumn),new Tt(e.modifiedStartLineNumber,e.modifiedStartColumn,e.modifiedEndLineNumber,e.modifiedEndColumn)))));o&&(o.modifiedRange.endLineNumberExclusive!==i.modifiedRange.startLineNumber&&o.originalRange.endLineNumberExclusive!==i.originalRange.startLineNumber||(i=new kr(o.originalRange.join(i.originalRange),o.modifiedRange.join(i.modifiedRange),o.innerChanges&&i.innerChanges?o.innerChanges.concat(i.innerChanges):void 0),s.pop())),s.push(i),o=i}return Sr((()=>Nr(s,((e,t)=>t.originalRange.startLineNumber-e.originalRange.endLineNumberExclusive==t.modifiedRange.startLineNumber-e.modifiedRange.endLineNumberExclusive&&e.originalRange.endLineNumberExclusive<t.originalRange.startLineNumber&&e.modifiedRange.endLineNumberExclusive<t.modifiedRange.startLineNumber)))),new Mr(s,i.quitEarly)}},advanced:new class{constructor(){this.dynamicProgrammingDiffing=new Gr,this.myersDiffingAlgorithm=new Qr}computeDiff(e,t,n){const r=0===n.maxComputationTimeMs?$r.instance:new zr(n.maxComputationTimeMs),i=!n.ignoreTrimWhitespace,s=new Map;function o(e){let t=s.get(e);return void 0===t&&(t=s.size,s.set(e,t)),t}const a=e.map((e=>o(e.trim()))),l=t.map((e=>o(e.trim()))),u=new ti(a,e),h=new ti(l,t),c=(()=>u.length+h.length<1500?this.dynamicProgrammingDiffing.compute(u,h,r,((n,r)=>e[n]===t[r]?0===t[r].length?.1:1+Math.log(1+t[r].length):.99)):this.myersDiffingAlgorithm.compute(u,h))();let d=c.diffs,g=c.hitTimeout;d=Yr(u,h,d);const m=[],f=n=>{if(i)for(let s=0;s<n;s++){const n=p+s,o=b+s;if(e[n]!==t[o]){const s=this.refineDiff(e,t,new Hr(new Ur(n,n+1),new Ur(o,o+1)),r,i);for(const e of s.mappings)m.push(e);s.hitTimeout&&(g=!0)}}};let p=0,b=0;for(const n of d){Sr((()=>n.seq1Range.start-p==n.seq2Range.start-b));f(n.seq1Range.start-p),p=n.seq1Range.endExclusive,b=n.seq2Range.endExclusive;const s=this.refineDiff(e,t,n,r,i);s.hitTimeout&&(g=!0);for(const e of s.mappings)m.push(e)}f(e.length-p);const _=function(e,t,n){const r=[];for(const i of function*(e,t){let n,r;for(const i of e)void 0!==r&&t(r,i)?n.push(i):(n&&(yield n),n=[i]),r=i;n&&(yield n)}(e.map((e=>function(e,t,n){let r=0,i=0;e.modifiedRange.startColumn-1>=n[e.modifiedRange.startLineNumber-1].length&&e.originalRange.startColumn-1>=t[e.originalRange.startLineNumber-1].length&&(r=1);1===e.modifiedRange.endColumn&&1===e.originalRange.endColumn&&e.originalRange.startLineNumber+r<=e.originalRange.endLineNumber&&e.modifiedRange.startLineNumber+r<=e.modifiedRange.endLineNumber&&(i=-1);const s=new xr(e.originalRange.startLineNumber+r,e.originalRange.endLineNumber+1+i),o=new xr(e.modifiedRange.startLineNumber+r,e.modifiedRange.endLineNumber+1+i);return new kr(s,o,[e])}(e,t,n))),((e,t)=>e.originalRange.overlapOrTouch(t.originalRange)||e.modifiedRange.overlapOrTouch(t.modifiedRange)))){const e=i[0],t=i[i.length-1];r.push(new kr(e.originalRange.join(t.originalRange),e.modifiedRange.join(t.modifiedRange),i.map((e=>e.innerChanges[0]))))}return Sr((()=>Nr(r,((e,t)=>t.originalRange.startLineNumber-e.originalRange.endLineNumberExclusive==t.modifiedRange.startLineNumber-e.modifiedRange.endLineNumberExclusive&&e.originalRange.endLineNumberExclusive<t.originalRange.startLineNumber&&e.modifiedRange.endLineNumberExclusive<t.modifiedRange.startLineNumber)))),r}(m,e,t);return new Mr(_,g)}refineDiff(e,t,n,r,i){const s=new ri(e,n.seq1Range,i),o=new ri(t,n.seq2Range,i),a=s.length+o.length<500?this.dynamicProgrammingDiffing.compute(s,o,r):this.myersDiffingAlgorithm.compute(s,o,r);let l=a.diffs;l=Yr(s,o,l),l=function(e,t,n){const r=[];let i;function s(){if(!i)return;const e=i.s1Range.length-i.deleted;i.s2Range.length,i.added;Math.max(i.deleted,i.added)+(i.count-1)>e&&r.push(new Hr(i.s1Range,i.s2Range)),i=void 0}for(const o of n){function a(e,t){var n,r,a,l;if(!i||!i.s1Range.containsRange(e)||!i.s2Range.containsRange(t))if(!i||i.s1Range.endExclusive<e.start&&i.s2Range.endExclusive<t.start)s(),i={added:0,deleted:0,count:0,s1Range:e,s2Range:t};else{const s=Ur.tryCreate(i.s1Range.endExclusive,e.start),o=Ur.tryCreate(i.s2Range.endExclusive,t.start);i.deleted+=null!==(n=null==s?void 0:s.length)&&void 0!==n?n:0,i.added+=null!==(r=null==o?void 0:o.length)&&void 0!==r?r:0,i.s1Range=i.s1Range.join(e),i.s2Range=i.s2Range.join(t)}const u=e.intersect(o.seq1Range),h=t.intersect(o.seq2Range);i.count++,i.deleted+=null!==(a=null==u?void 0:u.length)&&void 0!==a?a:0,i.added+=null!==(l=null==h?void 0:h.length)&&void 0!==l?l:0}const l=e.findWordContaining(o.seq1Range.start-1),u=t.findWordContaining(o.seq2Range.start-1),h=e.findWordContaining(o.seq1Range.endExclusive),c=t.findWordContaining(o.seq2Range.endExclusive);l&&h&&u&&c&&l.equals(h)&&u.equals(c)?a(l,u):(l&&u&&a(l,u),h&&c&&a(h,c))}s();return function(e,t){const n=[];for(;e.length>0||t.length>0;){const r=e[0],i=t[0];let s;s=r&&(!i||r.seq1Range.start<i.seq1Range.start)?e.shift():t.shift(),n.length>0&&n[n.length-1].seq1Range.endExclusive>=s.seq1Range.start?n[n.length-1]=n[n.length-1].join(s):n.push(s)}return n}(n,r)}(s,o,l),l=function(e,t,n){const r=[];for(const e of n){const t=r[r.length-1];t&&(e.seq1Range.start-t.seq1Range.endExclusive<=2||e.seq2Range.start-t.seq2Range.endExclusive<=2)?r[r.length-1]=new Hr(t.seq1Range.join(e.seq1Range),t.seq2Range.join(e.seq2Range)):r.push(e)}return r}(0,0,l);return{mappings:l.map((e=>new Tr(s.translateRange(e.seq1Range),o.translateRange(e.seq2Range)))),hitTimeout:a.hitTimeout}}}};function ui(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}class hi{constructor(e,t,n,r=1){this._rgbaBrand=void 0,this.r=0|Math.min(255,Math.max(0,e)),this.g=0|Math.min(255,Math.max(0,t)),this.b=0|Math.min(255,Math.max(0,n)),this.a=ui(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.r===t.r&&e.g===t.g&&e.b===t.b&&e.a===t.a}}class ci{constructor(e,t,n,r){this._hslaBrand=void 0,this.h=0|Math.max(Math.min(360,e),0),this.s=ui(Math.max(Math.min(1,t),0),3),this.l=ui(Math.max(Math.min(1,n),0),3),this.a=ui(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.h===t.h&&e.s===t.s&&e.l===t.l&&e.a===t.a}static fromRGBA(e){const t=e.r/255,n=e.g/255,r=e.b/255,i=e.a,s=Math.max(t,n,r),o=Math.min(t,n,r);let a=0,l=0;const u=(o+s)/2,h=s-o;if(h>0){switch(l=Math.min(u<=.5?h/(2*u):h/(2-2*u),1),s){case t:a=(n-r)/h+(n<r?6:0);break;case n:a=(r-t)/h+2;break;case r:a=(t-n)/h+4}a*=60,a=Math.round(a)}return new ci(a,l,u,i)}static _hue2rgb(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}static toRGBA(e){const t=e.h/360,{s:n,l:r,a:i}=e;let s,o,a;if(0===n)s=o=a=r;else{const e=r<.5?r*(1+n):r+n-r*n,i=2*r-e;s=ci._hue2rgb(i,e,t+1/3),o=ci._hue2rgb(i,e,t),a=ci._hue2rgb(i,e,t-1/3)}return new hi(Math.round(255*s),Math.round(255*o),Math.round(255*a),i)}}class di{constructor(e,t,n,r){this._hsvaBrand=void 0,this.h=0|Math.max(Math.min(360,e),0),this.s=ui(Math.max(Math.min(1,t),0),3),this.v=ui(Math.max(Math.min(1,n),0),3),this.a=ui(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.h===t.h&&e.s===t.s&&e.v===t.v&&e.a===t.a}static fromRGBA(e){const t=e.r/255,n=e.g/255,r=e.b/255,i=Math.max(t,n,r),s=i-Math.min(t,n,r),o=0===i?0:s/i;let a;return a=0===s?0:i===t?((n-r)/s%6+6)%6:i===n?(r-t)/s+2:(t-n)/s+4,new di(Math.round(60*a),o,i,e.a)}static toRGBA(e){const{h:t,s:n,v:r,a:i}=e,s=r*n,o=s*(1-Math.abs(t/60%2-1)),a=r-s;let[l,u,h]=[0,0,0];return t<60?(l=s,u=o):t<120?(l=o,u=s):t<180?(u=s,h=o):t<240?(u=o,h=s):t<300?(l=o,h=s):t<=360&&(l=s,h=o),l=Math.round(255*(l+a)),u=Math.round(255*(u+a)),h=Math.round(255*(h+a)),new hi(l,u,h,i)}}class gi{static fromHex(e){return gi.Format.CSS.parseHex(e)||gi.red}static equals(e,t){return!e&&!t||!(!e||!t)&&e.equals(t)}get hsla(){return this._hsla?this._hsla:ci.fromRGBA(this.rgba)}get hsva(){return this._hsva?this._hsva:di.fromRGBA(this.rgba)}constructor(e){if(!e)throw new Error("Color needs a value");if(e instanceof hi)this.rgba=e;else if(e instanceof ci)this._hsla=e,this.rgba=ci.toRGBA(e);else{if(!(e instanceof di))throw new Error("Invalid color ctor argument");this._hsva=e,this.rgba=di.toRGBA(e)}}equals(e){return!!e&&hi.equals(this.rgba,e.rgba)&&ci.equals(this.hsla,e.hsla)&&di.equals(this.hsva,e.hsva)}getRelativeLuminance(){return ui(.2126*gi._relativeLuminanceForComponent(this.rgba.r)+.7152*gi._relativeLuminanceForComponent(this.rgba.g)+.0722*gi._relativeLuminanceForComponent(this.rgba.b),4)}static _relativeLuminanceForComponent(e){const t=e/255;return t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4)}isLighter(){return(299*this.rgba.r+587*this.rgba.g+114*this.rgba.b)/1e3>=128}isLighterThan(e){return this.getRelativeLuminance()>e.getRelativeLuminance()}isDarkerThan(e){return this.getRelativeLuminance()<e.getRelativeLuminance()}lighten(e){return new gi(new ci(this.hsla.h,this.hsla.s,this.hsla.l+this.hsla.l*e,this.hsla.a))}darken(e){return new gi(new ci(this.hsla.h,this.hsla.s,this.hsla.l-this.hsla.l*e,this.hsla.a))}transparent(e){const{r:t,g:n,b:r,a:i}=this.rgba;return new gi(new hi(t,n,r,i*e))}isTransparent(){return 0===this.rgba.a}isOpaque(){return 1===this.rgba.a}opposite(){return new gi(new hi(255-this.rgba.r,255-this.rgba.g,255-this.rgba.b,this.rgba.a))}makeOpaque(e){if(this.isOpaque()||1!==e.rgba.a)return this;const{r:t,g:n,b:r,a:i}=this.rgba;return new gi(new hi(e.rgba.r-i*(e.rgba.r-t),e.rgba.g-i*(e.rgba.g-n),e.rgba.b-i*(e.rgba.b-r),1))}toString(){return this._toString||(this._toString=gi.Format.CSS.format(this)),this._toString}static getLighterColor(e,t,n){if(e.isLighterThan(t))return e;n=n||.5;const r=e.getRelativeLuminance(),i=t.getRelativeLuminance();return n=n*(i-r)/i,e.lighten(n)}static getDarkerColor(e,t,n){if(e.isDarkerThan(t))return e;n=n||.5;const r=e.getRelativeLuminance();return n=n*(r-t.getRelativeLuminance())/r,e.darken(n)}}function mi(e){const t=[];for(const n of e){const e=Number(n);(e||0===e&&""!==n.replace(/\s/g,""))&&t.push(e)}return t}function fi(e,t,n,r){return{red:e/255,blue:n/255,green:t/255,alpha:r}}function pi(e,t){const n=t.index,r=t[0].length;if(!n)return;const i=e.positionAt(n);return{startLineNumber:i.lineNumber,startColumn:i.column,endLineNumber:i.lineNumber,endColumn:i.column+r}}function bi(e,t){if(!e)return;const n=gi.Format.CSS.parseHex(t);return n?{range:e,color:fi(n.rgba.r,n.rgba.g,n.rgba.b,n.rgba.a)}:void 0}function _i(e,t,n){if(!e||1!==t.length)return;const r=mi(t[0].values());return{range:e,color:fi(r[0],r[1],r[2],n?r[3]:1)}}function Ci(e,t,n){if(!e||1!==t.length)return;const r=mi(t[0].values()),i=new gi(new ci(r[0],r[1]/100,r[2]/100,n?r[3]:1));return{range:e,color:fi(i.rgba.r,i.rgba.g,i.rgba.b,i.rgba.a)}}function yi(e,t){return"string"==typeof e?[...e.matchAll(t)]:e.findMatches(t)}function vi(e){return e&&"function"==typeof e.getValue&&"function"==typeof e.positionAt?function(e){const t=[],n=yi(e,/\b(rgb|rgba|hsl|hsla)(\([0-9\s,.\%]*\))|(#)([A-Fa-f0-9]{6})\b|(#)([A-Fa-f0-9]{8})\b/gm);if(n.length>0)for(const r of n){const n=r.filter((e=>void 0!==e)),i=n[1],s=n[2];if(!s)continue;let o;if("rgb"===i){const t=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*\)$/gm;o=_i(pi(e,r),yi(s,t),!1)}else if("rgba"===i){const t=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;o=_i(pi(e,r),yi(s,t),!0)}else if("hsl"===i){const t=/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*\)$/gm;o=Ci(pi(e,r),yi(s,t),!1)}else if("hsla"===i){const t=/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;o=Ci(pi(e,r),yi(s,t),!0)}else"#"===i&&(o=bi(pi(e,r),i+s));o&&t.push(o)}return t}(e):[]}gi.white=new gi(new hi(255,255,255,1)),gi.black=new gi(new hi(0,0,0,1)),gi.red=new gi(new hi(255,0,0,1)),gi.blue=new gi(new hi(0,0,255,1)),gi.green=new gi(new hi(0,255,0,1)),gi.cyan=new gi(new hi(0,255,255,1)),gi.lightgrey=new gi(new hi(211,211,211,1)),gi.transparent=new gi(new hi(0,0,0,0)),function(e){let t;!function(t){let n;!function(t){function n(e){const t=e.toString(16);return 2!==t.length?"0"+t:t}function r(e){switch(e){case 48:return 0;case 49:return 1;case 50:return 2;case 51:return 3;case 52:return 4;case 53:return 5;case 54:return 6;case 55:return 7;case 56:return 8;case 57:return 9;case 97:case 65:return 10;case 98:case 66:return 11;case 99:case 67:return 12;case 100:case 68:return 13;case 101:case 69:return 14;case 102:case 70:return 15}return 0}t.formatRGB=function(t){return 1===t.rgba.a?`rgb(${t.rgba.r}, ${t.rgba.g}, ${t.rgba.b})`:e.Format.CSS.formatRGBA(t)},t.formatRGBA=function(e){return`rgba(${e.rgba.r}, ${e.rgba.g}, ${e.rgba.b}, ${+e.rgba.a.toFixed(2)})`},t.formatHSL=function(t){return 1===t.hsla.a?`hsl(${t.hsla.h}, ${(100*t.hsla.s).toFixed(2)}%, ${(100*t.hsla.l).toFixed(2)}%)`:e.Format.CSS.formatHSLA(t)},t.formatHSLA=function(e){return`hsla(${e.hsla.h}, ${(100*e.hsla.s).toFixed(2)}%, ${(100*e.hsla.l).toFixed(2)}%, ${e.hsla.a.toFixed(2)})`},t.formatHex=function(e){return`#${n(e.rgba.r)}${n(e.rgba.g)}${n(e.rgba.b)}`},t.formatHexA=function(t,r=!1){return r&&1===t.rgba.a?e.Format.CSS.formatHex(t):`#${n(t.rgba.r)}${n(t.rgba.g)}${n(t.rgba.b)}${n(Math.round(255*t.rgba.a))}`},t.format=function(t){return t.isOpaque()?e.Format.CSS.formatHex(t):e.Format.CSS.formatRGBA(t)},t.parseHex=function(t){const n=t.length;if(0===n)return null;if(35!==t.charCodeAt(0))return null;if(7===n){const n=16*r(t.charCodeAt(1))+r(t.charCodeAt(2)),i=16*r(t.charCodeAt(3))+r(t.charCodeAt(4)),s=16*r(t.charCodeAt(5))+r(t.charCodeAt(6));return new e(new hi(n,i,s,1))}if(9===n){const n=16*r(t.charCodeAt(1))+r(t.charCodeAt(2)),i=16*r(t.charCodeAt(3))+r(t.charCodeAt(4)),s=16*r(t.charCodeAt(5))+r(t.charCodeAt(6)),o=16*r(t.charCodeAt(7))+r(t.charCodeAt(8));return new e(new hi(n,i,s,o/255))}if(4===n){const n=r(t.charCodeAt(1)),i=r(t.charCodeAt(2)),s=r(t.charCodeAt(3));return new e(new hi(16*n+n,16*i+i,16*s+s))}if(5===n){const n=r(t.charCodeAt(1)),i=r(t.charCodeAt(2)),s=r(t.charCodeAt(3)),o=r(t.charCodeAt(4));return new e(new hi(16*n+n,16*i+i,16*s+s,(16*o+o)/255))}return null}}(n=t.CSS||(t.CSS={}))}(t=e.Format||(e.Format={}))}(gi||(gi={}));var wi=function(e,t,n,r){return new(n||(n=Promise))((function(i,s){function o(e){try{l(r.next(e))}catch(e){s(e)}}function a(e){try{l(r.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,a)}l((r=r.apply(e,t||[])).next())}))};class Li extends Vt{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}findMatches(e){const t=[];for(let n=0;n<this._lines.length;n++){const r=this._lines[n],i=this.offsetAt(new kt(n+1,1)),s=r.matchAll(e);for(const e of s)(e.index||0===e.index)&&(e.index=e.index+i),t.push(e)}return t}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(e){return this._lines[e-1]}getWordAtPosition(e,t){const n=Ut(e.column,function(e){let t=Bt;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}(t),this._lines[e.lineNumber-1],0);return n?new Tt(e.lineNumber,n.startColumn,e.lineNumber,n.endColumn):null}words(e){const t=this._lines,n=this._wordenize.bind(this);let r=0,i="",s=0,o=[];return{*[Symbol.iterator](){for(;;)if(s<o.length){const e=i.substring(o[s].start,o[s].end);s+=1,yield e}else{if(!(r<t.length))break;i=t[r],o=n(i,e),s=0,r+=1}}}}getLineWords(e,t){const n=this._lines[e-1],r=this._wordenize(n,t),i=[];for(const e of r)i.push({word:n.substring(e.start,e.end),startColumn:e.start+1,endColumn:e.end+1});return i}_wordenize(e,t){const n=[];let r;for(t.lastIndex=0;(r=t.exec(e))&&0!==r[0].length;)n.push({start:r.index,end:r.index+r[0].length});return n}getValueInRange(e){if((e=this._validateRange(e)).startLineNumber===e.endLineNumber)return this._lines[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);const t=this._eol,n=e.startLineNumber-1,r=e.endLineNumber-1,i=[];i.push(this._lines[n].substring(e.startColumn-1));for(let e=n+1;e<r;e++)i.push(this._lines[e]);return i.push(this._lines[r].substring(0,e.endColumn-1)),i.join(t)}offsetAt(e){return e=this._validatePosition(e),this._ensureLineStarts(),this._lineStarts.getPrefixSum(e.lineNumber-2)+(e.column-1)}positionAt(e){e=Math.floor(e),e=Math.max(0,e),this._ensureLineStarts();const t=this._lineStarts.getIndexOf(e),n=this._lines[t.index].length;return{lineNumber:1+t.index,column:1+Math.min(t.remainder,n)}}_validateRange(e){const t=this._validatePosition({lineNumber:e.startLineNumber,column:e.startColumn}),n=this._validatePosition({lineNumber:e.endLineNumber,column:e.endColumn});return t.lineNumber!==e.startLineNumber||t.column!==e.startColumn||n.lineNumber!==e.endLineNumber||n.column!==e.endColumn?{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}:e}_validatePosition(e){if(!kt.isIPosition(e))throw new Error("bad position");let{lineNumber:t,column:n}=e,r=!1;if(t<1)t=1,n=1,r=!0;else if(t>this._lines.length)t=this._lines.length,n=this._lines[t-1].length+1,r=!0;else{const e=this._lines[t-1].length+1;n<1?(n=1,r=!0):n>e&&(n=e,r=!0)}return r?{lineNumber:t,column:n}:e}}class Si{constructor(e,t){this._host=e,this._models=Object.create(null),this._foreignModuleFactory=t,this._foreignModule=null}dispose(){this._models=Object.create(null)}_getModel(e){return this._models[e]}_getModels(){const e=[];return Object.keys(this._models).forEach((t=>e.push(this._models[t]))),e}acceptNewModel(e){this._models[e.url]=new Li(yt.parse(e.url),e.lines,e.EOL,e.versionId)}acceptModelChanged(e,t){if(!this._models[e])return;this._models[e].onEvents(t)}acceptRemovedModel(e){this._models[e]&&delete this._models[e]}computeUnicodeHighlights(e,t,n){return wi(this,void 0,void 0,(function*(){const r=this._getModel(e);return r?Er.computeUnicodeHighlights(r,t,n):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}}))}computeDiff(e,t,n,r){return wi(this,void 0,void 0,(function*(){const i=this._getModel(e),s=this._getModel(t);return i&&s?Si.computeDiff(i,s,n,r):null}))}static computeDiff(e,t,n,r){const i="advanced"===r?li.advanced:li.legacy,s=e.getLinesContent(),o=t.getLinesContent(),a=i.computeDiff(s,o,n);return{identical:!(a.changes.length>0)&&this._modelsAreIdentical(e,t),quitEarly:a.hitTimeout,changes:a.changes.map((e=>{var t;return[e.originalRange.startLineNumber,e.originalRange.endLineNumberExclusive,e.modifiedRange.startLineNumber,e.modifiedRange.endLineNumberExclusive,null===(t=e.innerChanges)||void 0===t?void 0:t.map((e=>[e.originalRange.startLineNumber,e.originalRange.startColumn,e.originalRange.endLineNumber,e.originalRange.endColumn,e.modifiedRange.startLineNumber,e.modifiedRange.startColumn,e.modifiedRange.endLineNumber,e.modifiedRange.endColumn]))]}))}}static _modelsAreIdentical(e,t){const n=e.getLineCount();if(n!==t.getLineCount())return!1;for(let r=1;r<=n;r++){if(e.getLineContent(r)!==t.getLineContent(r))return!1}return!0}computeMoreMinimalEdits(e,t,n){return wi(this,void 0,void 0,(function*(){const r=this._getModel(e);if(!r)return t;const i=[];let s;t=t.slice(0).sort(((e,t)=>{if(e.range&&t.range)return Tt.compareRangesUsingStarts(e.range,t.range);return(e.range?0:1)-(t.range?0:1)}));for(let{range:e,text:o,eol:a}of t){if("number"==typeof a&&(s=a),Tt.isEmpty(e)&&!o)continue;const t=r.getValueInRange(e);if(o=o.replace(/\r\n|\n|\r/g,r.eol),t===o)continue;if(Math.max(o.length,t.length)>Si._diffLimit){i.push({range:e,text:o});continue}const l=We(t,o,n),u=r.offsetAt(Tt.lift(e).getStartPosition());for(const e of l){const t=r.positionAt(u+e.originalStart),n=r.positionAt(u+e.originalStart+e.originalLength),s={text:o.substr(e.modifiedStart,e.modifiedLength),range:{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}};r.getValueInRange(s.range)!==s.text&&i.push(s)}}return"number"==typeof s&&i.push({eol:s,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),i}))}computeLinks(e){return wi(this,void 0,void 0,(function*(){const t=this._getModel(e);return t?function(e){return e&&"function"==typeof e.getLineCount&&"function"==typeof e.getLineContent?Yt.computeLinks(e):[]}(t):null}))}computeDefaultDocumentColors(e){return wi(this,void 0,void 0,(function*(){const t=this._getModel(e);return t?vi(t):null}))}textualSuggest(e,t,n,r){return wi(this,void 0,void 0,(function*(){const i=new te(!0),s=new RegExp(n,r),o=new Set;e:for(const n of e){const e=this._getModel(n);if(e)for(const n of e.words(s))if(n!==t&&isNaN(Number(n))&&(o.add(n),o.size>Si._suggestionsLimit))break e}return{words:Array.from(o),duration:i.elapsed()}}))}computeWordRanges(e,t,n,r){return wi(this,void 0,void 0,(function*(){const i=this._getModel(e);if(!i)return Object.create(null);const s=new RegExp(n,r),o=Object.create(null);for(let e=t.startLineNumber;e<t.endLineNumber;e++){const t=i.getLineWords(e,s);for(const n of t){if(!isNaN(Number(n.word)))continue;let t=o[n.word];t||(t=[],o[n.word]=t),t.push({startLineNumber:e,startColumn:n.startColumn,endLineNumber:e,endColumn:n.endColumn})}}return o}))}navigateValueSet(e,t,n,r,i){return wi(this,void 0,void 0,(function*(){const s=this._getModel(e);if(!s)return null;const o=new RegExp(r,i);t.startColumn===t.endColumn&&(t={startLineNumber:t.startLineNumber,startColumn:t.startColumn,endLineNumber:t.endLineNumber,endColumn:t.endColumn+1});const a=s.getValueInRange(t),l=s.getWordAtPosition({lineNumber:t.startLineNumber,column:t.startColumn},o);if(!l)return null;const u=s.getValueInRange(l);return Jt.INSTANCE.navigateValueSet(t,a,l,u,n)}))}loadForeignModule(e,t,n){const r=function(e,t){const n=e=>function(){const n=Array.prototype.slice.call(arguments,0);return t(e,n)},r={};for(const t of e)r[t]=n(t);return r}(n,((e,t)=>this._host.fhr(e,t))),i={host:r,getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(i,t),Promise.resolve(ce(this._foreignModule))):Promise.reject(new Error("Unexpected usage"))}fmr(e,t){if(!this._foreignModule||"function"!=typeof this._foreignModule[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._foreignModule[e].apply(this._foreignModule,t))}catch(e){return Promise.reject(e)}}}Si._diffLimit=1e5,Si._suggestionsLimit=1e4,"function"==typeof importScripts&&(globalThis.monaco={editor:void 0,languages:void 0,CancellationTokenSource:en,Emitter:ae,KeyCode:Yn,KeyMod:fr,Position:kt,Range:Tt,Selection:mn,SelectionDirection:ar,MarkerSeverity:Jn,MarkerTag:Qn,Uri:yt,Token:Rn});let Ni=!1;globalThis.onmessage=e=>{Ni||function(e){if(Ni)return;Ni=!0;const t=new Ie((e=>{globalThis.postMessage(e)}),(t=>new Si(t,e)));globalThis.onmessage=e=>{t.onmessage(e.data)}}(null)}})()})();