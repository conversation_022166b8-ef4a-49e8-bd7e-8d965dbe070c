<?php

use App\Models\User;
use App\Models\Cell;
use App\Models\District;
use App\Models\Sector;
use App\Models\Village;
use App\Models\Province;

use Laravel\Sanctum\Sanctum;

beforeEach(function () {

    $this->user = User::factory()->create();

    $this->province = Province::factory()->create();
    $this->district = District::factory()->create(['province_id' => $this->province->id]);
    $this->sector = Sector::factory()->create(['district_id' => $this->district->id]);
    $this->cell = Cell::factory()->create(['sector_id' => $this->sector->id]);
    $this->village = Village::factory()->create(['cell_id' => $this->cell->id]);

    Sanctum::actingAs($this->user);

    $this->url = '/map/navigation';
});

it('can get navigation', function (array $payload) {

    $response = $this->postJson(route('navigation.getNavigation'), $payload);

    dd($response->json());
})->with([
    function () {

         return [
            'from' => [
                'latitude' => -1.9981714,
                'longitude' => 30.0828833
            ],
            'to' => [
                'latitude' => -1.9925831,
                'longitude' => 30.0432624
            ],
            'throught' => [
                [
                    'latitude' => -2.4806021,
                    'longitude' => 29.8664243,
                    'index' => 0
                ],
                [
                    'latitude' => -2.0000525003020897,
                    'longitude' => 30.069221160820376,
                    'index' => 1
                ]
            ],
            'mode' => 'car',
            'language' => 'en'
        ];
    }
])->only();
