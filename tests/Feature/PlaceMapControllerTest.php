<?php

use App\Models\User;
use App\Models\PlaceMap;
use App\Models\PlaceMapItem;
use App\Models\Cell;
use App\Models\DataMap;
use App\Models\DataMapItem;
use App\Models\District;
use App\Models\Sector;
use App\Models\Village;
use App\Models\Province;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\Sanctum;
use Illuminate\Support\Facades\File;

beforeEach(function () {

    $this->user = User::factory()->create();

    $this->province = Province::factory()->create();
    $this->district = District::factory()->create(['province_id' => $this->province->id]);
    $this->sector = Sector::factory()->create(['district_id' => $this->district->id]);
    $this->cell = Cell::factory()->create(['sector_id' => $this->sector->id]);
    $this->village = Village::factory()->create(['cell_id' => $this->cell->id]);

    Sanctum::actingAs($this->user);

    $this->url = 'map/place';
});

it('can create place map', function (array $payload) {

    $response = $this->postJson($this->url, $payload);

    $response->assertStatus(200)->assertJson(['message' => 'Place map created successfully']);

    $this->assertDatabaseHas('PlaceMap', [
        'name' => $payload['name'],
        'description' => $payload['description'],
        'user_id' => $this->user->id
    ]);

    if (isset($payload['customFields'])) {

        $placeMap = PlaceMap::where('name', $payload['name'])
            ->where('user_id', $this->user->id)
            ->first();
    }
})->with([

    function () {

        return  [
            'name' => 'Test Place Map',
            'description' => 'Test Place Map Description',
            'image' => 'pin',
            'type' => 'general',
        ];
    },
    function () {

        return  [
            'name' => 'Test Place Map',
            'description' => 'Test Place Map Description',
            'type' => 'general',
            'image' => 'pin',
            'customFields' => [
                [
                    'type' => 'text',
                    'name' => 'field1',
                    'length' => 10,
                    'isRequired' => 'yes'
                ],
                [
                    'type' => 'number',
                    'name' => 'field2',
                    'length' => 10,
                    'isRequired' => 'no'
                ],
                [
                    'type' => 'date',
                    'name' => 'field3',
                    'length' => 10,
                    'isRequired' => 'yes'
                ]
            ],
        ];
    }
]);

it('can update place map', function (array $payload) {


    $response = $this->postJson("{$this->url}/{$payload['id']}", $payload['payload']);

    $response->assertStatus(200)->assertJson(['message' => 'Place map updated successfully']);

    $this->assertDatabaseHas('PlaceMap', [
        'name' => $payload['payload']['name'],
        'description' => $payload['payload']['description'],
        'user_id' => $this->user->id
    ]);

    if (isset($payload['payload']['customFields'])) {
        $placeMap = PlaceMap::where('name', $payload['payload']['name'])
            ->where('user_id', $this->user->id)
            ->first();

        //   expect($placeMap->customFields)->toEqual($payload['payload']['customFields']);
    }
})->with([

    function () {

        $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);
        return [

            'id' => $placeMap->id,
            'payload' => [
                'name' => 'Test Place Map',
                'description' => 'Test Place Map Description',
                'image' => 'pin',
                'type' => 'general',
                'customFields' =>  [
                    [
                        'type' => 'text',
                        'name' => 'field1',
                        'length' => 10,
                        'isRequired' => 'yes'
                    ],
                    [
                        'type' => 'number',
                        'name' => 'field2',
                        'length' => 10,
                        'isRequired' => 'no',
                    ],
                    [
                        'type' => 'date',
                        'name' => 'field3',
                        'length' => 10,
                        'isRequired' => 'yes'
                    ]
                ]
            ]
        ];
    },
    function () {
        $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);
        return [

            'id' => $placeMap->id,
            'payload' => [
                'name' => 'Test Place Map',
                'description' => 'Test Place Map Description',
                'image' => 'pin',
                'type' => 'general',
            ]
        ];
    }
]);

it('can get place map by id', function () {

    $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);

    $response = $this->getJson("{$this->url}/{$placeMap->id}");

    $response->assertStatus(200)->assertJson(['id' => $placeMap->id]);
});

it('can get all place maps', function () {

    $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id, 'name' => 'Test Place Map 1']);

    $placeMap2 = PlaceMap::factory()->create(['user_id' => $this->user->id, 'name' => 'Test Place Map 2']);

    $placeMap3 = PlaceMap::factory()->create(['user_id' => $this->user->id, 'name' => 'Test Place Map 3']);

    $response = $this->getJson($this->url);

    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'id'
                ]
            ],
            'currentPage',
            'lastPage',
            'itemsPerPage',
            'pageItems',
            'total',
        ])
        ->assertJsonCount(3, 'data');
});

it('can create map place item ', function (array $payload) {

    $response = $this->postJson("{$this->url}/{$payload['placeMapId']}/create-place", $payload['data']);
    $response->assertStatus(200)->assertJson(['message' => 'Place map item created successfully']);

    $this->assertDatabaseHas('PlaceMapItem', [
        'name' => $payload['data']['name'],
        'place_map_id' => $payload['placeMapId']
    ]);

    if (isset($payload['data']['dataItems'])) {
        $placeMapItem = PlaceMapItem::where('name', $payload['data']['name'])
            ->where('place_map_id', $payload['placeMapId'])
            ->first();

        expect(json_decode($placeMapItem->dataItems, true))->toEqual($payload['data']['dataItems']);
    }
})->with([

    function () {
        $placeMap = PlaceMap::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'administration',
            'customFields' => [
                [
                    'name' => 'field1',
                    'type' => 'text',
                    'length' => 10,
                    'isRequired' => 'yes'
                ],
                [
                    'name' => 'field2',
                    'type' => 'number',
                    'length' => 10,
                    'isRequired' => 'no'
                ]
            ]
        ]);

        $data = [
            'name' => 'Test Place Map Item abcdefghij',
            'type' => 'cell',
            'image' => 'pin',
            'address' => 'Test Place Map Item Address',
            'description' => 'Test Place Map Item Description',
            'latitude' => '1.0000000',
            'longitude' => '1.0000000',
            'visibility' => 'public',
            'status' => 'active',
            'dataItems' => [
                [
                    'name' => 'field1',
                    'value' => 'value1'
                ],
                [
                    'name' => 'field2',
                    'value' => '12'
                ]
            ]
        ];

        return [
            'placeMapId' => $placeMap->id,
            'data' => $data
        ];
    },

]);

it('can update map place item', function (array $payload) {
    $response = $this->postJson("{$this->url}/{$payload['placeMapId']}/{$payload['placeMapItemId']}/update-place", $payload['data']);

    if (isset($payload['expectedStatus']) && $payload['expectedStatus'] !== 200) {
        $response->assertStatus($payload['expectedStatus']);
        return;
    }

    $response->assertStatus(200)->assertJson(['message' => 'Place map item updated successfully']);

    $expectedData = [
        'name' => $payload['data']['name'],
        'place_map_id' => $payload['placeMapId']
    ];

    if (isset($payload['data']['dataItems'])) {
        $expectedData['dataItems'] = json_encode($payload['data']['dataItems']);
    }

    $this->assertDatabaseHas('PlaceMapItem', $expectedData);
})->with([
    // Scenario 1: Update name only
    function () {
        $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);
        $placeMapItem = PlaceMapItem::factory()->create(['place_map_id' => $placeMap->id]);

        return [
            'placeMapId' => $placeMap->id,
            'placeMapItemId' => $placeMapItem->id,
            'data' => [
                'name' => 'Updated Place Map Item'
            ],
            'expectedStatus' => 200
        ];
    },
    function () {
        // with custom fields 
        $placeMap = PlaceMap::factory()->create([
            'user_id' => $this->user->id,
            'customFields' => [
                [
                    'name' => 'field1',
                    'type' => 'text',
                    'length' => 10,
                    'isRequired' => 'yes'
                ],
                [
                    'name' => 'field2',
                    'type' => 'number',
                    'length' => 10,
                    'isRequired' => 'no'
                ]
            ]
        ]);

        $placeMapItem = PlaceMapItem::factory()->create(['place_map_id' => $placeMap->id]);
        return [
            'placeMapId' => $placeMap->id,
            'placeMapItemId' => $placeMapItem->id,
            'data' => [
                'name' => 'Updated Place Map Item',
                'dataItems' => [
                    [
                        'name' => 'field1',
                        'value' => 'value1'
                    ],
                    [
                        'name' => 'field2',
                        'value' => '12'
                    ]
                ]
            ],
            'expectedStatus' => 200
        ];
    }

]);

it('can get single map place item ', function () {

    $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);

    $placeMapItem = PlaceMapItem::factory()->create(['place_map_id' => $placeMap->id]);

    $response = $this->getJson("{$this->url}/{$placeMap->id}/{$placeMapItem->id}");

    $response->assertStatus(200)->assertJson(['id' => $placeMapItem->id]);
});

it('can get all map place items ', function () {

    $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);

    $placeMapItem = PlaceMapItem::factory()->create(['place_map_id' => $placeMap->id]);

    $placeMapItem2 = PlaceMapItem::factory()->create(['place_map_id' => $placeMap->id]);

    $placeMapItem3 = PlaceMapItem::factory()->create(['place_map_id' => $placeMap->id]);

    $response = $this->getJson("{$this->url}/{$placeMap->id}/items");

    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'id'
                ]
            ],
            'currentPage',
            'lastPage',
            'itemsPerPage',
            'pageItems',
            'total',
        ])
        ->assertJsonCount(3, 'data');
});

it('can update geofancing for place map', function (array $payloadBuilder) {
    $payload = $payloadBuilder;
    $response = $this->postJson("geo-fancing", $payload);

    $response->assertJson(['message' => 'Geo-fencing updated successfully']);

    $placeMapItem = PlaceMapItem::find($payload['placeMapItemId']);
    $geoFancing = json_decode($placeMapItem->geoFancing, true);

    expect($geoFancing['apiKey']['isActive'])->toBe($payload['apiKey']);
    expect($geoFancing['webHook']['isActive'])->toBe($payload['webHookStatus']);

    if ($payload['webHookStatus'] === 'yes') {
        expect($geoFancing['webHook']['url'])->toBe($payload['webHookUrl']);
    }

    if ($payload['geoFancing'] === 'yes') {
        expect($geoFancing['geoFancing'])->toEqual($payload['geoFancingData']);
    } else {
        expect($geoFancing['geoFancing'])->toBeEmpty();
    }
})->with([
    function () {
        $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id, 'type' => 'tracking']);
        $placeMapItem = PlaceMapItem::factory()->create([
            'place_map_id' => $placeMap->id,
            'type' => 'moving_item',
            'geoFancing' => json_encode([
                'apiKey' => ['key' => 'somekey', 'isActive' => 'no'],
                'webHook' => ['url' => '', 'isActive' => 'no'],
                'geoFancing' => []
            ])
        ]);

        return [
            'placeMapId' => (string)$placeMap->id,
            'placeMapItemId' => (string)$placeMapItem->id,
            'apiKey' => 'yes',
            'webHookStatus' => 'yes',
            'webHookUrl' => 'https://example.com/webhook',
            'geoFancing' => 'yes',
            'geoFancingData' => [
                ['name' => 'ID', 'value' => (string)$this->province->id],
                ['name' => 'ID-Type', 'value' => 'province'],
                ['name' => 'Action-Trigger', 'value' => 'enter'],
                ['name' => 'Type', 'value' => 'circle'],
                ['name' => 'Radius', 'value' => '100'],
            ],
        ];
    },
    function () {
        $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id, 'type' => 'tracking']);
        $placeMapItem = PlaceMapItem::factory()->create([
            'place_map_id' => $placeMap->id,
            'type' => 'moving_item',
            'geoFancing' => json_encode([
                'apiKey' => ['key' => 'somekey', 'isActive' => 'no'],
                'webHook' => ['url' => '', 'isActive' => 'no'],
                'geoFancing' => []
            ])
        ]);

        return [
            'placeMapId' => (string)$placeMap->id,
            'placeMapItemId' => (string)$placeMapItem->id,
            'apiKey' => 'yes',
            'webHookStatus' => 'yes',
            'webHookUrl' => 'https://example.com/webhook',
            'geoFancing' => 'no',
            'geoFancingData' => []
        ];
    },
    function () {
        $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id, 'type' => 'tracking']);
        $placeMapItem = PlaceMapItem::factory()->create([
            'place_map_id' => $placeMap->id,
            'type' => 'moving_item',
            'geoFancing' => json_encode([
                'apiKey' => ['key' => 'somekey', 'isActive' => 'no'],
                'webHook' => ['url' => '', 'isActive' => 'no'],
                'geoFancing' => []
            ])
        ]);

        return [
            'placeMapId' => (string)$placeMap->id,
            'placeMapItemId' => (string)$placeMapItem->id,
            'apiKey' => 'no',
            'webHookStatus' => 'no',
            'webHookUrl' => null,
            'geoFancing' => 'no',
            'geoFancingData' => []
        ];
    }
]);

it('can store gps data', function (array $payloadBuilder) {

    $payload = $payloadBuilder;
    $response = $this->postJson("gps-data", $payload);

    $response->assertJson(['message' => 'Gps data stored successfully']);

    $placeMapItem = PlaceMapItem::whereHas('placeMap', function ($query) {
        $query->where('user_id', $this->user->id);
    })->first();

    $dataMapItem = DataMapItem::where('place_map_item_id', $placeMapItem->id)->first();
    expect($dataMapItem)->not->toBeNull();

    $dataItems = collect($dataMapItem->dataItems);
    $latestItem = $dataItems->last();

    expect($latestItem['Latitude'])->toBe($payload['latitude']);
    expect($latestItem['Longitude'])->toBe($payload['longitude']);
    expect($latestItem['Timestamp'])->toBe($payload['timestamp']);
    expect($latestItem['Details'])->toBe($payload['details']);
})->with([
    function () {
        $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id, 'type' => 'tracking']);
        PlaceMapItem::factory()->create([
            'place_map_id' => $placeMap->id,
            'type' => 'moving_item',
            'geoFancing' => json_encode([
                'apiKey' => ['key' => 'somekey', 'isActive' => 'yes'],
                'webHook' => ['url' => '', 'isActive' => 'no'],
                'geoFancing' => []
            ])
        ]);

        return [
            'key' => 'somekey',
            'latitude' => '1.0000000',
            'longitude' => '1.0000000',
            'timestamp' => '2023-08-05 14:30:00',
            'details' => 'Test Details'
        ];
    }
]);

it('can create notification when geo-fancing is triggered', function () {

    // Create a district with simple test geometry
    $district = District::factory()->create([
        'name' => 'Test District',
        'code' => 'TD001',
        'province_id' => $this->province->id,
        'geojson' => [
            'type' => 'Polygon',
            'coordinates' => [[
                [30.0, -2.0],
                [30.0, -1.0],
                [31.0, -1.0],
                [31.0, -2.0],
                [30.0, -2.0]
            ]]
        ],
        'latitude' => -1.5,
        'longitude' => 30.5,
        'geometry' => DB::raw("ST_GeomFromText('POLYGON((30 -2, 30 -1, 31 -1, 31 -2, 30 -2))', 4326)"),
        'centroid' => DB::raw("ST_GeomFromText('POINT(30.5 -1.5)', 4326)")
    ]);

    // Create a place map for tracking
    $placeMap = PlaceMap::factory()->create([
        'user_id' => $this->user->id,
        'type' => 'tracking',
        'name' => 'Test Tracking Map'
    ]);

    // Create a place map item with geofencing enabled
    $placeMapItem = PlaceMapItem::factory()->create([
        'place_map_id' => $placeMap->id,
        'name' => 'Test Moving Item',
        'type' => 'moving_item',
        'geoFancing' => json_encode([
            'apiKey' => [
                'key' => 'test-api-key-123',
                'isActive' => 'yes'
            ],
            'webHook' => [
                'url' => '',
                'isActive' => 'no'
            ],
            'geoFancing' => [
                [
                    'name' => 'ID',
                    'value' => (string) $district->id
                ],
                [
                    'name' => 'ID-Type',
                    'value' => 'district'
                ],
                [
                    'name' => 'Action-Trigger',
                    'value' => 'send notification'
                ],
                [
                    'name' => 'Type',
                    'value' => 'entered'
                ],
                [
                    'name' => 'Radius',
                    'value' => '1'
                ]
            ]
        ])
    ]);

    // Create a data map for GPS tracking
    $dataMap = \App\Models\DataMap::factory()->create([
        'place_map_id' => $placeMap->id,
        'user_id' => $this->user->id,
        'name' => 'GPS Tracking Data',
        'customFields' => config('geo-data.gps-field')
    ]);

    // Create a data map item
    DataMapItem::factory()->create([
        'data_map_id' => $dataMap->id,
        'place_map_item_id' => $placeMapItem->id,
        'name' => 'GPS Data for Test Moving Item',
        'type' => 'gps_data'
    ]);

    // Ensure no notifications exist before test
    expect($this->user->notifications()->count())->toBe(0);

    // Send GPS data that should trigger geofencing (coordinates inside the district)
    $gpsPayload = [
        'key' => 'test-api-key-123',
        'latitude' => '-1.5',  // Inside the district polygon (center of our test district)
        'longitude' => '30.5', // Inside the district polygon (center of our test district)
        'timestamp' => now()->format('Y-m-d H:i:s'),
        'details' => 'Test GPS data for geofencing'
    ];

    $response = $this->postJson('gps-data', $gpsPayload);
    $response->assertJson(['message' => 'Gps data stored successfully']);

    // Check that a notification was created
    $this->user->refresh();
    expect($this->user->notifications()->count())->toBe(1);

    $notification = $this->user->notifications()->first();
    expect($notification->type)->toBe(\App\Notifications\GeoFenceNotification::class);
    expect($notification->data['trigger_type'])->toBe('entered');
    expect($notification->data['entity_name'])->toBe('Test District');
    expect($notification->data['entity_type'])->toBe('district');
    expect($notification->data['place_map_item_name'])->toBe('Test Moving Item');

    // Test the notifications endpoint
    $notificationsResponse = $this->getJson('notifications');
    $notificationsResponse->assertStatus(200);

    $notificationsData = $notificationsResponse->json();
    expect($notificationsData)->toHaveCount(1);
    expect($notificationsData[0]['type'])->toBe('geofence');
    expect($notificationsData[0]['data']['trigger_type'])->toBe('entered');
});

it('can get shared map data', function (array $payloadBuilder) {

    $payload = $payloadBuilder;
    $response = $this->getJson("map/shared/map-data/{$payload['mapKey']}");

    $response->assertStatus(200);
})->with([
    function () {
        $placeMap = PlaceMap::factory()->create(['visibility' => 'public']);
        $placeMapItem = PlaceMapItem::factory()->create(['place_map_id' => $placeMap->id]);
        $dataMap = DataMap::factory()->create(['place_map_id' => $placeMap->id]);
        $dataMapItem = DataMapItem::factory()->create(['data_map_id' => $dataMap->id, 'place_map_item_id' => $placeMapItem->id]);
        return [
            'mapKey' => $placeMap->key
        ];
    }
]);
