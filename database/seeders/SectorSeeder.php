<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use App\Models\Sector;
use App\Models\District;
use Illuminate\Support\Facades\Log;
use Spinen\Geometry\Geometry;
use Illuminate\Support\Facades\DB;

class SectorSeeder extends Seeder
{
    protected $geometry;

    /**
     * Constructor to inject Geometry service
     *
     * @param Geometry $geometry
     */
    public function __construct(Geometry $geometry)
    {
        $this->geometry = $geometry;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $geojsonPath = storage_path('dataset/Sector_level_boundary.geojson');
        if (!File::exists($geojsonPath)) {
            throw new \Exception('GeoJSON file not found at: ' . $geojsonPath);
        }

        $geojson = json_decode(File::get($geojsonPath), true);

        if (!isset($geojson['features']) || empty($geojson['features'])) {
            throw new \Exception('No features found in GeoJSON file');
        }

        $districts = District::pluck('id', 'name')->toArray();

        $batchSize = 500;
        $sectors = [];

        foreach ($geojson['features'] as $index => $feature) {
            $districtName = $feature['properties']['NAME_2'] ?? null;

            if (!$districtName || !isset($districts[$districtName])) {
                Log::warning('Skipping sector due to missing district', [
                    'sector_name' => $feature['properties']['NAME_3'] ?? 'Unknown',
                    'district_name' => $districtName ?? 'Unknown',
                ]);
                continue;
            }

            try {
                
                if (!isset($feature['geometry']) || !in_array($feature['geometry']['type'], ['Polygon', 'MultiPolygon'])) {
                    Log::warning('Invalid or missing geometry for sector: ' . ($feature['properties']['NAME_3'] ?? 'Unknown'));
                    continue;
                }

                $geometryObject = $this->geometry->parseGeoJson(json_encode($feature['geometry']));
                $geometryWkt = $geometryObject->toWkt();
                $centroidObject = $geometryObject->centroid();
                $longitude = $centroidObject->x();
                $latitude = $centroidObject->y();
                $centroidWkt = "POINT($longitude $latitude)";

                $sectors[] = [
                    'name' => $feature['properties']['NAME_3'] ?? null,
                    'code' => $feature['properties']['ID_3	'] ?? null,
                    'geojson' => json_encode($feature['geometry']),
                    'district_id' => $districts[$districtName],
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'created_at' => now(),
                    'updated_at' => now(),
                    'geometry' => DB::raw("ST_GeomFromText('{$geometryWkt}', 4326)"),
                    'centroid' => DB::raw("ST_GeomFromText('{$centroidWkt}', 4326)"),
                ];
            } catch (\Exception $e) {
                Log::error('Error processing sector: ' . ($feature['properties']['sector'] ?? 'Unknown') . ' - ' . $e->getMessage());
                continue;
            }

            if (count($sectors) >= $batchSize || $index === count($geojson['features']) - 1) {
                Sector::upsert(
                    $sectors,
                    ['code'],
                    [
                        'name',
                        'geojson',
                        'district_id',
                        'latitude',
                        'longitude',
                        'updated_at',
                    ]
                );
                $sectors = [];
            }
        }

        Log::info('Sector seeding completed. ' . Sector::count() . ' sectors created on ' . now()->toDateTimeString() . ' CAT.');
    }
}
