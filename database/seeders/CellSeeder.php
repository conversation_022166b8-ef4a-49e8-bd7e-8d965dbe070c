<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use App\Models\Cell;
use App\Models\Sector;
use Illuminate\Support\Facades\Log;
use Spinen\Geometry\Geometry;
use Illuminate\Support\Facades\DB;

class CellSeeder extends Seeder
{
    protected $geometry;

    /**
     * Constructor to inject Geometry service
     *
     * @param Geometry $geometry
     */
    public function __construct(Geometry $geometry)
    {
        $this->geometry = $geometry;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $geojsonPath = storage_path('dataset/Cell_level_boundary.geojson');
        if (!File::exists($geojsonPath)) {
            throw new \Exception('GeoJSON file not found at: ' . $geojsonPath);
        }

        $geojson = json_decode(File::get($geojsonPath), true);

        if (!isset($geojson['features']) || empty($geojson['features'])) {
            throw new \Exception('No features found in GeoJSON file');
        }

        // Prepare data for upsert in chunks
        $batchSize = 500;
        $cells = [];

        foreach ($geojson['features'] as $index => $feature) {

            try {
                // Validate geometry
                if (!isset($feature['geometry']) || !in_array($feature['geometry']['type'], ['Polygon', 'MultiPolygon'])) {
                    Log::warning('Invalid or missing geometry for cell: ' . ($feature['properties']['NAME_4'] ?? 'Unknown'));
                    continue;
                }

                // Calculate centroid
                $geometryObject = $this->geometry->parseGeoJson(json_encode($feature['geometry']));
                $geometryWkt = $geometryObject->toWkt();
                $centroidObject = $geometryObject->centroid();
                $longitude = $centroidObject->x();
                $latitude = $centroidObject->y();
                $centroidWkt = "POINT($longitude $latitude)";

                $cells[] = [
                    'name' => $feature['properties']['NAME_4'] ?? null,
                    'code' => $feature['properties']['ID_4	'] ?? null,
                    'geojson' => json_encode($feature['geometry']),
                    'sector_id' => Sector::where('name', $feature['properties']['NAME_3'])->whereHas('district', function ($query) use ($feature) {
                        $query->where('name', $feature['properties']['NAME_2']);
                    })->first()?->id ?? null,
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'created_at' => now(),
                    'updated_at' => now(),
                    'geometry' => DB::raw("ST_GeomFromText('{$geometryWkt}', 4326)"),
                    'centroid' => DB::raw("ST_GeomFromText('{$centroidWkt}', 4326)"),
                ];
            } catch (\Exception $e) {
                Log::error('Error processing cell: ' . ($feature['properties']['cellule_1'] ?? 'Unknown') . ' - ' . $e->getMessage());
                continue;
            }

            // Perform upsert when batch size is reached or at the end
            if (count($cells) >= $batchSize || $index === count($geojson['features']) - 1) {
                Cell::upsert(
                    $cells,
                    ['code'], // Unique key for upsert (assumes 'code' is unique)
                    [
                        'name',
                        'geojson',
                        'sector_id',
                        'latitude',
                        'longitude',
                        'updated_at',
                    ]
                );
                $cells = []; // Clear the batch
            }
        }


        // try {
        //     // Calculate centroid for Ryakibogo
        //     $ryakibogoGeometry = [
        //         'type' => 'Polygon',
        //         'coordinates' => [[[-4.481, 3.567], [-4.481, 3.567], [-4.481, 3.567], [-4.481, 3.567]]],
        //     ];
        //     $geometry = $this->geometry->parseGeoJson(json_encode($ryakibogoGeometry));
        //     $centroid = $geometry->centroid();
        //     $latitude = round($centroid->y(), 6);
        //     $longitude = round($centroid->x(), 6);

        //     Cell::upsert(
        //         [
        //             [
        //                 'name' => 'Ryakibogo',
        //                 'code' => 476,
        //                 'capture_year' => null,
        //                 'source' => null,
        //                 'geojson' => json_encode($ryakibogoGeometry),
        //                 'shape_length' => 14.95946,
        //                 'shape_area' => 7940329.72,
        //                 'population' => null,
        //                 'description' => null,
        //                 'sector_id' => Sector::where('name', 'Gishamvu')->first()->id,
        //                 'latitude' => $latitude,
        //                 'longitude' => $longitude,
        //                 'created_at' => now(),
        //                 'updated_at' => now(),
        //             ],
        //         ],
        //         ['code'],
        //         [
        //             'name',
        //             'capture_year',
        //             'source',
        //             'geojson',
        //             'shape_length',
        //             'shape_area',
        //             'population',
        //             'description',
        //             'sector_id',
        //             'latitude',
        //             'longitude',
        //             'updated_at',
        //         ]
        //     );
        // } catch (\Exception $e) {
        //     Log::error('Error processing Ryakibogo cell: ' . $e->getMessage());
        // }


        Log::info('Cell seeding completed. ' . Cell::count() . ' cells created on ' . now()->toDateTimeString() . ' CAT.');
    }
}
