<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('PlaceMapItem', function (Blueprint $table) {
            $table->id();
            $table->foreignId('place_map_id')->references('id')->on('PlaceMap')->onDelete('restrict');
            $table->string('name');
            $table->string('address')->nullable();
            $table->integer('location_id')->nullable();
            $table->text('description')->nullable();
            $table->string('image')->nullable();
            $table->json('dataItems')->nullable();
            $table->decimal('latitude', 15, 7)->nullable();
            $table->decimal('longitude', 15, 7)->nullable();
            $table->json('geojson')->nullable();
            $table->string('type')->nullable();
            $table->string('source')->nullable();
            $table->string('visibility')->nullable();
            $table->json('geoFancing')->nullable();
            $table->string('status')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('PlaceMapItem');
    }
};
