<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('DataMapItem', function (Blueprint $table) {
            $table->id();
            $table->foreignId('data_map_id')->references('id')->on('DataMap')->onDelete('restrict');
            $table->foreignId('place_map_item_id')->references('id')->on('PlaceMapItem')->onDelete('restrict');
            $table->string('name');
            $table->string('description')->nullable();
            $table->json('dataItems')->nullable();
            $table->string('type')->nullable();
            $table->string('visibility')->nullable();
            $table->json('details')->nullable();
            $table->string('status')->nullable();
            $table->timestamps();
        });
        
        DB::statement('ALTER TABLE DataMapItem ADD geometry GEOMETRY NOT NULL DEFAULT (ST_GeomFromText("GEOMETRYCOLLECTION EMPTY", 4326)) SRID 4326');
        DB::statement('ALTER TABLE DataMapItem ADD centroid POINT NOT NULL DEFAULT (ST_GeomFromText("POINT(0 0)", 4326)) SRID 4326');
        DB::statement('CREATE SPATIAL INDEX geometry_index ON DataMapItem (geometry)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('DataMapItem');
    }
};
