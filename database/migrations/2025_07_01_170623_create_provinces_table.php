<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('Province', function (Blueprint $table) {
            $table->id();
            $table->string('name_en')->index(); // English name (e.g., "East")
            $table->string('name_local')->nullable(); // Local name (e.g., "Iburasirazuba")
            $table->string('code')->nullable(); // Province code (e.g., "5")
            $table->integer('capture_year')->nullable(); // Capture year (e.g., 2012)
            $table->string('source')->nullable(); // Source (e.g., "NISR")
            $table->json('geojson'); // Geometry (MultiPolygon)
            $table->float('shape_length')->nullable();
            $table->float('shape_area')->nullable();
            $table->integer('population')->nullable();
            $table->text('description')->nullable();
            $table->decimal('latitude', 15, 7)->nullable();
            $table->decimal('longitude', 15, 7)->nullable();
            $table->timestamps();
        });

        DB::statement('ALTER TABLE Province ADD geometry GEOMETRY NOT NULL SRID 4326');
        DB::statement('ALTER TABLE Province ADD centroid POINT NOT NULL SRID 4326');
        DB::statement('CREATE SPATIAL INDEX geometry_index ON Province (geometry)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('Province');
    }
};
