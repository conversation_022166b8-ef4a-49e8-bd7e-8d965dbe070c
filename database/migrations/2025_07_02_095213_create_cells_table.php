<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('Cell', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sector_id')->references('id')->on('Sector')->onDelete('restrict');
            $table->string('name')->index();
            $table->string('code')->nullable();
            $table->integer('capture_year')->nullable();
            $table->string('source')->nullable();
            $table->json('geojson');
            $table->float('shape_length')->nullable();
            $table->float('shape_area')->nullable();
            $table->integer('population')->nullable();
            $table->text('description')->nullable();
            $table->decimal('latitude', 15, 7)->nullable();
            $table->decimal('longitude', 15, 7)->nullable();
            $table->timestamps();
        });

        DB::statement('ALTER TABLE Cell ADD geometry GEOMETRY NOT NULL SRID 4326');
        DB::statement('ALTER TABLE Cell ADD centroid POINT NOT NULL SRID 4326');
        DB::statement('CREATE SPATIAL INDEX geometry_index ON Cell (geometry)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('Cell');
    }
};
