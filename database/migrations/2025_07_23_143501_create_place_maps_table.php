<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('PlaceMap', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->references('id')->on('users');
            $table->string('name');
            $table->string('description')->nullable();
            $table->string('type')->nullable()->default('general');
            $table->string('image')->nullable()->default('pin');
            $table->string('key')->nullable();
            $table->string('visibility')->nullable()->default('private');
            $table->json('customFields')->nullable();
            $table->string('status')->nullable()->default('active');
            $table->string('isTemplate')->nullable();
            $table->string('zoom')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('PlaceMap');
    }
};
