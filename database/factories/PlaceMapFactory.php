<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PlaceMap>
 */
class PlaceMapFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'name' => fake()->name(),
            'description' => 'demo place map',
            'key' => fake()->uuid(),
            'visibility' => null,
            'customFields' => null,
            'status' => null,
            'isTemplate' => null,
            'image' => null,
            'zoom' => null
        ];
    }
}
