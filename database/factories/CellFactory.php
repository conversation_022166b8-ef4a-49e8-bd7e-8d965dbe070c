<?php

namespace Database\Factories;

use App\Models\Sector;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\DB;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Cell>
 */
class CellFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $longitude = 30.1331177545;
        $latitude = -1.8836292995;

        return [
            'sector_id' => Sector::factory(),
            'name' => fake()->name(),
            'code' => fake()->uuid(),
            'latitude' => null,
            'longitude' => null,
            'geojson' => ['type' => 'Polygon', 'coordinates' => []],
            'population' => null,
            'capture_year' => null,
            'geometry' => DB::raw("ST_GeomFromText('POLYGON(($longitude $latitude, $longitude $latitude, $longitude $latitude, $longitude $latitude, $longitude $latitude))', 4326)"),
            'centroid' => DB::raw("ST_GeomFromText('POINT($longitude $latitude)', 4326)"),
        ];
    }
}
