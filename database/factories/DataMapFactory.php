<?php

namespace Database\Factories;

use App\Models\PlaceMap;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DataMap>
 */
class DataMapFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'place_map_id' => PlaceMap::factory(),
            'type' => null,
            'name' => fake()->name(),
            'description' => 'demo data map',
            'visibility' => null,
            'customFields' => null,
            'status' => null,
            'key' => fake()->uuid(),
        ];
    }
}
