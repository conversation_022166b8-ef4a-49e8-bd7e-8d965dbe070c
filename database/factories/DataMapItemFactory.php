<?php

namespace Database\Factories;

use App\Models\DataMap;
use App\Models\PlaceMapItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DataMapItem>
 */
class DataMapItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'data_map_id' => DataMap::factory(),
            'place_map_item_id' => PlaceMapItem::factory(),
            'name' => fake()->name(),
            'description' => 'demo data map item',
            'visibility' => null,
            'details' => null,
            'status' => null,
            'dataItems' => null,
        ];
    }
}
