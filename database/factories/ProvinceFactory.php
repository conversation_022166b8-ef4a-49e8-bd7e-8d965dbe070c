<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\DB;


/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Province>
 */
class ProvinceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */

public function definition(): array
{
    $longitude = 30.1331177545;
    $latitude = -1.8836292995;

    return [
        'name_local' => fake()->name(),
        'name_en' => fake()->name(),
        'code' => fake()->uuid(),
        'latitude' => $latitude,
        'longitude' => $longitude,
        'geojson' => ['type' => 'Polygon', 'coordinates' => []],
        'population' => fake()->numberBetween(10000, 1000000),
        'capture_year' => fake()->year(),
        'geometry' => DB::raw("ST_GeomFromText('POLYGON(($longitude $latitude, $longitude $latitude, $longitude $latitude, $longitude $latitude, $longitude $latitude))', 4326)"),
        'centroid' => DB::raw("ST_GeomFromText('POINT($longitude $latitude)', 4326)"),
    ];
}

}
