<?php

namespace Database\Factories;

use App\Models\PlaceMap;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PlaceMapItem>
 */
class PlaceMapItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'place_map_id' => PlaceMap::factory(),
            'name' => fake()->name(),
            'address' => fake()->address(),
            'description' => 'demo place map item',
            'visibility' => null,
            'status' => null,
            'geojson' => null,
            'latitude' => null,
            'longitude' => null,
            'source' => null,
            'type' => null,
            'image' => null,
            'geoFancing' => null,
            'dataItems' => null,
        ];
    }
}
