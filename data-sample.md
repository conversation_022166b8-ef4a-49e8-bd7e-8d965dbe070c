Avatar
Button
When to use
Types
Priorities
Sizes
Accessories
Interaction
Placement
Accessibility
Content
Availability
Carousel Cards
Compact date input
Copy Block
Date input
Date picker
Dropdown
Image Ratios
Instruction
Media Button
Money input
Popover
Search input
Select
Switch
Text area
Text input
Design at Wise
Foundations
Components
Patterns
Resources
Button
A button lets the user perform an action with a tap or a click.

Button
When to use
Use a button:
when the user needs to perform an action, like initiating a new flow or confirming something.
Don't use a button:
when you want to link to other pages or external resources (use a link instead)
for hierarchical navigation, where the user navigates to a sub-topic and goes deeper into the product (use a navigation option instead)
Types
There are two different types of button that, default and negative, these designed to emphasise the nature of the action.

Default button style
Default
Default is the standard button type that conveys a general action. Use this by default.

Negative button type
Negative
Use negative for confirming destructive actions, like canceling a transfer or closing a balance.

Priorities
Priorities set a visual hierarchy amongst the buttons displayed on the screen to help more important buttons to take precedence over others.

Primary button
Primary
The most important action to move forward in a flow, acknowledge and dismiss, or finish a task.

Secondary button
Secondary
Use secondary for providing alternatives to the primary action, or when none of your actions are more important than the others.

Secondary neutral button
Secondary neutral
For functional actions, such as copying information like bank details with a button, controls or for navigation.

Tertiary button
Tertiary button
Dismissive actions give users a way out of something, letting them cancel, do nothing, dismiss, or skip.

Primary
The most important action to move forward in a flow, acknowledge and dismiss, or finish a task.

You should always have one primary button per page where relevant, this can contextually change if another action needs to be completed before being able to proceed.

Primary button
Footer buttons with primary secondary and minimal buttons
neptune.StatusIcon.iconLabel.success
Use one primary button per page to indicate the most important action.

Footer buttons with two primary's and a minimal button
neptune.StatusIcon.iconLabel.error
Don’t use multiple primary buttons on a page.

Secondary
Use secondary for providing alternatives to the primary action, or when none of your actions are more important than the others.

Secondary on list items
neptune.StatusIcon.iconLabel.success
Use secondary buttons when you need to display multiple key actions at the same level of hierarchy.

Secondary button paired with negative 
neptune.StatusIcon.iconLabel.error
Don’t pair a secondary with a negative action, use secondary neutral or tertiary instead.

Primary and secondary neutral on card
neptune.StatusIcon.iconLabel.success
Pair primary and secondary neutral on neutral surfaces

Secondary with secondary neutral  
neptune.StatusIcon.iconLabel.error
Don’t use the secondary on a neutral surface.

Secondary neutral
Use secondary neutral for less important convenience actions, such as copying information like bank details with a button, controls or for navigation.

Secondary neutral button used for account details
neptune.StatusIcon.iconLabel.success
Use secondary neutral buttons for controls and views such as the bank details.

Secondary neutral button used in footer buttons
neptune.StatusIcon.iconLabel.error
Avoid using secondary neutral buttons for dismissive actions, use tertiary instead.

Secondary neutral on neutral surface
neptune.StatusIcon.iconLabel.success
Use the secondary neutral on neutral surfaces

Secondary on neutral surface
neptune.StatusIcon.iconLabel.error
Don’t use the secondary on a neutral surface.

Sizes
There are three different button sizes, small medium and large, each used for different purposes.

Large button
Large
Medium button
Medium
Small button
Small
Large
The large button is used to move users forward in a flow.

It’s used at full width on mobile and auto-width, adjusting to the content on desktop depending on use case.

Large button
Button used as footer
neptune.StatusIcon.iconLabel.success
Use large button at the bottom of the page or content to aid the user to the next step.

Large button used in card
neptune.StatusIcon.iconLabel.error
Don’t use large buttons inside cards or list items.

Medium
The medium button is used for in line content that needs a greater emphasis than small button or that simply needs to be kept in line with other content on the same line — For example in the nav where you have a 40px close or back icon button.

Medium supports icon left and right, and single or double avatars and wraps to the content.

Medium button with different accessory types
Medium supports icon left and right, and single or double avatars and wraps to the content.

medium button in header nav
neptune.StatusIcon.iconLabel.success
Use in line with other buttons at the same size

Medium button in list item
neptune.StatusIcon.iconLabel.error
Don’t use inside of list items

Small
The small button is used for smaller in line content such as list items.

Small button supports icon left and right and wraps to the content.

Small button with different accessory types
Small button supports icon left and right and wraps to the content.

small button in list item
neptune.StatusIcon.iconLabel.success
Use in line with other buttons at the same size or where you need more horizontal space.

Small button in footer
neptune.StatusIcon.iconLabel.error
Don’t use in place of large buttons or at full width

Accessories
Icons
Use icon right for actions
Use icon left to support the message
Use icons that best match the written content
Don’t use icons for the sake of it
Don’t change the avatar placement
Icon left and right example
neptune.StatusIcon.iconLabel.success
Use icon right for actions and left to support the message

button with wrong icons left and right
neptune.StatusIcon.iconLabel.error
Don’t place actions on the left, or supporting icons on the right

Avatars
Use avatar on the left
Use avatar to support written content or indicate currency or recipient
Don’t use avatars for the sake of it
Don’t change the avatar placement
Don’t use double diagonal avatar, use horizontal only
Double diagonal avatar on medium button
neptune.StatusIcon.iconLabel.error
Don’t use double diagonal avatars.

Avatar on right of medium button
neptune.StatusIcon.iconLabel.error
Don’t change the avatar placement.

Interaction
Unless the button is disabled, the user can tap or click on it to perform the action assigned to it.

Placement
The button should always be contextual, which is why it should be used in line with the content. This gives a stronger cohesion between the context and the action that it performs.

On mobile, if an action must be taken by the user, for example, to finalise something or proceed in the current process, it is best to keep the button visible all the time.

You can do this by pinning it to the bottom of the screen, outside of the scrolling area. Please pin only one primary action per screen.

Accessibility
When using buttons on mobile, remember that their height may change based on the user's system-wide preferred font size for accessibility.

For screen reader users, the button's title is read aloud. This emphasises the importance of contextual titles, ensuring all users understand the button's action.

Keep button copy concise. Longer text is harder to scan and increases cognitive load. Also, consider that text length may double when translated.

To ensure users always know what will happen when they interact with a button, the text must be fully visible in all supported font sizes and languages. We don't truncate button text; instead, we allow it to wrap, and the button area grows accordingly.

On small devices, buttons with more than a few words can dominate the screen, reducing the usability of other elements—especially if the button is pinned to the bottom.

Disabled states
Consider providing alternative cues explaining why a button is disabled, ensuring screen readers can convey this information, or rethink the design so that buttons are always enabled but provide feedback on required steps before proceeding (e.g. form validation).

Content
Button
Button copy should:

start with a verb, like ‘Pay’ or ‘Send’

be just a few words (ideally 1 or 2)

describe the action (if someone only reads the button, they should know what will happen next)

connect to the content around it — for example, it might use the same words as the title

avoid using first person pronouns like ‘me’, ‘my’ and ‘I’

be in sentence case (only capitalise the first letter of the first word)

have no full stop

A button that reads 'Log in'.
neptune.StatusIcon.iconLabel.success
A button that reads 'I want to log in to my account'.
neptune.StatusIcon.iconLabel.error
A button that reads 'Freeze card'.
neptune.StatusIcon.iconLabel.success
A button that reads 'Freeze my card'.
neptune.StatusIcon.iconLabel.error
Availability
Platform

Available

Developer documentation

Android

iOS

Web

Web documentation

Wise Design - Button



Avatar
Sizes
Media
Types
Single
Double
Interaction
Accessibility
Availability
Button
Carousel Cards
Compact date input
Copy Block
Date input
Date picker
Dropdown
Image Ratios
Instruction
Media Button
Money input
Popover
Search input
Select
Switch
Text area
Text input
Design at Wise
Foundations
Components
Patterns
Resources
Avatar
An avatar is a representation of a unique entity — like a person, a business, or an object. It can contain initials, images, icons, or flags.

Avatar
Sizes
Supported sizes are 16, 24, 32, 40, 48, 56, and 72.

Avatar sizes
Media
There are 4 media types available for avatar. Image, flag, icon and text.

Avatar with image
Image
Avatars can have an image. Can be uploaded by the user or representation of a partner by utilising their logo.

Avatar with flag
Flag
Flags allow us to visually communicate countries and currencies.

Avatar with clock icon
Icon
Icons are used to illustrate information or as an entry point.

Avatar with AA text
Text
If the user profile image is unavailable, a fallback up to two numerical characters can be used instead.

Image
Images are used to represent either a person or a brand by utilising their logo.

If no profile image is available fallback to initials on the text avatar type.
If no logo is available fallback to category icon on the icon type.
Do not stretch or allow logos to bleed outside of the avatar shape.
Avatar with image to avatar with initials
neptune.StatusIcon.iconLabel.success
If no profile image is available fallback to initials on the text avatar type.

Avatar google logo to avatar with shopping bag icon
neptune.StatusIcon.iconLabel.success
If no logo is available fallback to category icon on the icon type.

Avatars with airbnb, google and coop logos
neptune.StatusIcon.iconLabel.success
Logo should be recognisable inside of the shape.

Avatars with airbnb, google and coop logos all stretched or oversized
neptune.StatusIcon.iconLabel.error
Do not stretch or allow logos to bleed outside of the avatar shape or use images with transparent backgrounds.

Flag
Flags are used to represent either a currency or a country. We support all global flags in our libraries.

Avatars with different world flags
Icon
Icons are used to illustrate information or as an entry point.

Icon avatars come in an interactive and non interactive state, see more information under interaction.

Use icons that clearly represent the action being presented.
Avatars with different category icons
Used to represent categories

Avatar in an activity list item
Used as standalone to support other content

Text
Text is used to represent a person or to showcase there are more than 2 items in a collection on a double avatar.

Text avatars come in an interactive and non interactive state, see more information under interaction.

Size 16 avatar does not support the text variant as it doesn’t pass accessibility standards.

Use text for initials
Use text to reflect more than 2 items in a double avatar
When you need to display more than 9 additional items display it as +9
Don’t add more than 2 characters
Avatar with JW initials
Text can be used for initials

Double avatar with +3 text
Text can be used to represent a number of items behind an avatar

Double avatar with more than 2 characters
neptune.StatusIcon.iconLabel.error
Don’t add more than 2 characters.

Double avatar with 2 characters
neptune.StatusIcon.iconLabel.success
When you need to display more than 9 additional items display it as +9

Types
There are 2 different types of avatar with multiple subtypes underneath each.

Avatar with EU flag
Single
Double avatar with photo and UK flag
Double
Single
There are 3 types of single avatar, standalone, with a badge or with a notifications.

Avatar with snowflake icon
Standalone
Avatar with image and fast flag badge
With badge
Avatar with FG initials and a notification 
With notification
Standalone
When you need to make it easier for a user to identify content, like a recipient or currency.

Wise launchpad home screen on desktop
Used on list items to visually elevate and support the information.

With badge
Badges contain additional information to support avatars, such as flags, the Wise logo, images, statuses actions and references.

There are 6 different types of badges you can use.

Avatar with flag badge
Flag
Used to represent currency or country

Avatar with image and fast flag badge
Fast flag
Used to represent a Wise account.

Avatar with image and google badge
Image
Used to represent other brands

Avatar with different status badges
Status
Used to represent status.

Avatar with plus action
Action
Used to indicate a supporting action.

Avatar with a reference icon badge
Reference
Used as a reference to an action already taken.

Avatars being used in lists and standalone
neptune.StatusIcon.iconLabel.success
Use on activity list, navigation option or standalone avatars.

Avatar with two badges
neptune.StatusIcon.iconLabel.error
Don’t use more than one badge in the avatar.

With notification
Notifications are used to indicate to the user that there is a notification behind the avatar that requires their attention.

These are primarily used for the profile avatar.

Profile avatar with notification 
Notification usage on the profile avatar.

Double
Double avatars are used when you need to display more than one avatar at the same level of hierarchy.

This can be useful for representing recipient and currency or for currency conversion.

Double horizontal with image and flag
Horizontal
Double diagonal avatar with two flags
Diagonal
Double horizontal
When you don’t need to retain width alignment with other avatars to keep content aligned — for example in action buttons or tables.

Double horizontal avatar with image and flag
Double horizontal avatars are used to represent two related items on the same level of hierarchy within variable width content.

Double avatar on a table
neptune.StatusIcon.iconLabel.success
Use within variable width content where there is no impact on the visual alignment.

Double horizontal avatar on list item
neptune.StatusIcon.iconLabel.warning
Avoid using a double horizontal avatar if it affects the content alignment, such as in a navigation option.

Double horizontal avatar with image and text
neptune.StatusIcon.iconLabel.success
Use the second avatar to highlight when you need to communicate more than 2.

Four avatars stacked horizontally
neptune.StatusIcon.iconLabel.error
Don’t use more than 2 avatars.

Double diagonal
Use when you need to retain width alignment with other avatars — for example in navigation options.

Double diagonal avatar with flags
Double diagonal avatars are used to represent two related items on the same level of hierarchy and take up the same space as a standalone avatar.

Double diagonal avatar on a list item
neptune.StatusIcon.iconLabel.success
Use when content alignment needs to remain the same.

Double diagonal avatar in a button
neptune.StatusIcon.iconLabel.error
Don’t use on variable width components where there is room to grow.

Interaction
Avatar is used as supporting information, and can be interactive depending on the content it’s paired with or the context it’s in.

Use interactive avatar as a standalone interactive component acting as a button.
If the avatar is sitting in an interactive surface use a non-interactive avatar.
These rules only apply to the initials and icon variation of avatar.

Interactive avatar as profile
Use the interactive background for standalone avatars that are interactive, like the profile avatar.

Non interactive avatar on list item
neptune.StatusIcon.iconLabel.success
Use a non-interactive avatar when it sits on top of interactive surface like navigation option.

Interactive avatar on list item
neptune.StatusIcon.iconLabel.error
Don’t use interactive avatars layered on top of interactive content, like navigation option.

Selected
Avatars have an optional selected state which can be used when you’re selecting from a list of options. For example choosing a spending category or an icon for your Jar.

Selectable avatars always need to use the interactive background state.

Collection of categories using the interactive avatar
neptune.StatusIcon.iconLabel.success
Use the interactive avatar for selectable avatars.

Collection of category avatars using the non interactive avatar
neptune.StatusIcon.iconLabel.error
Don’t use non-interactive background for non selected or selected state.

Accessibility
Never use an avatar in isolation without copy or interaction cues (such as a chevron icon). This is so that they are clearly identifiable, and aid comprehension and interaction.

Availability
Platform

Available

Developer documentation

Android

iOS

Web

Web documentation

