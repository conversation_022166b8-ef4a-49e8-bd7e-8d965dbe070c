# OnRwanda GEO


### Setup
These are the procedures to follow when setting up the main service for the first time using sail with docker

- Copy the .env.example into .env
    ```shell
    cp .env.example .env
    ```
- Copy the .env.testing.example into .env.testing and un-comment the `Sail` section then comment the `Github actions` section
    ```shell
    cp .env.example.testing .env.testing
    ```
- Install composer dependencies
    ```shell
    docker run --rm -it -v $(pwd):/var/www/html -w /var/www/html laravelsail/php84-composer:latest composer install --ignore-platform-reqs
    ```
- Build the project **(keep this running in a tab in order to get access to the sail command)**
    ```shell
    vendor/bin/sail up -d
    ```
- Retrieve the latest RoadRunner binary
    ```shell
    vendor/bin/sail shell
    
    # Within the Sail shell...
    vendor/bin/rr get-binary
  
    chmod +x ./rr
    ```
- Rebuild sail without cache
    ```shell
    vendor/bin/sail build --no-cache
    ```
- Install npm packages(used by octane to watch files changes)
    ```shell
    vendor/bin/sail npm install
    ```
- Stop the octane server
    ```shell
    vendor/bin/sail artisan octane:stop
    ```
- Generate the application key
    ```shell
    vendor/bin/sail artisan key:generate
    ```
- Run migrations
    ```shell
    vendor/bin/sail artisan migrate:fresh --seed
    ```
- Generate openapi documentation
    ```shell
    vendor/bin/sail artisan l5-swagger:generate
    ```

### Usage
- Start the project
    ```shell
    vendor/bin/sail up -d
    ```
- Stop the octane server
    ```shell
    vendor/bin/sail artisan octane:stop
    ```
- Generate openapi documentation
    ```shell
    vendor/bin/sail artisan l5-swagger:generate
    ```
- Start the octane server with support for watching files changes
    ```shell
    vendor/bin/sail artisan octane:start --server=roadrunner --host=0.0.0.0 --rpc-port=6001 --port=80 --watch
    ```
