<script setup>
import { ref, computed, watch } from 'vue';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { debounce } from 'lodash';

const props = defineProps({
    searchQuery: {
        type: String,
        default: ''
    },
    searchResults: {
        type: Object,
        default: () => ({})
    },
    isLoading: {
        type: Boolean,
        default: false
    },
    error: {
        type: String,
        default: null
    },
    searchTime: {
        type: Number,
        default: 0
    },
    selectedLanguage: {
        type: String,
        default: 'en'
    },
    showFilters: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits([
    'update:searchQuery',
    'update:selectedLanguage', 
    'update:showFilters',
    'clear-search',
    'result-hover',
    'result-click'
]);

// --- COMPUTED PROPERTIES ---
const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'healthFacs'];

const totalResults = computed(() => {
    return ALL_RESULT_TYPES.reduce((sum, type) => sum + (props.searchResults[type]?.length || 0), 0);
});

const hasResults = computed(() => totalResults.value > 0);

const getPlaceholderText = computed(() => ({
    rw: 'Shakisha ahantu...',
    en: 'Search Rwanda locations...',
    fr: 'Rechercher des lieux...',
}[props.selectedLanguage] || 'Search locations...'));

// Language options
const languages = [
    { code: 'rw', name: 'Kiny', flag: '🇷🇼' },
    { code: 'en', name: 'Eng', flag: '🇺🇸' },
    { code: 'fr', name: 'Fra', flag: '🇫🇷' },
];

// --- METHODS ---
const updateSearchQuery = (value) => {
    emit('update:searchQuery', value);
};

const updateLanguage = (lang) => {
    emit('update:selectedLanguage', lang);
};

const toggleFilters = () => {
    emit('update:showFilters', !props.showFilters);
};

const clearSearch = () => {
    emit('clear-search');
};

const getDisplayName = (result) => {
    return result[`name_${props.selectedLanguage}`] || result.name_en || result.name_local || result.name || 'N/A';
};

const getResultType = (type) => {
    return type.slice(0, -1); // Remove 's' from plural
};

const getHoverColor = (type) => {
    const colors = {
        provinces: '#1E88E5', // Blue
        districts: '#43A047', // Green
        sectors: '#FFB300', // Yellow
        cells: '#1E88E5',
        villages: '#43A047',
        healthFacs: '#FFB300'
    };
    return colors[type] || '#1E88E5';
};

const handleResultHover = (result, type) => {
    emit('result-hover', { ...result, type });
};

const handleResultClick = (result, type) => {
    emit('result-click', { ...result, type });
};
</script>

<template>
    <div class="w-full max-w-4xl mx-auto px-6">
        <!-- Main Search Container with Glassmorphism -->
        <div class="glassmorphism-container rounded-3xl p-8 mb-6">
            <!-- Title -->
            <h1 class="text-5xl font-bold text-white mb-8 text-center drop-">
                Rwanda Geo Platform
            </h1>
            
            <!-- Search Bar -->
            <div class="flex items-center gap-4 mb-6">
                <div class="relative flex-1">
                    <svg class="absolute left-4 top-1/2 -translate-y-1/2 h-6 w-6 text-white/70" 
                         xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                    </svg>
                    <Input 
                        :model-value="searchQuery"
                        @update:model-value="updateSearchQuery"
                        :placeholder="getPlaceholderText" 
                        class="w-full pl-14 pr-14 py-4 text-lg rounded-2xl border-0 bg-white/20 backdrop-blur-sm text-white placeholder:text-white/70 focus:ring-2 focus:ring-white/50 focus:bg-white/30 transition-all duration-300"
                    />
                    <div v-if="isLoading" class="absolute right-4 top-1/2 -translate-y-1/2">
                        <div class="animate-spin h-6 w-6 border-2 border-white/50 border-t-white rounded-full"></div>
                    </div>
                    <div v-else-if="searchQuery" class="absolute right-4 top-1/2 -translate-y-1/2">
                        <button @click="clearSearch()" class="text-white/70 hover:text-white transition-colors">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
                <Button @click="toggleFilters" class="bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 border-0 rounded-full p-3 transition-all duration-300">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1m-1 4H5m10 4H5m6 4h10" />
                    </svg>
                </Button>
            </div>

            <!-- Filter Dropdown -->
            <div v-if="showFilters" class="glassmorphism-panel rounded-2xl p-6 mb-6">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-sm font-medium text-white/90">Filter by Language</span>
                    <button @click="toggleFilters" class="text-white/70 hover:text-white transition-colors">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="flex gap-2">
                    <button
                        v-for="lang in languages"
                        :key="lang.code"
                        @click="updateLanguage(lang.code)"
                        :class="[
                            'px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 flex items-center gap-2',
                            selectedLanguage === lang.code 
                                ? 'bg-white/30 text-white backdrop-blur-sm' 
                                : 'text-white/70 hover:bg-white/20 hover:text-white'
                        ]"
                    >
                        <span>{{ lang.flag }}</span>
                        {{ lang.name }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Search Results -->
        <div v-if="hasResults || isLoading || error" class="glassmorphism-panel rounded-2xl overflow-hidden">
            <div class="px-6 py-3 border-b border-white/20 bg-white/10">
                <div class="text-sm text-white/90 flex items-center justify-between">
                    <span v-if="isLoading">Searching...</span>
                    <span v-else-if="error" class="text-red-300">{{ error }}</span>
                    <span v-else>{{ totalResults }} result{{ totalResults !== 1 ? 's' : '' }}</span>
                    <span v-if="searchTime > 0 && !isLoading" class="text-xs bg-white/20 text-white px-2 py-1 rounded-full backdrop-blur-sm">{{ searchTime }}ms</span>
                </div>
            </div>
            <div class="max-h-80 overflow-y-auto">
                <template v-for="type in ALL_RESULT_TYPES" :key="type">
                    <div v-for="(result, index) in searchResults[type]" 
                         :key="`${type}-${result.id}`" 
                         class="px-6 py-4 border-b border-white/10 last:border-b-0 hover:bg-white/10 transition-all duration-150 cursor-pointer"
                         @mouseover="handleResultHover(result, type)"
                         @click="handleResultClick(result, type)">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 rounded-full" 
                                 :style="{ backgroundColor: getHoverColor(type) }"></div>
                            <div class="flex-1 min-w-0">
                                <p class="text-base font-medium text-white truncate">
                                    {{ getDisplayName(result) }}
                                </p>
                                <div class="flex items-center space-x-3 mt-1">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-white/20 text-white/90 capitalize backdrop-blur-sm">
                                        {{ getResultType(type) }}
                                    </span>
                                    <p v-if="result.address" class="text-sm text-white/70 truncate">
                                        {{ result.address }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Glassmorphism Styles */
.glassmorphism-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glassmorphism-panel {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar for results */
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Smooth transitions */
* {
    transition: all 0.3s ease;
}
</style>
