<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';

const props = defineProps({
    center: {
        type: Array,
        default: () => [29.8739, -1.9403] // Rwanda center
    },
    zoom: {
        type: Number,
        default: 8.5
    },
    theme: {
        type: String,
        default: 'Default'
    },
    searchResults: {
        type: Object,
        default: () => ({})
    },
    selectedResult: {
        type: Object,
        default: null
    },
    interactive: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['map-ready', 'map-click']);

// --- STATE ---
const mapContainer = ref(null);
const map = ref(null);
const searchLayerIds = ref([]);
const searchSourceIds = ref([]);

// --- MAP CONFIGURATION ---
const MAP_THEMES = {
    'Default': {
        url: 'https://a.tile.openstreetmap.org/{z}/{x}/{y}.png',
        attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        paint: { 'raster-saturation': -0.8, 'raster-contrast': 0.2, 'raster-opacity': 0.9 }
    },
    'Black': {
        url: 'https://a.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png',
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
        paint: {}
    },
    'Satellite': {
        url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
        attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
        paint: {}
    }
};

const LAYER_STYLES = {
    province: { fillColor: '#3b82f6', borderColor: '#2563eb', fillOpacity: 0.15, borderWidth: 1.5 },
    district: { fillColor: '#22c55e', borderColor: '#16a34a', fillOpacity: 0.2, borderWidth: 1.5 },
    sector: { fillColor: '#a855f7', borderColor: '#9333ea', fillOpacity: 0.2, borderWidth: 1 },
    cell: { fillColor: '#f97316', borderColor: '#ea580c', fillOpacity: 0.2, borderWidth: 0.8 },
    village: { fillColor: '#ec4899', borderColor: '#db2777', fillOpacity: 0.2, borderWidth: 0.5 },
    healthFac: { fillColor: '#DC2626', borderColor: '#B91C1C' },
};

// --- LIFECYCLE ---
onMounted(() => {
    nextTick(() => {
        initializeMap();
    });
});

onUnmounted(() => {
    if (map.value) {
        map.value.remove();
        map.value = null;
    }
});

// --- WATCHERS ---
watch(() => props.theme, (newTheme) => {
    if (map.value) {
        applyMapTheme(newTheme);
    }
});

watch(() => props.searchResults, (newResults) => {
    if (map.value) {
        updateMapLayers(newResults);
    }
}, { deep: true });

watch(() => props.selectedResult, (newResult) => {
    if (map.value && newResult) {
        focusOnResult(newResult);
    }
});

// --- METHODS ---
const initializeMap = () => {
    if (!mapContainer.value) return;

    const theme = MAP_THEMES[props.theme] || MAP_THEMES.Default;
    
    map.value = new maplibregl.Map({
        container: mapContainer.value,
        style: {
            version: 8,
            sources: {
                'raster-tiles': {
                    type: 'raster',
                    tiles: [theme.url],
                    tileSize: 256,
                    attribution: theme.attribution,
                }
            },
            layers: [{
                id: 'background-tiles',
                type: 'raster',
                source: 'raster-tiles',
                paint: theme.paint
            }]
        },
        center: props.center,
        zoom: props.zoom,
        interactive: props.interactive,
        attributionControl: false
    });

    map.value.on('load', () => {
        emit('map-ready', map.value);
        updateMapLayers(props.searchResults);
    });

    if (props.interactive) {
        map.value.on('click', (e) => {
            emit('map-click', e);
        });
    }
};

const applyMapTheme = (themeName) => {
    if (!map.value) return;
    
    const theme = MAP_THEMES[themeName] || MAP_THEMES.Default;
    const source = map.value.getSource('raster-tiles');
    
    if (source) {
        source.tiles = [theme.url];
        source.attribution = theme.attribution;
        
        const layer = map.value.getLayer('background-tiles');
        if (layer) {
            map.value.setPaintProperty('background-tiles', 'raster-saturation', theme.paint['raster-saturation'] || 0);
            map.value.setPaintProperty('background-tiles', 'raster-contrast', theme.paint['raster-contrast'] || 0);
            map.value.setPaintProperty('background-tiles', 'raster-opacity', theme.paint['raster-opacity'] || 1);
        }
    }
};

const clearSearchLayers = () => {
    if (!map.value) return;
    
    searchLayerIds.value.forEach(layerId => {
        if (map.value.getLayer(layerId)) {
            map.value.removeLayer(layerId);
        }
    });
    
    searchSourceIds.value.forEach(sourceId => {
        if (map.value.getSource(sourceId)) {
            map.value.removeSource(sourceId);
        }
    });
    
    searchLayerIds.value = [];
    searchSourceIds.value = [];
};

const updateMapLayers = (results) => {
    if (!map.value || !results) return;
    
    clearSearchLayers();
    
    const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'healthFacs'];
    
    ALL_RESULT_TYPES.forEach(type => {
        const items = results[type] || [];
        items.forEach(item => {
            if (item.geojson && item.geojson.geometry) {
                addGeoJsonLayer(type.slice(0, -1), item.id, item.geojson);
            } else if (item.latitude && item.longitude) {
                addPointMarker(type.slice(0, -1), item.id, {
                    type: 'Feature',
                    geometry: {
                        type: 'Point',
                        coordinates: [item.longitude, item.latitude]
                    },
                    properties: { id: item.id, type: type.slice(0, -1) }
                });
            }
        });
    });
};

const addGeoJsonLayer = (type, id, feature) => {
    if (!map.value || !feature?.geometry) return;
    
    const sourceId = `${type}-source-${id}`;
    if (map.value.getSource(sourceId)) return;
    
    const layerStyle = LAYER_STYLES[type];
    
    map.value.addSource(sourceId, {
        type: 'geojson',
        data: feature,
        promoteId: 'id'
    });
    
    map.value.addLayer({
        id: `${type}-fill-${id}`,
        type: 'fill',
        source: sourceId,
        paint: {
            'fill-color': layerStyle.fillColor,
            'fill-opacity': layerStyle.fillOpacity
        }
    });
    
    map.value.addLayer({
        id: `${type}-border-${id}`,
        type: 'line',
        source: sourceId,
        paint: {
            'line-color': layerStyle.borderColor,
            'line-width': layerStyle.borderWidth
        }
    });
    
    searchLayerIds.value.push(`${type}-fill-${id}`, `${type}-border-${id}`);
    searchSourceIds.value.push(sourceId);
};

const addPointMarker = (type, id, feature) => {
    if (!map.value || !feature?.geometry) return;
    
    const sourceId = `${type}-point-source-${id}`;
    if (map.value.getSource(sourceId)) return;
    
    const layerStyle = LAYER_STYLES[type];
    
    map.value.addSource(sourceId, {
        type: 'geojson',
        data: feature,
        promoteId: 'id'
    });
    
    map.value.addLayer({
        id: `${type}-point-${id}`,
        type: 'circle',
        source: sourceId,
        paint: {
            'circle-color': layerStyle.fillColor || '#FF0000',
            'circle-radius': 6,
            'circle-stroke-color': layerStyle.borderColor || '#FFFFFF',
            'circle-stroke-width': 1.5,
            'circle-opacity': 0.9,
        }
    });
    
    searchLayerIds.value.push(`${type}-point-${id}`);
    searchSourceIds.value.push(sourceId);
};

const focusOnResult = (result) => {
    if (!map.value || !result) return;
    
    if (result.geojson?.bbox) {
        const bounds = new maplibregl.LngLatBounds(result.geojson.bbox);
        map.value.fitBounds(bounds, {
            padding: { top: 100, bottom: 100, left: 100, right: 100 },
            maxZoom: 14,
            duration: 1000
        });
    } else if (result.latitude && result.longitude) {
        map.value.flyTo({
            center: [result.longitude, result.latitude],
            zoom: 12,
            duration: 1000
        });
    }
};

// --- EXPOSE METHODS ---
defineExpose({
    getMap: () => map.value,
    focusOnResult,
    applyMapTheme,
    clearSearchLayers
});
</script>

<template>
    <div ref="mapContainer" class="w-full h-full"></div>
</template>

<style scoped>
/* Ensure map container takes full space */
.maplibregl-map {
    width: 100%;
    height: 100%;
}

/* Hide map controls for background use */
:deep(.maplibregl-ctrl-top-left),
:deep(.maplibregl-ctrl-top-right),
:deep(.maplibregl-ctrl-bottom-left),
:deep(.maplibregl-ctrl-bottom-right) {
    display: none;
}
</style>
