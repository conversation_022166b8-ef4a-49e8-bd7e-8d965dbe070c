<script setup>
import { ref } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import ApplicationMark from '@/Components/ApplicationMark.vue';
import Banner from '@/Components/Banner.vue';
import {
    Search,
    Navigation,
    FileText,
    Map,
    User,
    Settings,
    LogOut,
    LogIn,
    UserPlus,
    Menu,
    X,
    Home
} from 'lucide-vue-next';

defineProps({
    title: String,
});

// Navigation state management
const showingMobileMenu = ref(false);
const showingPopupMenu = ref(false);

const logout = () => {
    router.post(route('logout'));
    showingMobileMenu.value = false;
    showingPopupMenu.value = false;
};

// Mobile menu functions
const closeMobileMenu = () => {
    showingMobileMenu.value = false;
};

const toggleMobileMenu = () => {
    showingMobileMenu.value = !showingMobileMenu.value;
};

// Popup menu functions
const closePopupMenu = () => {
    showingPopupMenu.value = false;
};

const togglePopupMenu = () => {
    showingPopupMenu.value = !showingPopupMenu.value;
};
</script>

<template>
    <div>
        <Head :title="title" />
        <Banner />

        <div class="min-h-screen bg-cta-background-two">
            <!-- Premium Navigation Header -->
            <nav class="bg-white border-b border-gray-100 shadow-sm">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center h-20">
                        <!-- Logo Section -->
                        <div class="flex items-center space-x-8">
                            <Link :href="route('landing')" class="flex items-center group">
                                <ApplicationMark class="text-gorilla-primary group-hover:text-gorilla-primary/80 transition-colors" />
                            </Link>

                            <!-- Desktop Navigation - Rounded Pills -->
                            <div class="hidden lg:flex items-center bg-cta-background-one rounded-full p-1.5 space-x-1">
                                <Link :href="route('search')"
                                    class="px-6 py-2.5 rounded-full text-sm font-medium transition-all duration-200 flex items-center space-x-2"
                                    :class="route().current('search')
                                        ? 'bg-gorilla-primary text-white shadow-sm'
                                        : 'text-gorilla-primary-three hover:bg-white hover:text-gorilla-primary'">
                                    <Search class="w-4 h-4" />
                                    <span>Search</span>
                                </Link>
                                <Link :href="route('navigation')"
                                    class="px-6 py-2.5 rounded-full text-sm font-medium transition-all duration-200 flex items-center space-x-2"
                                    :class="route().current('navigation')
                                        ? 'bg-gorilla-primary text-white shadow-sm'
                                        : 'text-gorilla-primary-three hover:bg-white hover:text-gorilla-primary'">
                                    <Navigation class="w-4 h-4" />
                                    <span>Navigation</span>
                                </Link>
                                <Link :href="route('mapApi.search')"
                                    class="px-6 py-2.5 rounded-full text-sm font-medium transition-all duration-200 flex items-center space-x-2"
                                    :class="route().current('mapApi.search')
                                        ? 'bg-gorilla-primary text-white shadow-sm'
                                        : 'text-gorilla-primary-three hover:bg-white hover:text-gorilla-primary'">
                                    <FileText class="w-4 h-4" />
                                    <span>API Docs</span>
                                </Link>
                                <Link :href="route('myMap.index')"
                                    class="px-6 py-2.5 rounded-full text-sm font-medium transition-all duration-200 flex items-center space-x-2"
                                    :class="route().current('myMap.index')
                                        ? 'bg-gorilla-primary text-white shadow-sm'
                                        : 'text-gorilla-primary-three hover:bg-white hover:text-gorilla-primary'">
                                    <Map class="w-4 h-4" />
                                    <span>My Maps</span>
                                </Link>
                            </div>
                        </div>

                        <!-- Right Side Actions -->
                        <div class="flex items-center space-x-4">
                            <!-- Mobile Menu Button -->
                            <button @click="toggleMobileMenu"
                                class="lg:hidden p-2.5 rounded-xl bg-cta-background-one hover:bg-gorilla-primary hover:text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gorilla-primary/20">
                                <Menu v-if="!showingMobileMenu" class="w-5 h-5 text-gorilla-primary-three" />
                                <X v-else class="w-5 h-5 text-gorilla-primary-three" />
                            </button>

                            <!-- Desktop User Menu Button -->
                            <button @click="togglePopupMenu"
                                class="hidden lg:flex items-center space-x-2 p-2.5 rounded-xl bg-cta-background-one hover:bg-gorilla-primary hover:text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gorilla-primary/20">
                                <User class="w-5 h-5 text-gorilla-primary-three" />
                                <span class="text-sm font-medium text-gorilla-primary-three">Menu</span>
                            </button>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Mobile Navigation Menu -->
            <div v-if="showingMobileMenu"
                class="lg:hidden fixed inset-0 z-50 bg-black bg-opacity-50 transition-opacity duration-300"
                @click="closeMobileMenu">
                <div class="fixed inset-y-0 left-0 w-80 bg-white transform transition-transform duration-300 ease-in-out"
                    @click.stop>
                    <div class="flex flex-col h-full">
                        <!-- Mobile Header -->
                        <div class="flex items-center justify-between p-6 border-b border-gray-100">
                            <ApplicationMark class="text-gorilla-primary" />
                            <button @click="closeMobileMenu"
                                class="p-2 rounded-xl hover:bg-cta-background-one transition-colors">
                                <X class="w-5 h-5 text-gorilla-primary-three" />
                            </button>
                        </div>

                        <!-- Mobile Navigation Links -->
                        <nav class="flex-1 px-6 py-6 space-y-2">
                            <Link :href="route('search')"
                                class="flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200"
                                :class="route().current('search')
                                    ? 'bg-gorilla-primary text-white'
                                    : 'text-gorilla-primary-three hover:bg-cta-background-one hover:text-gorilla-primary'"
                                @click="closeMobileMenu">
                                <Search class="w-5 h-5" />
                                <span class="font-medium">Search</span>
                            </Link>
                            <Link :href="route('navigation')"
                                class="flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200"
                                :class="route().current('navigation')
                                    ? 'bg-gorilla-primary text-white'
                                    : 'text-gorilla-primary-three hover:bg-cta-background-one hover:text-gorilla-primary'"
                                @click="closeMobileMenu">
                                <Navigation class="w-5 h-5" />
                                <span class="font-medium">Navigation</span>
                            </Link>
                            <Link :href="route('mapApi.search')"
                                class="flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200"
                                :class="route().current('mapApi.search')
                                    ? 'bg-gorilla-primary text-white'
                                    : 'text-gorilla-primary-three hover:bg-cta-background-one hover:text-gorilla-primary'"
                                @click="closeMobileMenu">
                                <FileText class="w-5 h-5" />
                                <span class="font-medium">API Documentation</span>
                            </Link>
                            <Link :href="route('myMap.index')"
                                class="flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200"
                                :class="route().current('myMap.index')
                                    ? 'bg-gorilla-primary text-white'
                                    : 'text-gorilla-primary-three hover:bg-cta-background-one hover:text-gorilla-primary'"
                                @click="closeMobileMenu">
                                <Map class="w-5 h-5" />
                                <span class="font-medium">My Maps</span>
                            </Link>

                            <!-- Mobile User Section -->
                            <div class="pt-6 mt-6 border-t border-gray-100">
                                <template v-if="$page.props.auth.user !== null">
                                    <Link :href="route('dashboard')"
                                        class="flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200"
                                        :class="route().current('dashboard')
                                            ? 'bg-gorilla-primary text-white'
                                            : 'text-gorilla-primary-three hover:bg-cta-background-one hover:text-gorilla-primary'"
                                        @click="closeMobileMenu">
                                        <Home class="w-5 h-5" />
                                        <span class="font-medium">Dashboard</span>
                                    </Link>
                                    <Link :href="route('profile.show')"
                                        class="flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200"
                                        :class="route().current('profile.show')
                                            ? 'bg-gorilla-primary text-white'
                                            : 'text-gorilla-primary-three hover:bg-cta-background-one hover:text-gorilla-primary'"
                                        @click="closeMobileMenu">
                                        <User class="w-5 h-5" />
                                        <span class="font-medium">Profile</span>
                                    </Link>
                                    <button @click="logout"
                                        class="w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-red-600 hover:bg-red-50 transition-all duration-200">
                                        <LogOut class="w-5 h-5" />
                                        <span class="font-medium">Log Out</span>
                                    </button>
                                </template>
                                <template v-else>
                                    <Link :href="route('login')"
                                        class="flex items-center space-x-3 px-4 py-3 rounded-xl bg-gorilla-primary text-white hover:bg-gorilla-primary/90 transition-all duration-200"
                                        @click="closeMobileMenu">
                                        <LogIn class="w-5 h-5" />
                                        <span class="font-medium">Login</span>
                                    </Link>
                                    <Link :href="route('register')"
                                        class="flex items-center space-x-3 px-4 py-3 rounded-xl text-gorilla-primary-three hover:bg-cta-background-one hover:text-gorilla-primary transition-all duration-200 mt-2"
                                        @click="closeMobileMenu">
                                        <UserPlus class="w-5 h-5" />
                                        <span class="font-medium">Register</span>
                                    </Link>
                                </template>
                            </div>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Desktop User Menu Popup -->
            <div v-if="showingPopupMenu"
                class="hidden lg:block fixed inset-0 bg-black bg-opacity-30 z-40 transition-opacity duration-300"
                @click="closePopupMenu"></div>

            <div v-if="showingPopupMenu" class="hidden lg:block fixed inset-0 flex items-center justify-center z-50 p-4">
                <div :class="{ 'scale-100 opacity-100': showingPopupMenu, 'scale-95 opacity-0': !showingPopupMenu }"
                    class="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 border border-gray-100">
                    <div class="p-8">
                        <!-- Header -->
                        <div class="flex items-center justify-between mb-8">
                            <h2 class="text-2xl font-bold text-gorilla-primary-three">Menu</h2>
                            <button @click="closePopupMenu"
                                class="p-2 rounded-xl hover:bg-cta-background-one transition-colors">
                                <X class="w-6 h-6 text-gorilla-primary-three" />
                            </button>
                        </div>

                        <!-- User Section - Not Logged In -->
                        <div v-if="$page.props.auth.user === null" class="mb-8 p-6 bg-cta-background-one rounded-xl text-center">
                            <div class="mb-6">
                                <div class="w-16 h-16 bg-gorilla-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <User class="w-8 h-8 text-gorilla-primary" />
                                </div>
                                <p class="text-gorilla-primary-three font-semibold text-lg">Join OnRwanda Geo</p>
                                <p class="text-gray-600 text-sm mt-2">Access premium mapping features and save your locations</p>
                            </div>
                            <div class="space-y-3">
                                <Link :href="route('login')"
                                    class="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-gorilla-primary text-white rounded-xl hover:bg-gorilla-primary/90 transition-all duration-200 font-medium"
                                    @click="closePopupMenu">
                                    <LogIn class="w-5 h-5" />
                                    <span>Login</span>
                                </Link>
                                <Link :href="route('register')"
                                    class="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-white text-gorilla-primary border border-gorilla-primary/20 rounded-xl hover:bg-gorilla-primary/5 transition-all duration-200 font-medium"
                                    @click="closePopupMenu">
                                    <UserPlus class="w-5 h-5" />
                                    <span>Register</span>
                                </Link>
                            </div>
                        </div>

                        <!-- User Section - Logged In -->
                        <div v-if="$page.props.auth.user !== null" class="mb-8 p-6 bg-cta-background-one rounded-xl text-center">
                            <div class="mb-4">
                                <img v-if="$page.props.jetstream.managesProfilePhotos"
                                    :src="$page.props.auth.user.profile_photo_url" :alt="$page.props.auth.user.name"
                                    class="w-16 h-16 rounded-full object-cover mx-auto mb-3 border-2 border-gorilla-primary/20" />
                                <div v-else class="w-16 h-16 bg-gorilla-primary rounded-full flex items-center justify-center mx-auto mb-3">
                                    <User class="w-8 h-8 text-white" />
                                </div>
                                <div>
                                    <p class="font-bold text-gorilla-primary-three text-lg">{{ $page.props.auth.user.name }}</p>
                                    <p class="text-gray-600 text-sm">{{ $page.props.auth.user.email }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation Links -->
                        <nav class="space-y-2 mb-8">
                            <!-- Main Navigation -->
                            <div class="mb-6">
                                <h3 class="text-sm font-semibold text-gorilla-primary-three mb-3 px-2">Navigation</h3>
                                <div class="space-y-1">
                                    <Link :href="route('search')"
                                        class="flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 font-medium group"
                                        :class="route().current('search')
                                            ? 'bg-gorilla-primary text-white'
                                            : 'text-gorilla-primary-three hover:bg-cta-background-one hover:text-gorilla-primary'"
                                        @click="closePopupMenu">
                                        <div class="p-1.5 rounded-lg transition-colors"
                                            :class="route().current('search')
                                                ? 'bg-white/20'
                                                : 'bg-gorilla-primary/10 group-hover:bg-gorilla-primary/20'">
                                            <Search class="w-4 h-4" />
                                        </div>
                                        <span>Search Locations</span>
                                    </Link>

                                    <Link :href="route('navigation')"
                                        class="flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 font-medium group"
                                        :class="route().current('navigation')
                                            ? 'bg-gorilla-primary text-white'
                                            : 'text-gorilla-primary-three hover:bg-cta-background-one hover:text-gorilla-primary'"
                                        @click="closePopupMenu">
                                        <div class="p-1.5 rounded-lg transition-colors"
                                            :class="route().current('navigation')
                                                ? 'bg-white/20'
                                                : 'bg-gorilla-primary/10 group-hover:bg-gorilla-primary/20'">
                                            <Navigation class="w-4 h-4" />
                                        </div>
                                        <span>Navigation</span>
                                    </Link>

                                    <Link :href="route('mapApi.search')"
                                        class="flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 font-medium group"
                                        :class="route().current('mapApi.search')
                                            ? 'bg-gorilla-primary text-white'
                                            : 'text-gorilla-primary-three hover:bg-cta-background-one hover:text-gorilla-primary'"
                                        @click="closePopupMenu">
                                        <div class="p-1.5 rounded-lg transition-colors"
                                            :class="route().current('mapApi.search')
                                                ? 'bg-white/20'
                                                : 'bg-gorilla-primary/10 group-hover:bg-gorilla-primary/20'">
                                            <FileText class="w-4 h-4" />
                                        </div>
                                        <span>API Documentation</span>
                                    </Link>

                                    <Link :href="route('myMap.index')"
                                        class="flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 font-medium group"
                                        :class="route().current('myMap.index')
                                            ? 'bg-gorilla-primary text-white'
                                            : 'text-gorilla-primary-three hover:bg-cta-background-one hover:text-gorilla-primary'"
                                        @click="closePopupMenu">
                                        <div class="p-1.5 rounded-lg transition-colors"
                                            :class="route().current('myMap.index')
                                                ? 'bg-white/20'
                                                : 'bg-gorilla-primary/10 group-hover:bg-gorilla-primary/20'">
                                            <Map class="w-4 h-4" />
                                        </div>
                                        <span>My Maps</span>
                                    </Link>
                                </div>
                            </div>

                            <!-- User Account Links -->
                            <template v-if="$page.props.auth.user !== null">
                                <div>
                                    <h3 class="text-sm font-semibold text-gorilla-primary-three mb-3 px-2">Account</h3>
                                    <div class="space-y-1">
                                        <Link :href="route('dashboard')"
                                            class="flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 font-medium group"
                                            :class="route().current('dashboard')
                                                ? 'bg-gorilla-primary text-white'
                                                : 'text-gorilla-primary-three hover:bg-cta-background-one hover:text-gorilla-primary'"
                                            @click="closePopupMenu">
                                            <div class="p-1.5 rounded-lg transition-colors"
                                                :class="route().current('dashboard')
                                                    ? 'bg-white/20'
                                                    : 'bg-gorilla-primary/10 group-hover:bg-gorilla-primary/20'">
                                                <Home class="w-4 h-4" />
                                            </div>
                                            <span>Dashboard</span>
                                        </Link>

                                        <Link :href="route('profile.show')"
                                            class="flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 font-medium group"
                                            :class="route().current('profile.show')
                                                ? 'bg-gorilla-primary text-white'
                                                : 'text-gorilla-primary-three hover:bg-cta-background-one hover:text-gorilla-primary'"
                                            @click="closePopupMenu">
                                            <div class="p-1.5 rounded-lg transition-colors"
                                                :class="route().current('profile.show')
                                                    ? 'bg-white/20'
                                                    : 'bg-gorilla-primary/10 group-hover:bg-gorilla-primary/20'">
                                                <User class="w-4 h-4" />
                                            </div>
                                            <span>Profile Settings</span>
                                        </Link>

                                        <Link v-if="$page.props.jetstream.hasApiFeatures" :href="route('api-tokens.index')"
                                            class="flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 font-medium group"
                                            :class="route().current('api-tokens.index')
                                                ? 'bg-gorilla-primary text-white'
                                                : 'text-gorilla-primary-three hover:bg-cta-background-one hover:text-gorilla-primary'"
                                            @click="closePopupMenu">
                                            <div class="p-1.5 rounded-lg transition-colors"
                                                :class="route().current('api-tokens.index')
                                                    ? 'bg-white/20'
                                                    : 'bg-gorilla-primary/10 group-hover:bg-gorilla-primary/20'">
                                                <Settings class="w-4 h-4" />
                                            </div>
                                            <span>API Tokens</span>
                                        </Link>
                                    </div>
                                </div>
                            </template>
                        </nav>

                        <!-- Logout Button -->
                        <div v-if="$page.props.auth.user !== null" class="pt-6 border-t border-gray-100">
                            <button @click="logout"
                                class="w-full flex items-center justify-center space-x-3 px-6 py-3 bg-red-50 text-red-600 rounded-xl hover:bg-red-100 transition-all duration-200 font-medium">
                                <LogOut class="w-5 h-5" />
                                <span>Log Out</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <main class="bg-cta-background-two min-h-screen">
                <slot />
            </main>
        </div>
    </div>
</template>

<style scoped>
/* Custom scrollbar for popup menu */
.popup-menu::-webkit-scrollbar {
    width: 4px;
}

.popup-menu::-webkit-scrollbar-track {
    background: #edefeb;
    border-radius: 2px;
}

.popup-menu::-webkit-scrollbar-thumb {
    background: #1A773E;
    border-radius: 2px;
}

.popup-menu::-webkit-scrollbar-thumb:hover {
    background: #1C5172;
}

/* Smooth transitions for mobile menu */
.mobile-menu-enter-active,
.mobile-menu-leave-active {
    transition: transform 0.3s ease-in-out;
}

.mobile-menu-enter-from {
    transform: translateX(-100%);
}

.mobile-menu-leave-to {
    transform: translateX(-100%);
}

/* Premium shadow for navigation */
.nav-shadow {
    box-shadow: 0 1px 3px 0 rgba(26, 119, 62, 0.1), 0 1px 2px 0 rgba(26, 119, 62, 0.06);
}

/* Focus states for accessibility */
.focus-ring:focus {
    outline: 2px solid #1A773E;
    outline-offset: 2px;
}
</style>