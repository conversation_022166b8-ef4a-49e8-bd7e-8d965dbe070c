<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { Lock, LockOpen, CircleCheck, CircleX } from 'lucide-vue-next';

const props = defineProps({
    placeMapId: { type: String, required: true },
    dataMapId: { type: String, required: true }
});

const dataMap = ref(null);
const placeMapItems = ref([]);
const dataMapItems = ref([]);
const itemsPagination = ref(null);
const itemsPerPage = ref(10);
const itemsPerPageOptions = ref([5, 10, 25, 50]);
const alert = ref({ show: false, type: '', message: '' });
const showCreateEditItemModal = ref(false);
const isEditingItem = ref(false);
const editingItemId = ref(null);
const itemForm = ref({
    name: '',
    description: '',
    placeMapItemID: '',
    type: 'multi',
    visibility: 'public',
    status: 'active',
    processing: false,
    errors: {}
});


const showAlert = (type, message, duration = 4000) => {
    alert.value = { show: true, type, message };
    setTimeout(() => alert.value.show = false, duration);
};

const fetchDataMapDetails = async () => {
    try {
        const response = await axios.get(route('dataMap.getById', { placeMapId: props.placeMapId, dataMapId: props.dataMapId }));
        dataMap.value = response.data;
    } catch (error) {
        showAlert('error', 'Error fetching data map details.');
    }
};

const fetchPlaceMapItems = async () => {
    try {
        const response = await axios.get(route('placeMap.getPlaceMapItem', { placeMapId: props.placeMapId, perPage: 9999 }));
        placeMapItems.value = response.data.data;
    } catch (error) {
        showAlert('error', 'Error fetching place map items.');
    }
};

const fetchDataMapItems = async (page = 1, perPage = itemsPerPage.value) => {
    try {
        const response = await axios.get(route('dataMap.getDataMapItem', { dataMapId: props.dataMapId, page, perPage }));
        dataMapItems.value = response.data.data;
        itemsPagination.value = {
            currentPage: response.data.currentPage,
            lastPage: response.data.lastPage,
            total: response.data.total,
            perPage: response.data.perPage,
        };
    } catch (error) {
        showAlert('error', 'Error fetching data map items.');
    }
};

onMounted(async () => {
    await fetchDataMapDetails();
    await fetchPlaceMapItems();
    await fetchDataMapItems();
});

const openCreateItemModal = () => {
    isEditingItem.value = false;
    editingItemId.value = null;
    itemForm.value = {
        name: '',
        description: '',
        placeMapItemID: '',
        type: 'multi',
        visibility: 'public',
        status: 'active',
        processing: false,
        errors: {}
    };
    showCreateEditItemModal.value = true;
};

const openEditItemModal = (item) => {
    isEditingItem.value = true;
    editingItemId.value = item.id;
    itemForm.value = {
        name: item.name,
        description: item.description,
        placeMapItemID: item.place_map_item_id,
        type: item.type || 'multi',
        visibility: item.visibility || 'public',
        status: item.status || 'active',
        processing: false,
        errors: {}
    };
    showCreateEditItemModal.value = true;
};

const submitDataItem = async () => {
    itemForm.value.processing = true;
    itemForm.value.errors = {};

    const url = isEditingItem.value
        ? route('dataMap.updateDataItem', { dataMapId: props.dataMapId, dataMapItemId: editingItemId.value })
        : route('dataMap.createDataItem', { dataMapId: props.dataMapId });

    try {
        const response = await axios[isEditingItem.value ? 'put' : 'post'](url, itemForm.value);
        showAlert('success', response.data.message);
        await fetchDataMapItems(itemsPagination.value?.currentPage || 1, itemsPerPage.value);
        showCreateEditItemModal.value = false;
    } catch (error) {
        showAlert('error', error.response?.data?.message || 'An error occurred.');
        if (error.response?.status === 422) {
            itemForm.value.errors = error.response.data.errors;
        }
    } finally {
        itemForm.value.processing = false;
    }
};
</script>

<template>
    <AppLayout :title="dataMap ? `Items in ${dataMap.name}` : 'Data Map Items'">
        <!-- Alert -->
        <div v-if="alert.show" class="fixed bottom-6 left-6 z-50 w-full max-w-sm rounded-lg border p-4  flex items-start"
             :class="alert.type === 'success' ? 'border-green-300 bg-green-50 text-green-800' : 'border-red-300 bg-red-50 text-red-800'">
            <div class="ml-3 flex-1">
                <p class="text-sm font-medium">{{ alert.message }}</p>
            </div>
            <button @click="alert.show = false" class="ml-auto bg-transparent rounded-md p-1.5 text-gray-500 hover:text-gray-800">
                <span class="sr-only">Dismiss</span>
                <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </button>
        </div>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div v-if="dataMap" class="bg-white overflow-hidden  border border-gray-200 sm:rounded-lg p-6 md:p-8">
                    <div class="mb-6">
                        <Link :href="route('myMapData.index', { placeMapId: placeMapId })" class="text-sm text-indigo-600 hover:underline mb-4 inline-block">&larr; Back to Data Maps</Link>
                        <div class="flex flex-col md:flex-row justify-between md:items-center gap-4">
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">Items in {{ dataMap.name }}</h1>
                                <p class="mt-1 text-md text-gray-600">{{ dataMap.description }}</p>
                            </div>
                            <button @click="openCreateItemModal" class="inline-flex items-center justify-center rounded-md text-sm font-medium bg-black text-white hover:bg-gray-800 px-4 py-2">Add New Data Item</button>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 bg-white">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Place Map Item</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr v-for="item in dataMapItems" :key="item.id" class="hover:bg-gray-50 transition-colors duration-200">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ item.name }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.place_map_item?.name || 'N/A' }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex justify-end space-x-2">
                                            <Link :href="`/map/data/${props.placeMapId}/${props.dataMapId}/${item.id}/custom-data`"
                                                  class="font-medium text-green-600 hover:text-green-800 hover:underline">Custom Data</Link>
                                            <button @click="openEditItemModal(item)" class="font-medium text-indigo-600 hover:text-indigo-800 hover:underline">Edit</button>
                                        </div>
                                    </td>
                                 </tr>
                                <tr v-if="dataMapItems.length === 0">
                                    <td colspan="3" class="text-center text-gray-500 py-12">
                                        <p class="text-lg">No data items found for this map.</p>
                                        <p class="text-sm">Get started by adding a new data item.</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div v-if="itemsPagination && itemsPagination.total > 0" class="mt-6 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <span>Per Page:</span>
                            <select id="itemsPerPage" v-model="itemsPerPage" @change="fetchDataMapItems(1, $event.target.value)" class="rounded-md border-gray-300  focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 text-sm py-1">
                                <option v-for="option in itemsPerPageOptions" :key="option" :value="option">{{ option }}</option>
                            </select>
                            <span class="hidden sm:inline">| Total Items: {{ itemsPagination.total }}</span>
                        </div>
                        <nav v-if="itemsPagination.lastPage > 1" class="flex items-center justify-center">
                            <button @click="fetchDataMapItems(itemsPagination.currentPage - 1, itemsPerPage)" :disabled="itemsPagination.currentPage <= 1" 
                                    class="px-3 py-1 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                &laquo; Prev
                            </button>
                            <span class="px-4 py-1 text-sm text-gray-800 bg-gray-100 border-t border-b border-gray-300">
                                Page {{ itemsPagination.currentPage }} of {{ itemsPagination.lastPage }}
                            </span>
                            <button @click="fetchDataMapItems(itemsPagination.currentPage + 1, itemsPerPage)" :disabled="itemsPagination.currentPage >= itemsPagination.lastPage"
                                    class="px-3 py-1 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                Next &raquo;
                            </button>
                        </nav>
                    </div>
                </div>
                <div v-else class="bg-white overflow-hidden  border border-gray-200 sm:rounded-lg p-6 md:p-8">
                    <!-- Loading state -->
                    <div class="mb-6">
                        <div class="animate-pulse bg-gray-200 rounded-md h-4 w-1/4 mb-4"></div>
                        <div class="flex flex-col md:flex-row justify-between md:items-center gap-4">
                            <div>
                                <div class="animate-pulse bg-gray-200 rounded-md h-8 w-48 mb-2"></div>
                                <div class="animate-pulse bg-gray-200 rounded-md h-4 w-64"></div>
                            </div>
                            <div class="animate-pulse bg-gray-200 rounded-md h-10 w-32"></div>
                        </div>
                    </div>
                    <div class="divide-y divide-gray-200">
                        <div class="py-4" v-for="i in 5" :key="i">
                            <div class="flex items-center justify-between">
                                <div class="flex-1 space-y-2">
                                    <div class="animate-pulse bg-gray-200 rounded-md h-4 w-1/3"></div>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="animate-pulse bg-gray-200 rounded-md h-8 w-16"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal -->
        <div v-if="showCreateEditItemModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div class="bg-white rounded-lg  p-8 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                <h2 class="text-2xl font-bold mb-6">{{ isEditingItem ? 'Edit' : 'Create' }} Data Item</h2>
                <form @submit.prevent="submitDataItem">
                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium block mb-1" for="itemName">Item Name</label>
                            <input id="itemName" v-model="itemForm.name" placeholder="Item Name"
                                   class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <div v-if="itemForm.errors.name" class="text-red-500 text-sm mt-1">{{ itemForm.errors.name[0] }}</div>
                        </div>
                        <div>
                            <label class="text-sm font-medium block mb-1" for="itemDescription">Description</label>
                            <textarea id="itemDescription" v-model="itemForm.description" placeholder="Description"
                                      class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" rows="2"></textarea>
                            <div v-if="itemForm.errors.description" class="text-red-500 text-sm mt-1">{{ itemForm.errors.description[0] }}</div>
                        </div>
                        <div>
                            <label class="text-sm font-medium block mb-1" for="placeMapItem">Associate with Place Map Item</label>
                            <select id="placeMapItem" v-model="itemForm.placeMapItemID"
                                    class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">Select a Place Map Item</option>
                                <option v-for="placeItem in placeMapItems" :key="placeItem.id" :value="placeItem.id">{{ placeItem.name }}</option>
                            </select>
                            <div v-if="itemForm.errors.placeMapItemID" class="text-red-500 text-sm mt-1">{{ itemForm.errors.placeMapItemID[0] }}</div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="p-3 border rounded-lg">
                                <label class="text-sm font-medium block mb-2">Visibility</label>
                                <div class="flex items-center justify-between">
                                    <span class="flex items-center text-sm text-gray-700">
                                        <LockOpen v-if="itemForm.visibility === 'public'" class="w-5 h-5 mr-2 text-green-600" />
                                        <Lock v-else class="w-5 h-5 mr-2 text-gray-500" />
                                        {{ itemForm.visibility === 'public' ? 'Public' : 'Private' }}
                                    </span>
                                    <button type="button" @click="itemForm.visibility = itemForm.visibility === 'public' ? 'private' : 'public'" role="switch" :aria-checked="itemForm.visibility === 'public'"
                                            :class="['peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                                                     itemForm.visibility === 'public' ? 'bg-green-500' : 'bg-gray-300']">
                                        <span :class="['pointer-events-none block h-5 w-5 rounded-full bg-white  ring-0 transition-transform',
                                                       itemForm.visibility === 'public' ? 'translate-x-5' : 'translate-x-0']" />
                                    </button>
                                </div>
                                <div v-if="itemForm.errors.visibility" class="text-red-500 text-sm mt-1">{{ itemForm.errors.visibility[0] }}</div>
                            </div>
                            <div class="p-3 border rounded-lg">
                                <label class="text-sm font-medium block mb-2">Status</label>
                                <div class="flex items-center justify-between">
                                    <span class="flex items-center text-sm text-gray-700">
                                        <CircleCheck v-if="itemForm.status === 'active'" class="w-5 h-5 mr-2 text-green-600" />
                                        <CircleX v-else class="w-5 h-5 mr-2 text-red-600" />
                                        {{ itemForm.status === 'active' ? 'Active' : 'Inactive' }}
                                    </span>
                                    <button type="button" @click="itemForm.status = itemForm.status === 'active' ? 'inactive' : 'active'" role="switch" :aria-checked="itemForm.status === 'active'"
                                            :class="['peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                                                     itemForm.status === 'active' ? 'bg-green-500' : 'bg-red-500']">
                                        <span :class="['pointer-events-none block h-5 w-5 rounded-full bg-white  ring-0 transition-transform',
                                                       itemForm.status === 'active' ? 'translate-x-5' : 'translate-x-0']" />
                                    </button>
                                </div>
                                <div v-if="itemForm.errors.status" class="text-red-500 text-sm mt-1">{{ itemForm.errors.status[0] }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-4">
                        <button type="button" @click="showCreateEditItemModal = false"
                                class="inline-flex items-center justify-center rounded-md text-sm font-medium bg-gray-200 text-gray-800 hover:bg-gray-300 px-4 py-2">Cancel</button>
                        <button type="submit" :disabled="itemForm.processing"
                                class="inline-flex items-center justify-center rounded-md text-sm font-medium bg-black text-white hover:bg-gray-800 px-4 py-2 disabled:opacity-50">
                            {{ itemForm.processing ? 'Saving...' : (isEditingItem ? 'Update' : 'Create') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </AppLayout>
</template>
