<script setup>
import AppLayout from "@/Layouts/AppLayout.vue";
import { <PERSON>, <PERSON> } from "@inertiajs/vue3";
import { ref, onMounted, computed, defineComponent, h, watch, nextTick } from "vue";
import axios from "axios";
import { debounce } from "lodash";
import maplibregl from "maplibre-gl";
import "maplibre-gl/dist/maplibre-gl.css";

// --- Icon Component ---
const MapIcon = defineComponent({
    props: ["name", "size", "class"],
    setup(props) {
        const size = props.size || 20;
        const iconPaths = {
            pin: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z"/><circle cx="12" cy="9" r="2.5"/>`,
            star: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>`,
            heart: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>`,
            flag: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"/><line x1="4" y1="22" x2="4" y2="15"/>`,
            home: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/>`,
            work: `<rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/><line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/><line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/>`,
            cafe: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 8h1a4 4 0 0 1 0 8h-1"/><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z" stroke="currentColor" stroke-width="2" fill="none"/>`,
            park: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 22v-6m0 0l-3-3m3 3l3-3m-3-10v10m0-10l-3-3m3 3l3-3M12 2v10"/><path d="M5 12h14" stroke="currentColor" stroke-width="2"/>`,
            restaurant: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V2"/><path d="M7 2v20"/><path d="M21 15v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7"/>`,
            shopping: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/><line x1="3" y1="6" x2="21" y2="6"/><path d="M16 10a4 4 0 0 1-8 0"/>`,
            hospital: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v12m6-6H6"/><rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>`,
            school: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c3 3 9 3 12 0v-5"/>`,
            plus: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v14m-7-7h14"/>`,
            close: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 6L6 18M6 6l12 12"/>`,
            search: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"/>`,
            lock: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 0 0-8 0v4h8v-4z"/><rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/>`,
            unlock: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11V7a5 5 0 0 1 9.9-1"/><rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/>`,
            edit: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>`,
            map: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 6v16l7-4 8 4 7-4V2l-7 4-8-4-7 4z"/>`,
            location: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>`,
        };
        return () => h("svg", { class: props.class, width: size, height: size, viewBox: "0 0 24 24", fill: "none", innerHTML: iconPaths[props.name] || iconPaths.pin });
    },
});

// --- Props ---
const props = defineProps({
    placeMapId: Number,
    geoFancingfields: Array,
});

// --- State ---
const placeMap = ref(null);
const alert = ref({ show: false, type: "", message: "" });
const placeMapItems = ref([]);
const itemsPagination = ref(null);
const itemsPerPage = ref(10);
const itemsPerPageOptions = ref([5, 10, 25, 50]);
const loading = ref({ details: true, items: true, map: false });
const searchQuery = ref("");
const selectedItem = ref(null);

// --- Map Types Configuration ---
const MAP_TYPES = {
    ITINERARY: 'itinerary',
    ADMINISTRATION: 'administration',
    GENERAL: 'general',
    TRACKING: 'tracking'
};

const getInitialItemForm = () => ({
    name: "", description: "", address: "", latitude: "", longitude: "",
    image: "pin", type: "place", locationID: null, visibility: "private",
    status: "active", dataItems: [], processing: false, errors: {},
});

const editorState = ref({
    isOpen: false,
    isEditing: false,
    itemId: null,
    form: getInitialItemForm(),
});

const mapIcons = ref([
    { name: "pin", label: "Location Pin" }, { name: "star", label: "Star" },
    { name: "heart", label: "Heart" }, { name: "flag", label: "Flag" },
    { name: "home", label: "Home" }, { name: "work", label: "Work" },
    { name: "cafe", label: "Cafe" }, { name: "park", label: "Park" },
    { name: "restaurant", label: "Restaurant" }, { name: "shopping", label: "Shopping" },
    { name: "hospital", label: "Hospital" }, { name: "school", label: "School" },
]);

// --- Map and Search State ---
const locationSearchQuery = ref("");
const locationSearchResults = ref(null);
const isSearching = ref(false);
const mapContainer = ref(null);
const map = ref(null);
const markers = ref([]);
const showMapView = ref(true); // Always show map initially
const showMapToggle = ref(true); // User can toggle map visibility

// --- Filter State for Administration Maps ---
const showFilters = ref(false);
const searchFilters = ref({
    type: 'all', // all, province, district, sector, cell, village, healthFac
    language: 'en' // en, rw, fr
});

// --- Geofencing State ---
const showGeofencing = ref(false);
const geofencingForm = ref({
    apiKey: 'no',
    webHookStatus: 'no',
    webHookUrl: '',
    geoFancing: 'no',
    geoFancingData: [],
    processing: false,
    errors: {}
});

// --- Filter Configuration ---
const FILTER_TYPES = [
    { code: 'all', name: 'All Types', icon: 'M3 4a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2zM3 16a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z' },
    { code: 'province', name: 'Province', icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z' },
    { code: 'district', name: 'District', icon: 'M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z' },
    { code: 'sector', name: 'Sector', icon: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z' },
    { code: 'cell', name: 'Cell', icon: 'M21 16V8a2 2 0 00-1-1.73l-7-4a2 2 0 00-2 0l-7 4A2 2 0 003 8v8a2 2 0 001 1.73l7 4a2 2 0 002 0l7-4A2 2 0 0021 16z' },
    { code: 'village', name: 'Village', icon: 'M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z' },
    { code: 'healthFac', name: 'Health Facility', icon: 'M22 12h-4l-3 9L9 3l-3 9H2' }
];

const FILTER_LANGUAGES = [
    { code: 'en', name: 'English' },
    { code: 'rw', name: 'Kinyarwanda' },
    { code: 'fr', name: 'Français' }
];

// --- Computed Properties ---
const customFields = computed(() => {
    if (placeMap.value && placeMap.value.customFields) {
        try {
            const fields = typeof placeMap.value.customFields === "string" ? JSON.parse(placeMap.value.customFields) : placeMap.value.customFields;
            return Array.isArray(fields) ? fields : [];
        } catch (e) {
            console.error("Error parsing custom fields:", e);
            return [];
        }
    }
    return [];
});

const formattedSearchResults = computed(() => {
    if (!locationSearchResults.value) return [];
    const { provinces, districts, sectors, cells, villages, healthFacs } = locationSearchResults.value;
    const results = [];
    const addResults = (items, type) => items?.forEach(item => results.push({ ...item, type }));
    addResults(provinces, "Province"); addResults(districts, "District");
    addResults(sectors, "Sector"); addResults(cells, "Cell");
    addResults(villages, "Village"); addResults(healthFacs, "Health Facility");
    return results;
});

// --- Map Type Logic ---
const isAdministrationType = computed(() => placeMap.value?.type === MAP_TYPES.ADMINISTRATION);
const isTrackingType = computed(() => placeMap.value?.type === MAP_TYPES.TRACKING);
const isGeneralOrItineraryType = computed(() =>
    placeMap.value?.type === MAP_TYPES.GENERAL || placeMap.value?.type === MAP_TYPES.ITINERARY
);

const shouldShowLocationSearch = computed(() => {
    return isAdministrationType.value && !editorState.value.isEditing;
});

const shouldShowCoordinateInputs = computed(() => {
    return isGeneralOrItineraryType.value || isTrackingType.value;
});

const allowCoordinateEditing = computed(() => {
    return !isAdministrationType.value;
});

// --- Geofencing Logic ---
const canUseGeofencing = computed(() => {
    return isTrackingType.value &&
           selectedItem.value &&
           selectedItem.value.type === 'movingItem' &&
           editorState.value.isEditing;
});

const hasExistingGeofencing = computed(() => {
    if (!selectedItem.value?.geoFancing) return false;

    let geoFancingData;
    if (typeof selectedItem.value.geoFancing === 'string') {
        try {
            geoFancingData = JSON.parse(selectedItem.value.geoFancing);
        } catch (e) {
            return false;
        }
    } else {
        geoFancingData = selectedItem.value.geoFancing;
    }

    return geoFancingData &&
           (geoFancingData.apiKey?.isActive === 'yes' ||
            geoFancingData.webHook?.isActive === 'yes' ||
            (geoFancingData.geoFancing && geoFancingData.geoFancing.length > 0));
});

const getApiKey = computed(() => {
    if (!selectedItem.value?.geoFancing) return '';

    let geoFancingData;
    if (typeof selectedItem.value.geoFancing === 'string') {
        try {
            geoFancingData = JSON.parse(selectedItem.value.geoFancing);
        } catch (e) {
            return '';
        }
    } else {
        geoFancingData = selectedItem.value.geoFancing;
    }

    return geoFancingData?.apiKey?.key || '';
});

// --- Functions ---
const showAlert = (type, message, duration = 4000) => {
    alert.value = { show: true, type, message };
    setTimeout(() => (alert.value.show = false), duration);
};

const fetchPlaceMapDetails = async () => {
    loading.value.details = true;
    try {
        const response = await axios.get(route("placeMap.getById", { placeMapId: props.placeMapId }));
        placeMap.value = response.data;
        // Initialize map view after getting place map details
        if (placeMap.value) {
            showMapView.value = true;
            nextTick(() => initMap());
        }
    } catch (error) {
        showAlert("error", "Error fetching map details.");
    } finally {
        loading.value.details = false;
    }
};

const fetchPlaceMapItems = async (page = 1, perPage = itemsPerPage.value) => {
    loading.value.items = true;
    try {
        const response = await axios.get(route("placeMap.getPlaceMapItem", {
            placeMapId: props.placeMapId,
            page,
            perPage,
            searchQuery: searchQuery.value
        }));
        placeMapItems.value = response.data.data;
        itemsPagination.value = response.data;
        // Update map markers when items are loaded
        updateMapMarkers();
    } catch (error) {
        showAlert("error", "Error fetching map items.");
    } finally {
        loading.value.items = false;
    }
};

const openEditorForCreate = () => {
    locationSearchQuery.value = "";
    locationSearchResults.value = null;

    // Set initial type based on map type
    let initialType = "place";
    if (isTrackingType.value) {
        initialType = "movingItem";
    } else if (isAdministrationType.value) {
        initialType = "village"; // Default to village for administration
    }

    editorState.value = {
        isOpen: true,
        isEditing: false,
        itemId: null,
        form: {
            ...getInitialItemForm(),
            type: initialType,
            dataItems: customFields.value.map(f => ({ name: f.name, value: "" })),
        },
    };
};

const openEditorForEdit = (item) => {
    selectedItem.value = item;
    locationSearchQuery.value = "";
    locationSearchResults.value = null;
    const existingDataItems = item.dataItems ? JSON.parse(item.dataItems) : [];
    const existingDataMap = existingDataItems.reduce((acc, curr) => ({ ...acc, [curr.name]: curr.value }), {});

    editorState.value = {
        isOpen: true,
        isEditing: true,
        itemId: item.id,
        form: {
            name: item.name || "",
            description: item.description || "",
            address: item.address || "",
            latitude: item.latitude || "",
            longitude: item.longitude || "",
            image: item.image || "pin",
            type: item.type || "place",
            locationID: item.location_id || null,
            visibility: item.visibility || "private",
            status: item.status || "active",
            dataItems: customFields.value.map(f => ({ name: f.name, value: existingDataMap[f.name] || "" })),
            processing: false,
            errors: {},
        },
    };

    // Focus on the item in the map
    if (item.latitude && item.longitude && map.value) {
        map.value.flyTo({
            center: [parseFloat(item.longitude), parseFloat(item.latitude)],
            zoom: 15,
            duration: 1000
        });
        highlightMarker(item.id);
    }
};

const closeEditor = () => {
    editorState.value.isOpen = false;
    selectedItem.value = null;
    clearHighlightedMarker();
};

const submitPlaceMapItem = async () => {
    const form = editorState.value.form;
    form.processing = true;
    form.errors = {};

    const url = editorState.value.isEditing
        ? route("placeMap.updatePlaceItem", { placeMapId: props.placeMapId, placeMapItemId: editorState.value.itemId })
        : route("placeMap.createPlaceItem", { placeMapId: props.placeMapId });

    try {
        const response = await axios.post(url, form);
        showAlert("success", response.data.message);
        await fetchPlaceMapItems(itemsPagination.value?.currentPage || 1, itemsPerPage.value);
        if (!editorState.value.isEditing) {
            closeEditor();
        }
    } catch (error) {
        const errorMessage = error.response?.data?.message || "An unexpected error occurred.";
        showAlert("error", errorMessage);
        if (error.response?.status === 422) {
            form.errors = error.response.data.errors;
        }
    } finally {
        form.processing = false;
    }
};

// --- Map Functions ---
const initMap = () => {
    if (map.value || !mapContainer.value) return;

    const initialCenter = [29.8739, -1.9403];
    const initialZoom = 8;

    map.value = new maplibregl.Map({
        container: mapContainer.value,
        style: {
            version: 8,
            sources: {
                osm: {
                    type: 'raster',
                    tiles: ['https://a.tile.openstreetmap.org/{z}/{x}/{y}.png'],
                    tileSize: 256,
                    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                }
            },
            layers: [{ id: 'osm-tiles', type: 'raster', source: 'osm', paint: {} }]
        },
        center: initialCenter,
        zoom: initialZoom
    });

    map.value.on('load', () => {
        updateMapMarkers();
    });

    map.value.on('click', (e) => {
        if (editorState.value.isOpen && allowCoordinateEditing.value) {
            const { lng, lat } = e.lngLat;
            editorState.value.form.latitude = lat.toFixed(6);
            editorState.value.form.longitude = lng.toFixed(6);
            // Auto-fill address
            reverseGeocode(lat, lng);
        } else {
            // Click on map to select item
            const features = map.value.queryRenderedFeatures(e.point);
            if (features.length > 0) {
                const feature = features[0];
                if (feature.properties && feature.properties.itemId) {
                    const item = placeMapItems.value.find(i => i.id == feature.properties.itemId);
                    if (item) {
                        openEditorForEdit(item);
                    }
                }
            }
        }
    });
};

const updateMapMarkers = () => {
    if (!map.value) return;

    // Clear existing markers
    markers.value.forEach(marker => marker.remove());
    markers.value = [];

    // Add markers for all items
    placeMapItems.value.forEach(item => {
        if (item.latitude && item.longitude) {
            const marker = createMarker(item);
            markers.value.push(marker);
        }
    });
};

const createMarker = (item) => {
    const el = document.createElement('div');
    el.className = 'custom-marker';
    el.style.cssText = `
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: ${item.visibility === 'public' ? '#22c55e' : '#6b7280'};
        border: 3px solid white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        font-weight: bold;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        transition: all 0.2s ease;
    `;

    // Add icon based on item.image
    const iconMap = {
        pin: '📍', star: '⭐', heart: '❤️', flag: '🚩',
        home: '🏠', work: '💼', cafe: '☕', park: '🌳',
        restaurant: '🍽️', shopping: '🛍️', hospital: '🏥', school: '🏫'
    };
    el.textContent = iconMap[item.image] || '📍';

    el.addEventListener('mouseenter', () => {
        el.style.transform = 'scale(1.2)';
        el.style.zIndex = '1000';
    });

    el.addEventListener('mouseleave', () => {
        el.style.transform = 'scale(1)';
        el.style.zIndex = '1';
    });

    const marker = new maplibregl.Marker(el)
        .setLngLat([parseFloat(item.longitude), parseFloat(item.latitude)])
        .addTo(map.value);

    marker.itemId = item.id;
    return marker;
};

const highlightMarker = (itemId) => {
    markers.value.forEach(marker => {
        const el = marker.getElement();
        if (marker.itemId == itemId) {
            el.style.border = '3px solid #3b82f6';
            el.style.transform = 'scale(1.3)';
            el.style.zIndex = '1001';
        } else {
            el.style.border = '3px solid white';
            el.style.transform = 'scale(1)';
            el.style.zIndex = '1';
        }
    });
};

const clearHighlightedMarker = () => {
    markers.value.forEach(marker => {
        const el = marker.getElement();
        el.style.border = '3px solid white';
        el.style.transform = 'scale(1)';
        el.style.zIndex = '1';
    });
};

const reverseGeocode = async (lat, lng) => {
    try {
        const response = await axios.post('/map/search-latitude-langitude-json', {
            latitude: lat,
            longitude: lng,
            lang: 'en'
        });

        if (response.data && response.data.length > 0) {
            // Use the first result for address
            const location = response.data[0];
            editorState.value.form.address = location.address || `${location.name_en || location.name}, Rwanda`;
        } else {
            // Fallback to coordinates if no results
            editorState.value.form.address = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
        }
    } catch (error) {
        console.error('Reverse geocoding failed:', error);
        // Fallback to coordinates
        editorState.value.form.address = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    }
};

const destroyMap = () => {
    if (map.value) {
        markers.value.forEach(marker => marker.remove());
        markers.value = [];
        map.value.remove();
        map.value = null;
    }
};



const performLocationSearch = debounce(async () => {
    if (locationSearchQuery.value.length < 3) {
        locationSearchResults.value = null;
        return;
    }
    isSearching.value = true;
    try {
        const response = await axios.get("/api/search", {
            params: {
                filterData: searchFilters.value.type === 'all' ? editorState.value.form.type : searchFilters.value.type,
                searchQuery: locationSearchQuery.value,
                lang: searchFilters.value.language,
            },
        });
        locationSearchResults.value = response.data;
    } catch (error) {
        showAlert("error", "Failed to search for locations.");
        locationSearchResults.value = null;
    } finally {
        isSearching.value = false;
    }
}, 300);

const toggleFilters = () => {
    showFilters.value = !showFilters.value;
};

const resetFilters = () => {
    searchFilters.value.type = 'all';
    searchFilters.value.language = 'en';
    if (locationSearchQuery.value.length >= 3) {
        performLocationSearch();
    }
};

// --- Geofencing Functions ---
const initializeGeofencingForm = () => {
    if (!selectedItem.value) return;

    let geoFancingData = null;

    // Handle both string and object formats
    if (selectedItem.value.geoFancing) {
        if (typeof selectedItem.value.geoFancing === 'string') {
            try {
                geoFancingData = JSON.parse(selectedItem.value.geoFancing);
            } catch (e) {
                console.error('Failed to parse geoFancing data:', e);
                geoFancingData = null;
            }
        } else {
            geoFancingData = selectedItem.value.geoFancing;
        }
    }

    if (geoFancingData) {
        // Parse existing geofencing data
        geofencingForm.value = {
            apiKey: geoFancingData.apiKey?.isActive || 'no',
            webHookStatus: geoFancingData.webHook?.isActive || 'no',
            webHookUrl: geoFancingData.webHook?.url || '',
            geoFancing: (geoFancingData.geoFancing && geoFancingData.geoFancing.length > 0) ? 'yes' : 'no',
            geoFancingData: geoFancingData.geoFancing || props.geoFancingfields?.map(field => ({
                name: field.name,
                value: ''
            })) || [],
            processing: false,
            errors: {}
        };
    } else {
        // Initialize with default values from config (when geoFancing is null)
        geofencingForm.value = {
            apiKey: 'no',
            webHookStatus: 'no',
            webHookUrl: '',
            geoFancing: 'no',
            geoFancingData: props.geoFancingfields?.map(field => ({
                name: field.name,
                value: ''
            })) || [],
            processing: false,
            errors: {}
        };
    }
};

const openGeofencing = () => {
    if (!canUseGeofencing.value) return;
    initializeGeofencingForm();
    showGeofencing.value = true;
};

const closeGeofencing = () => {
    showGeofencing.value = false;
    geofencingForm.value.errors = {};
};

const submitGeofencing = async () => {
    if (geofencingForm.value.processing) return;

    geofencingForm.value.processing = true;
    geofencingForm.value.errors = {};

    try {
        await axios.post('/geo-fancing', {
            placeMapId: props.placeMapId,
            placeMapItemId: selectedItem.value.id,
            apiKey: geofencingForm.value.apiKey,
            webHookStatus: geofencingForm.value.webHookStatus,
            webHookUrl: geofencingForm.value.webHookUrl,
            geoFancing: geofencingForm.value.geoFancing,
            geoFancingData: geofencingForm.value.geoFancingData
        });

        showAlert("success", "Geofencing settings updated successfully!");
        closeGeofencing();
        fetchPlaceMapItems(); // Refresh to get updated data
    } catch (error) {
        if (error.response?.status === 422) {
            geofencingForm.value.errors = error.response.data.errors || {};
        } else {
            showAlert("error", "Failed to update geofencing settings.");
        }
    } finally {
        geofencingForm.value.processing = false;
    }
};

const getFieldType = (fieldName) => {
    const fieldConfig = props.geoFancingfields?.find(f => f.name === fieldName);
    if (fieldConfig?.type === 'number' || fieldName === 'Radius') {
        return 'number';
    }
    return 'text';
};

const getFieldPlaceholder = (fieldName) => {
    const placeholders = {
        'ID': 'Auto-generated ID',
        'ID-Type': 'e.g., cell, sector, village',
        'Action-Trigger': 'e.g., send email, send SMS',
        'Type': 'e.g., when tracking is away, when item is close',
        'Radius': 'e.g., 10 (in kilometers)'
    };
    return placeholders[fieldName] || `Enter ${fieldName.toLowerCase()}`;
};

const selectLocation = (location) => {
    const form = editorState.value.form;

    // Fill basic information
    form.name = location.name_en || location.name;
    form.locationID = location.id;

    // Fill coordinates if available
    if (location.latitude && location.longitude) {
        form.latitude = parseFloat(location.latitude);
        form.longitude = parseFloat(location.longitude);
    }

    // Fill address - try multiple sources
    if (location.address) {
        form.address = location.address;
    } else {
        // Build address from available information
        const addressParts = [];
        if (location.name_en || location.name) {
            addressParts.push(location.name_en || location.name);
        }
        if (location.district_name) {
            addressParts.push(location.district_name);
        }
        if (location.province_name) {
            addressParts.push(location.province_name);
        }
        addressParts.push('Rwanda');
        form.address = addressParts.join(', ');
    }

    // Set type based on location type for administration maps
    if (isAdministrationType.value && location.type) {
        const typeMapping = {
            'Province': 'province',
            'District': 'district',
            'Sector': 'sector',
            'Cell': 'cell',
            'Village': 'village',
            'Health Facility': 'healthFac'
        };
        form.type = typeMapping[location.type] || location.type.toLowerCase();
    }

    locationSearchQuery.value = "";
    locationSearchResults.value = null;

    // If coordinates are available, update map view
    if (form.latitude && form.longitude && map.value) {
        map.value.flyTo({
            center: [parseFloat(form.longitude), parseFloat(form.latitude)],
            zoom: 15,
            duration: 1000
        });
    }
};

const clearSelectedLocation = () => {
    const form = editorState.value.form;
    form.locationID = null;
    form.name = "";
    form.address = "";

    // Only clear coordinates for administration types if they were auto-filled
    if (isAdministrationType.value) {
        form.latitude = "";
        form.longitude = "";
    }

    locationSearchQuery.value = "";
    locationSearchResults.value = null;
};

onMounted(async () => {
    await fetchPlaceMapDetails();
    await fetchPlaceMapItems();
});

watch(searchQuery, debounce(() => {
    fetchPlaceMapItems(1, itemsPerPage.value);
}, 300));

watch(itemsPerPage, () => {
    fetchPlaceMapItems(1, itemsPerPage.value);
});

watch(locationSearchQuery, () => {
    if (shouldShowLocationSearch.value && !editorState.value.form.locationID) {
        performLocationSearch();
    }
});

watch([() => searchFilters.value.type, () => searchFilters.value.language], () => {
    if (shouldShowLocationSearch.value && locationSearchQuery.value.length >= 3) {
        performLocationSearch();
    }
});

watch(() => editorState.value.form.type, () => {
    if (!editorState.value.isEditing) {
        const form = editorState.value.form;
        form.latitude = "";
        form.longitude = "";
        form.locationID = null;
        form.address = "";
        locationSearchQuery.value = "";
        locationSearchResults.value = null;
    }
});

watch(
    () => [editorState.value.form.latitude, editorState.value.form.longitude],
    debounce(([newLat, newLng]) => {
        if (map.value && newLat && newLng && editorState.value.isOpen) {
            const lat = parseFloat(newLat);
            const lng = parseFloat(newLng);
            if (!isNaN(lat) && !isNaN(lng)) {
                map.value.flyTo({ center: [lng, lat], zoom: Math.max(map.value.getZoom(), 12) });
                // Auto-fill address if empty
                if (!editorState.value.form.address) {
                    reverseGeocode(lat, lng);
                }
            }
        }
    }, 500)
);

</script>

<template>
    <AppLayout :title="placeMap ? `Editing ${placeMap.name}` : 'Map Items'">
        <Head title="Map Items" />

        <!-- Alert -->
        <div v-if="alert.show" :class="`fixed top-4 right-4 z-50 p-3 rounded-lg border ${alert.type === 'success' ? 'bg-white border-gray-300 text-gray-900' : 'bg-white border-gray-300 text-gray-900'}`">
            {{ alert.message }}
        </div>

        <div class="min-h-screen bg-gray-50">
            <!-- Dashboard Header -->
            <div class="bg-white border-b border-gray-200">
                <div class="max-w-7xl mx-auto px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <Link :href="route('myMap.index')" class="text-sm font-medium text-gray-600 hover:text-black transition-colors">
                                ← Back to My Maps
                            </Link>
                            <div class="h-4 w-px bg-gray-300"></div>
                            <div v-if="placeMap">
                                <h1 class="text-xl font-semibold text-black">{{ placeMap.name }}</h1>
                                <p class="text-sm text-gray-600">{{ placeMap.type }} Map Management</p>
                            </div>
                            <div v-else class="space-y-1">
                                <div class="h-5 bg-gray-200 rounded w-32 animate-pulse"></div>
                                <div class="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <Link :href="route('myMapData.index', placeMapId)" class="px-3 py-2 text-sm font-medium text-gray-700 hover:text-black border border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
                                Map Data
                            </Link>
                            <button @click="openEditorForCreate" class="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors font-medium">
                                <MapIcon name="plus" size="16" class="mr-2" />
                                Add New Place
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Dashboard Content -->
            <div class="max-w-7xl mx-auto px-6 py-6">
                <div class="grid grid-cols-12 gap-6">
                    <!-- Left Panel: Items List -->
                    <div class="col-span-4">
                        <div class="bg-white rounded-lg border border-gray-200">
                            <!-- Search and Controls -->
                            <div class="p-4 border-b border-gray-200">
                                <div class="space-y-3">
                                    <div class="relative">
                                        <input v-model="searchQuery" type="text" placeholder="Search places..." class="w-full px-3 py-2 pl-10 rounded-lg border border-gray-300 focus:border-gray-500 focus:outline-none transition-colors" />
                                        <MapIcon name="search" size="16" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                                    </div>
                                </div>
                            </div>

                            <!-- Items List -->
                            <div class="max-h-96 overflow-y-auto">
                                <div v-if="loading.items" class="p-4 space-y-3">
                                    <div v-for="i in 6" :key="i" class="h-16 bg-gray-100 rounded-lg animate-pulse"></div>
                                </div>
                                <div v-else-if="placeMapItems.length > 0" class="divide-y divide-gray-100">
                                    <div v-for="item in placeMapItems" :key="item.id"
                                         class="p-4 hover:bg-gray-50 transition-all cursor-pointer"
                                         :class="{'bg-gray-50 border-l-2 border-black': editorState.isOpen && editorState.itemId === item.id}"
                                         @click="openEditorForEdit(item)">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 rounded-lg bg-black flex items-center justify-center text-white">
                                                <MapIcon :name="item.image || 'pin'" size="16" />
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-center space-x-2 mb-1">
                                                    <h3 class="font-medium text-black truncate">{{ item.name }}</h3>
                                                    <MapIcon :name="item.visibility === 'public' ? 'unlock' : 'lock'" size="12" :class="item.visibility === 'public' ? 'text-gray-600' : 'text-gray-400'" />
                                                </div>
                                                <p class="text-xs text-gray-600 truncate">{{ item.address || 'No address' }}</p>
                                                <div class="flex items-center space-x-2 mt-1">
                                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                                                        {{ item.type }}
                                                    </span>
                                                    <span :class="`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${item.status === 'active' ? 'bg-gray-100 text-gray-900' : 'bg-gray-100 text-gray-600'}`">
                                                        {{ item.status }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-else class="text-center p-8 text-gray-500">
                                    <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                        <MapIcon name="pin" size="24" class="text-gray-400" />
                                    </div>
                                    <p class="font-medium">No places found</p>
                                    <p v-if="searchQuery" class="text-sm mt-1">Try a different search term</p>
                                    <p v-else class="text-sm mt-1">Get started by adding your first place</p>
                                </div>
                            </div>

                            <!-- Pagination Controls -->
                            <div v-if="itemsPagination && itemsPagination.total > 0" class="p-4 border-t border-gray-200">
                                <div class="flex items-center justify-between text-sm">
                                    <select v-model="itemsPerPage" class="px-2 py-1 rounded border border-gray-300 focus:border-gray-500 focus:outline-none text-xs">
                                        <option v-for="option in itemsPerPageOptions" :key="option" :value="option">{{ option }} / page</option>
                                    </select>
                                    <div class="text-gray-600 font-medium">
                                        {{ ((itemsPagination.currentPage - 1) * itemsPagination.itemsPerPage) + 1 }}-{{ Math.min(itemsPagination.currentPage * itemsPagination.itemsPerPage, itemsPagination.total) }} of {{ itemsPagination.total }}
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <button @click="fetchPlaceMapItems(itemsPagination.currentPage - 1)"
                                                :disabled="itemsPagination.currentPage <= 1"
                                                class="px-3 py-1 rounded border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-xs">
                                            Prev
                                        </button>
                                        <span class="px-3 py-1 bg-black text-white rounded text-xs font-medium">
                                            {{ itemsPagination.currentPage }} / {{ itemsPagination.lastPage }}
                                        </span>
                                        <button @click="fetchPlaceMapItems(itemsPagination.currentPage + 1)"
                                                :disabled="itemsPagination.currentPage >= itemsPagination.lastPage"
                                                class="px-3 py-1 rounded border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-xs">
                                            Next
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Panel: Map and Editor -->
                    <div class="col-span-8 space-y-6">
                        <!-- Map Card -->
                        <div class="bg-white rounded-lg border border-gray-200">
                            <div class="p-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-black">Map View</h3>
                                <p class="text-sm text-gray-600 mt-1">Click on markers to edit items</p>
                            </div>
                            <div class="p-4">
                                <div class="h-80 rounded-lg border border-gray-300 overflow-hidden">
                                    <div v-if="showMapView" ref="mapContainer" class="w-full h-full"></div>
                                    <div v-else class="w-full h-full flex items-center justify-center bg-gray-50">
                                        <div class="text-center">
                                            <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                                <MapIcon name="map" size="24" class="text-gray-400" />
                                            </div>
                                            <p class="text-gray-600 font-medium">Map will load after place details are fetched</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Editor Card -->
                        <div v-if="editorState.isOpen" class="bg-white rounded-lg border border-gray-200">
                            <div class="p-4 border-b border-gray-200">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <h3 class="text-lg font-semibold text-black">{{ editorState.isEditing ? 'Edit Place' : 'New Place' }}</h3>
                                        <p class="text-sm text-gray-600 mt-1">{{ editorState.isEditing ? 'Update place information' : 'Add a new place to your map' }}</p>
                                    </div>
                                    <button @click="closeEditor" class="p-2 rounded hover:bg-gray-100 transition-colors">
                                        <MapIcon name="close" size="16" class="text-gray-600"/>
                                    </button>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="space-y-6">

                                <!-- Editor Form -->
                                <div class="max-h-96 overflow-y-auto">
                                    <div class="p-4 space-y-4">
                                <!-- Main Details Card -->
                                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                    <h3 class="text-base font-semibold text-black mb-3">Basic Information</h3>
                                    <div class="space-y-3">
                                        <!-- Type Selection (conditional based on map type) -->
                                        <div v-if="!isAdministrationType || editorState.isEditing">
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                                            <select v-model="editorState.form.type"
                                                    :disabled="isAdministrationType && editorState.isEditing"
                                                    class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-gray-500 focus:outline-none transition-colors disabled:bg-gray-100">
                                                <option v-if="isGeneralOrItineraryType" value="place">Place</option>
                                                <option v-if="isTrackingType" value="movingItem">Moving Item</option>
                                                <option v-if="isAdministrationType" value="province">Province</option>
                                                <option v-if="isAdministrationType" value="district">District</option>
                                                <option v-if="isAdministrationType" value="sector">Sector</option>
                                                <option v-if="isAdministrationType" value="cell">Cell</option>
                                                <option v-if="isAdministrationType" value="village">Village</option>
                                                <option v-if="isAdministrationType" value="healthFac">Health Facility</option>
                                            </select>
                                        </div>

                                        <!-- Location Search (for administration type) -->
                                        <div v-if="shouldShowLocationSearch">
                                            <div class="flex items-center justify-between mb-1">
                                                <label class="block text-sm font-medium text-gray-700">Search Location</label>
                                                <button @click="toggleFilters"
                                                        class="p-1 rounded hover:bg-gray-100 transition-colors"
                                                        :class="{ 'bg-gray-100 text-black': showFilters }">
                                                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2zM3 16a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" />
                                                    </svg>
                                                </button>
                                            </div>

                                            <!-- Filters Panel -->
                                            <div v-if="showFilters" class="mb-3 p-3 bg-white border border-gray-300 rounded-lg space-y-3">
                                                <div class="flex justify-between items-center">
                                                    <h4 class="font-medium text-black">Search Filters</h4>
                                                    <button @click="resetFilters" class="text-sm text-gray-600 hover:text-black font-medium">
                                                        Reset
                                                    </button>
                                                </div>

                                                <!-- Type Filter -->
                                                <div>
                                                    <label class="block text-xs font-medium text-gray-600 mb-2 uppercase tracking-wide">Location Type</label>
                                                    <div class="grid grid-cols-2 gap-1">
                                                        <button v-for="filterType in FILTER_TYPES" :key="filterType.code"
                                                                @click="searchFilters.type = filterType.code"
                                                                :class="`p-2 rounded border transition-all text-left ${searchFilters.type === filterType.code ? 'border-black bg-gray-100 text-black' : 'border-gray-300 hover:border-gray-400 text-gray-700'}`">
                                                            <div class="flex items-center space-x-2">
                                                                <svg class="w-3 h-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="filterType.icon" />
                                                                </svg>
                                                                <span class="text-xs font-medium">{{ filterType.name }}</span>
                                                            </div>
                                                        </button>
                                                    </div>
                                                </div>

                                                <!-- Language Filter -->
                                                <div>
                                                    <label class="block text-xs font-medium text-gray-600 mb-2 uppercase tracking-wide">Language</label>
                                                    <div class="flex space-x-1">
                                                        <button v-for="lang in FILTER_LANGUAGES" :key="lang.code"
                                                                @click="searchFilters.language = lang.code"
                                                                :class="`px-3 py-1 rounded border transition-all ${searchFilters.language === lang.code ? 'border-black bg-gray-100 text-black' : 'border-gray-300 hover:border-gray-400 text-gray-700'}`">
                                                            <span class="text-xs font-medium">{{ lang.name }}</span>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <div v-if="editorState.form.locationID" class="p-3 bg-gray-100 border border-gray-300 rounded-lg">
                                                <div class="flex justify-between items-center">
                                                    <div>
                                                        <div class="font-medium text-black">{{ editorState.form.name }}</div>
                                                        <div class="text-sm text-gray-600">{{ editorState.form.address }}</div>
                                                    </div>
                                                    <button @click="clearSelectedLocation" class="text-gray-600 hover:text-black p-1 rounded hover:bg-gray-200 transition-colors">
                                                        <MapIcon name="close" size="14" />
                                                    </button>
                                                </div>
                                            </div>
                                            <div v-else class="relative">
                                                <input v-model="locationSearchQuery"
                                                       type="text"
                                                       placeholder="Search for location..."
                                                       class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-gray-500 focus:outline-none transition-colors" />
                                                <div class="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
                                                    <div v-if="isSearching" class="animate-spin h-4 w-4 border-2 border-gray-600 border-t-transparent rounded-full"></div>
                                                    <MapIcon v-else name="search" size="16" class="text-gray-400" />
                                                </div>

                                                <!-- Search Results -->
                                                <div v-if="locationSearchResults && formattedSearchResults.length > 0"
                                                     class="absolute z-10 w-full mt-1 max-h-48 overflow-y-auto bg-white border border-gray-300 rounded-lg shadow-lg">
                                                    <div v-for="result in formattedSearchResults" :key="`${result.type}-${result.id}`"
                                                         @click="selectLocation(result)"
                                                         class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-200 last:border-b-0 transition-colors">
                                                        <div class="font-medium text-black">{{ result.name_en || result.name }}</div>
                                                        <div class="text-sm text-gray-600">{{ result.type }}</div>
                                                    </div>
                                                </div>

                                                <!-- No Results -->
                                                <div v-if="!isSearching && locationSearchQuery.length >= 3 && formattedSearchResults.length === 0"
                                                     class="absolute z-10 w-full mt-1 p-3 bg-white border border-gray-300 rounded-lg text-center text-gray-500">
                                                    No results found.
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Name -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Name *</label>
                                            <input v-model="editorState.form.name"
                                                   type="text"
                                                   placeholder="Enter place name"
                                                   class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-gray-500 focus:outline-none transition-colors" />
                                            <div v-if="editorState.form.errors.name" class="text-red-600 text-sm mt-1 font-medium">{{ editorState.form.errors.name[0] }}</div>
                                        </div>

                                        <!-- Description -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                            <textarea v-model="editorState.form.description"
                                                      rows="2"
                                                      placeholder="Enter description"
                                                      class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-gray-500 focus:outline-none transition-colors resize-none"></textarea>
                                        </div>

                                        <!-- Address (auto-filled) -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Address *</label>
                                            <input v-model="editorState.form.address"
                                                   type="text"
                                                   placeholder="Address will be auto-filled"
                                                   class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-gray-500 focus:outline-none transition-colors" />
                                            <div v-if="editorState.form.errors.address" class="text-red-600 text-sm mt-1 font-medium">{{ editorState.form.errors.address[0] }}</div>
                                        </div>

                                        <!-- Coordinates (conditional based on map type or if filled from location) -->
                                        <div v-if="shouldShowCoordinateInputs || (isAdministrationType && (editorState.form.latitude || editorState.form.longitude))" class="grid grid-cols-2 gap-3">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                                    Latitude
                                                    <span v-if="!isAdministrationType">*</span>
                                                    <span v-if="isAdministrationType && editorState.form.latitude" class="text-gray-500 text-xs">(Auto-filled)</span>
                                                </label>
                                                <input v-model="editorState.form.latitude"
                                                       type="number"
                                                       step="any"
                                                       placeholder="e.g. -1.9403"
                                                       :disabled="!allowCoordinateEditing"
                                                       class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-gray-500 focus:outline-none transition-colors disabled:bg-gray-100" />
                                                <div v-if="editorState.form.errors.latitude" class="text-red-600 text-sm mt-1 font-medium">{{ editorState.form.errors.latitude[0] }}</div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                                    Longitude
                                                    <span v-if="!isAdministrationType">*</span>
                                                    <span v-if="isAdministrationType && editorState.form.longitude" class="text-gray-500 text-xs">(Auto-filled)</span>
                                                </label>
                                                <input v-model="editorState.form.longitude"
                                                       type="number"
                                                       step="any"
                                                       placeholder="e.g. 29.8739"
                                                       :disabled="!allowCoordinateEditing"
                                                       class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-gray-500 focus:outline-none transition-colors disabled:bg-gray-100" />
                                                <div v-if="editorState.form.errors.longitude" class="text-red-600 text-sm mt-1 font-medium">{{ editorState.form.errors.longitude[0] }}</div>
                                            </div>
                                        </div>

                                        <!-- Visibility and Status -->
                                        <div class="grid grid-cols-2 gap-3">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">Visibility</label>
                                                <select v-model="editorState.form.visibility"
                                                        class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-gray-500 focus:outline-none transition-colors">
                                                    <option value="private">Private</option>
                                                    <option value="public">Public</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                                <select v-model="editorState.form.status"
                                                        class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-gray-500 focus:outline-none transition-colors">
                                                    <option value="active">Active</option>
                                                    <option value="inactive">Inactive</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Geofencing Section (for moving items on tracking maps) -->
                                        <div v-if="isTrackingType && editorState.form.type === 'movingItem' && editorState.isEditing" class="bg-gray-100 p-4 rounded-lg border border-gray-300">
                                            <div class="flex items-center justify-between mb-3">
                                                <div>
                                                    <h3 class="text-base font-semibold text-black">Geofencing</h3>
                                                    <p class="text-sm text-gray-600">Configure location-based triggers and notifications</p>
                                                </div>
                                                <svg class="w-6 h-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                            </div>

                                            <!-- API Key Status -->
                                            <div class="mb-3 p-3 bg-white border border-gray-300 rounded-lg">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center space-x-2">
                                                        <svg class="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2h-6m6 0v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2h6z" />
                                                        </svg>
                                                        <span class="text-sm font-medium text-gray-700">API Key:</span>
                                                    </div>
                                                    <code class="text-xs font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded">{{ getApiKey || 'Not generated' }}</code>
                                                </div>
                                            </div>

                                            <!-- Geofencing Status -->
                                            <div v-if="hasExistingGeofencing" class="mb-3 p-3 bg-white border border-gray-300 rounded-lg">
                                                <div class="flex items-center space-x-2">
                                                    <svg class="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                    </svg>
                                                    <span class="text-sm font-medium text-gray-700">Geofencing is configured</span>
                                                </div>
                                            </div>

                                            <button @click="openGeofencing" type="button" class="w-full inline-flex items-center justify-center px-4 py-2 bg-black text-white rounded-lg font-medium hover:bg-gray-800 transition-colors">
                                                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                                {{ hasExistingGeofencing ? 'Manage Geofencing Settings' : 'Setup Geofencing' }}
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Icon Selection Card -->
                                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                    <h3 class="text-base font-semibold text-black mb-3">Icon</h3>
                                    <div class="grid grid-cols-6 gap-2">
                                        <button v-for="icon in mapIcons" :key="icon.name"
                                                @click="editorState.form.image = icon.name"
                                                :class="`p-2 rounded border transition-all ${editorState.form.image === icon.name ? 'border-black bg-gray-100' : 'border-gray-300 hover:border-gray-400'}`">
                                            <MapIcon :name="icon.name" size="16" class="mx-auto" />
                                        </button>
                                    </div>
                                </div>

                                <!-- Custom Fields Card -->
                                <div v-if="editorState.form.dataItems.length > 0" class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                    <h3 class="text-base font-semibold text-black mb-3">Custom Data</h3>
                                    <div class="space-y-3">
                                        <div v-for="(item, index) in editorState.form.dataItems" :key="index">
                                            <label class="block text-sm font-medium text-gray-700 mb-1">{{ item.name }}</label>
                                            <input v-model="item.value"
                                                   type="text"
                                                   class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-gray-500 focus:outline-none transition-colors" />
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="mt-6 pt-4 border-t border-gray-200">
                                    <div class="flex justify-end">
                                        <button @click="submitPlaceMapItem" :disabled="editorState.form.processing"
                                                class="inline-flex items-center px-6 py-2 bg-black text-white rounded-lg font-medium hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                                            <span v-if="editorState.form.processing" class="mr-2">
                                                <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                            </span>
                                            {{ editorState.form.processing ? 'Saving...' : (editorState.isEditing ? 'Update Place' : 'Create Place') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Empty State -->
                        <div v-if="!editorState.isOpen" class="bg-white rounded-lg border border-gray-200 p-12 text-center">
                            <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <MapIcon name="edit" size="24" class="text-gray-400"/>
                            </div>
                            <h3 class="text-lg font-semibold text-black mb-2">Select a place to edit</h3>
                            <p class="text-gray-600">Choose a place from the list to edit its details, or create a new one to get started.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
            </div>
        </div>
        </div>

        <!-- Geofencing Modal -->
        <div v-if="showGeofencing" class="fixed inset-0 z-50 overflow-y-auto">
            <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20">
                <!-- Background overlay -->
                <div class="fixed inset-0 transition-opacity bg-black bg-opacity-50" @click="closeGeofencing"></div>

                <!-- Modal panel -->
                <div class="relative w-full max-w-lg p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white rounded-lg shadow-xl">
                    <!-- Header -->
                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <h3 class="text-lg font-semibold text-black">Geofencing Settings</h3>
                            <p class="text-sm text-gray-600 mt-1">Configure geofencing for {{ selectedItem?.name }}</p>
                        </div>
                        <button @click="closeGeofencing" class="p-2 rounded hover:bg-gray-100 transition-colors">
                            <MapIcon name="close" size="20" class="text-gray-600" />
                        </button>
                    </div>

                    <!-- Form -->
                    <form @submit.prevent="submitGeofencing" class="space-y-4">
                        <!-- API Key Section -->
                        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <h4 class="text-base font-semibold text-black mb-3">API Configuration</h4>
                            <div class="space-y-3">
                                <!-- Always show API Key -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                                    <div class="p-3 bg-white border border-gray-300 rounded-lg">
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2h-6m6 0v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2h6z" />
                                            </svg>
                                            <code class="text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded">{{ getApiKey || 'No API key generated yet' }}</code>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Enable API Key</label>
                                    <div class="flex space-x-4">
                                        <label class="flex items-center">
                                            <input v-model="geofencingForm.apiKey" type="radio" value="yes" class="mr-2" />
                                            <span class="text-sm font-medium">Yes</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input v-model="geofencingForm.apiKey" type="radio" value="no" class="mr-2" />
                                            <span class="text-sm font-medium">No</span>
                                        </label>
                                    </div>
                                    <div v-if="geofencingForm.errors.apiKey" class="text-red-600 text-sm mt-1 font-medium">{{ geofencingForm.errors.apiKey[0] }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Webhook Section -->
                        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <h4 class="text-base font-semibold text-black mb-3">Webhook Configuration</h4>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Enable Webhook</label>
                                    <div class="flex space-x-4">
                                        <label class="flex items-center">
                                            <input v-model="geofencingForm.webHookStatus" type="radio" value="yes" class="mr-2" />
                                            <span class="text-sm font-medium">Yes</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input v-model="geofencingForm.webHookStatus" type="radio" value="no" class="mr-2" />
                                            <span class="text-sm font-medium">No</span>
                                        </label>
                                    </div>
                                    <div v-if="geofencingForm.errors.webHookStatus" class="text-red-600 text-sm mt-1 font-medium">{{ geofencingForm.errors.webHookStatus[0] }}</div>
                                </div>

                                <div v-if="geofencingForm.webHookStatus === 'yes'">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Webhook URL *</label>
                                    <input v-model="geofencingForm.webHookUrl"
                                           type="url"
                                           placeholder="https://example.com/webhook"
                                           class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-gray-500 focus:outline-none transition-colors" />
                                    <div v-if="geofencingForm.errors.webHookUrl" class="text-red-600 text-sm mt-1 font-medium">{{ geofencingForm.errors.webHookUrl[0] }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Geofencing Section -->
                        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <h4 class="text-base font-semibold text-black mb-3">Geofencing Configuration</h4>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Enable Geofencing</label>
                                    <div class="flex space-x-4">
                                        <label class="flex items-center">
                                            <input v-model="geofencingForm.geoFancing" type="radio" value="yes" class="mr-2" />
                                            <span class="text-sm font-medium">Yes</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input v-model="geofencingForm.geoFancing" type="radio" value="no" class="mr-2" />
                                            <span class="text-sm font-medium">No</span>
                                        </label>
                                    </div>
                                    <div v-if="geofencingForm.errors.geoFancing" class="text-red-600 text-sm mt-1 font-medium">{{ geofencingForm.errors.geoFancing[0] }}</div>
                                </div>

                                <!-- Geofencing Data Fields -->
                                <div v-if="geofencingForm.geoFancing === 'yes'" class="space-y-3">
                                    <h5 class="text-sm font-semibold text-gray-800">Geofencing Parameters</h5>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                        <div v-for="(field, index) in geofencingForm.geoFancingData" :key="index">
                                            <label class="block text-sm font-medium text-gray-700 mb-1">{{ field.name }}</label>
                                            <input v-model="field.value"
                                                   type="text"
                                                   :placeholder="getFieldPlaceholder(field.name)"
                                                   class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-gray-500 focus:outline-none transition-colors" />
                                            <div v-if="geofencingForm.errors[`geoFancingData.${index}.value`]" class="text-red-600 text-sm mt-1 font-medium">
                                                {{ geofencingForm.errors[`geoFancingData.${index}.value`][0] }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                            <button type="button" @click="closeGeofencing" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                                Cancel
                            </button>
                            <button type="submit" :disabled="geofencingForm.processing" class="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg font-medium hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                                <span v-if="geofencingForm.processing" class="mr-2">
                                    <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </span>
                                {{ geofencingForm.processing ? 'Saving...' : 'Save Geofencing Settings' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style>
.h-screen-minus-header {
    height: calc(100vh - 65px); /* Adjust 65px to your actual header height */
}
</style>

