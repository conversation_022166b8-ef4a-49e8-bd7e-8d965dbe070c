<script setup>
import ApiTokenManager from '@/Pages/API/Partials/ApiTokenManager.vue';
import AppLayout from '@/Layouts/AppLayout.vue';

defineProps({
    tokens: Array,
    availablePermissions: Array,
    defaultPermissions: Array,
});
</script>

<template>
    <AppLayout title="API Tokens">

        <!--
         add label saying you are using free account for 2,500 requests per day and 10,000 requests per month
        -->
         <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div class="text-gray-500 text-sm">
                You are using a free account for 2,500 requests per day and 10,000 requests per month.
              
            </div>
        </div>
        <div>
            <div class="max-w-7xl mx-auto py-10 sm:px-6 lg:px-8">
                <ApiTokenManager
                    :tokens="tokens"
                    :available-permissions="availablePermissions"
                    :default-permissions="defaultPermissions"
                />
            </div>
        </div>
    </AppLayout>
</template>
