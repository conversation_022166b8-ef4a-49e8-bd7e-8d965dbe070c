<script setup>
import { ref, computed, watch } from "vue";
import { Head } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, MapPin, Globe, Building, Home, Hospital, Settings } from 'lucide-vue-next';
import axios from 'axios';
import { debounce } from 'lodash';

// --- Reactive State ---
const searchQuery = ref('');
const selectedLanguage = ref('en');
const selectedFilter = ref('all');
const showFilters = ref(false);
const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);

// Search results structure
const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'healthFacs'];
const searchResults = ref(
    Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]))
);



// Configuration
const languages = [
    { code: 'rw', name: 'Kinyarwanda' },
    { code: 'en', name: 'English' },
    { code: 'fr', name: 'Français' },
];

const filters = [
    { code: 'all', name: 'All Locations', icon: Globe },
    { code: 'province', name: 'Provinces', icon: Building },
    { code: 'district', name: 'Districts', icon: Building },
    { code: 'sector', name: 'Sectors', icon: Home },
    { code: 'cell', name: 'Cells', icon: Home },
    { code: 'village', name: 'Villages', icon: Home },
    { code: 'health_fac', name: 'Health Facilities', icon: Hospital },
];

// --- Computed Properties ---
const totalResults = computed(() => {
    return ALL_RESULT_TYPES.reduce((sum, type) => sum + (searchResults.value[type]?.length || 0), 0);
});

const hasResults = computed(() => totalResults.value > 0);

const getPlaceholderText = computed(() => ({
    rw: 'Shakisha ahantu mu Rwanda...',
    en: 'Search Rwanda locations...',
    fr: 'Rechercher des lieux au Rwanda...',
}[selectedLanguage.value] || 'Search locations...'));



// --- Search Functions ---
const performSearch = debounce(async (query, lang, filter) => {
    if (query.trim().length < 2) {
        clearSearch(false);
        return;
    }

    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();

    try {
        const { data } = await axios.post('/map/search-json', {
            searchQuery: query.trim(),
            lang,
            filterData: filter,
        });

        searchResults.value = Object.fromEntries(
            ALL_RESULT_TYPES.map(type => {
                const items = data[type] || [];
                return [
                    type,
                    items.map(item => ({
                        ...item,
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }))
                ];
            })
        );
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || 'Failed to fetch search results.';
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
    }
}, 300);

const clearSearch = (resetQuery = true) => {
    if (resetQuery) {
        searchQuery.value = '';
    }
    searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));
    error.value = null;
    isLoading.value = false;
    searchTime.value = 0;
};

const getDisplayName = (result) => {
    return result[`name_${selectedLanguage.value}`] || result.name_en || result.name_local || result.name || 'N/A';
};

// --- Watchers ---
watch(searchQuery, (newQuery) => {
    performSearch(newQuery, selectedLanguage.value, selectedFilter.value);
});

watch(selectedLanguage, (newLang) => {
    if (searchQuery.value.trim()) {
        performSearch(searchQuery.value, newLang, selectedFilter.value);
    }
});

watch(selectedFilter, (newFilter) => {
    if (searchQuery.value.trim()) {
        performSearch(searchQuery.value, selectedLanguage.value, newFilter);
    }
});
</script>

<template>
    <Head title="Rwanda Geo - Professional Geocoding Platform" />
    <AppLayout>
        <!-- Main Container -->
        <div class="bg-cta-background-two">
            <!-- Hero Section -->
            <section class="py-20 px-4 sm:px-6 lg:px-8">
                <div class="max-w-6xl mx-auto">
                    <div class="flex flex-col items-center justify-center space-y-16 min-h-[50vh]">

                        <!-- Hero Icon -->
                        <div class="flex justify-center">
                            <div class="w-24 h-24 bg-gorilla-primary rounded-3xl flex items-center justify-center">
                                <MapPin class="w-12 h-12 text-white" />
                            </div>
                        </div>

                        <!-- Main Heading -->
                        <div class="text-center">
                            <h1 class="text-4xl md:text-5xl lg:text-6xl font-normal text-gorilla-primary-three tracking-tight mb-4">
                                Rwanda Geo
                            </h1>
                            <p class="text-lg md:text-xl text-gray-600 font-light max-w-2xl mx-auto">
                                Professional geocoding platform for Rwanda's administrative boundaries and locations
                            </p>
                        </div>

                        <!-- Search Card -->
                        <div class="w-full max-w-2xl">
                            <Card class="border border-gray-200 rounded-2xl bg-white">
                                <CardHeader class="pb-4">
                                    <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                                        <CardTitle class="text-lg font-medium text-gorilla-primary-three">Search Locations</CardTitle>
                                        <button
                                            @click="showFilters = !showFilters"
                                            class="border border-gorilla-primary-two text-gorilla-primary-two hover:bg-gorilla-primary-two hover:text-white rounded-lg px-3 py-2 text-sm font-medium flex items-center gap-2 transition-colors"
                                        >
                                            <Settings class="w-4 h-4" />
                                            Filters
                                        </button>
                                    </div>
                                </CardHeader>
                                <CardContent class="space-y-4">
                                    <!-- Search Input -->
                                    <div class="relative">
                                        <Input
                                            v-model="searchQuery"
                                            :placeholder="getPlaceholderText"
                                            class="h-12 pl-12 pr-4 text-base border border-gray-300 rounded-lg focus:border-gorilla-primary focus:ring-0 transition-colors"
                                        />
                                        <Search class="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                                    </div>

                                    <!-- Filters Panel -->
                                    <div v-if="showFilters" class="space-y-4 p-4 bg-cta-background-one rounded-lg border border-gray-200">
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <!-- Language Filter -->
                                            <div>
                                                <label class="block text-sm font-medium text-gorilla-primary-three mb-2">Language</label>
                                                <Select v-model="selectedLanguage">
                                                    <SelectTrigger class="h-10 border border-gray-300 rounded-lg focus:border-gorilla-primary">
                                                        <SelectValue placeholder="Select language" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem v-for="lang in languages" :key="lang.code" :value="lang.code">
                                                            {{ lang.name }}
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <!-- Location Type Filter -->
                                            <div>
                                                <label class="block text-sm font-medium text-gorilla-primary-three mb-2">Location Type</label>
                                                <Select v-model="selectedFilter">
                                                    <SelectTrigger class="h-10 border border-gray-300 rounded-lg focus:border-gorilla-primary">
                                                        <SelectValue placeholder="Select type" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem v-for="filter in filters" :key="filter.code" :value="filter.code">
                                                            <div class="flex items-center gap-2">
                                                                <component :is="filter.icon" class="w-4 h-4" />
                                                                {{ filter.name }}
                                                            </div>
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Search Results -->
                                    <div v-if="isLoading || hasResults || error" class="space-y-3">
                                        <!-- Loading State -->
                                        <div v-if="isLoading" class="flex items-center justify-center py-4">
                                            <div class="flex items-center space-x-2">
                                                <div class="animate-spin rounded-full h-4 w-4 border-2 border-gray-400 border-t-transparent"></div>
                                                <span class="text-gray-600 text-sm">Searching...</span>
                                            </div>
                                        </div>

                                        <!-- Error State -->
                                        <div v-if="error" class="p-3 bg-red-50 border border-red-200 rounded-lg">
                                            <p class="text-red-700 text-sm">{{ error }}</p>
                                        </div>

                                        <!-- Results -->
                                        <div v-if="hasResults && !isLoading" class="space-y-2">
                                            <p class="text-xs text-gray-500 text-center">
                                                {{ totalResults }} location{{ totalResults !== 1 ? 's' : '' }} found
                                            </p>

                                            <div class="max-h-48 overflow-y-auto">
                                                <div class="bg-white border border-gray-200 rounded-lg divide-y divide-gray-100">
                                                    <template v-for="(type, typeKey) in searchResults" :key="typeKey">
                                                        <template v-if="type.length > 0">
                                                            <div v-for="result in type.slice(0, 2)" :key="result.id"
                                                                 class="p-4 hover:bg-cta-background-two cursor-pointer transition-colors">
                                                                <div class="flex items-center justify-between">
                                                                    <div class="flex items-center space-x-3">
                                                                        <div class="w-8 h-8 bg-gorilla-primary/10 rounded-full flex items-center justify-center">
                                                                            <MapPin class="h-4 w-4 text-gorilla-primary" />
                                                                        </div>
                                                                        <div>
                                                                            <h4 class="text-sm font-medium text-gorilla-primary-three">{{ getDisplayName(result) }}</h4>
                                                                            <p v-if="result.address" class="text-xs text-gray-600">
                                                                                {{ result.address }}
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                    <div class="text-right">
                                                                        <span class="text-xs text-gray-500">
                                                                            {{ result.latitude?.toFixed(4) }}, {{ result.longitude?.toFixed(4) }}
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </template>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </AppLayout>
</template>

<style scoped>
/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Smooth transitions */
* {
    transition-property: color, background-color, border-color;
    transition-duration: 150ms;
    transition-timing-function: ease-in-out;
}
</style>
