<script setup>
import { Head } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import { ref } from 'vue';
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Palette,
  MousePointer,
  FileText,
  Navigation,
  CreditCard,
  Zap,
  BarChart3,
  BookOpen,
  MapPin,
  Map,
  Settings,
  Search,
  Plus,
  Minus,
  Layers,
  <PERSON><PERSON>,
  Eye,
  EyeOff,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Grid3X3,
  Type,
  Smartphone,
  Tablet,
  Monitor,
  CheckCircle,
  XCircle,
  Building,
  Users,
  User,
  Hospital,
  GraduationCap,
  Store,
  Table2,
  ChevronDown,
  Bell
} from 'lucide-vue-next';

// Navigation state
const activeSection = ref('colors');

// Color palette data
const colorPalette = {
  primary: {
    'gorilla-primary': '#1A773E',
    'gorilla-primary-two': '#1C5172',
    'gorilla-primary-three': '#303017'
  },
  backgrounds: {
    'cta-background-one': '#edefeb',
    'cta-background-two': '#fafbfa'
  }
};

// Navigation sections
const sections = [
  { id: 'colors', name: 'Color System', icon: Palette },
  { id: 'buttons', name: 'Buttons', icon: MousePointer },
  { id: 'forms', name: 'Forms & Inputs', icon: FileText },
  { id: 'navigation', name: 'Navigation', icon: Navigation },
  { id: 'cards', name: 'Cards', icon: CreditCard },
  { id: 'avatars', name: 'Avatars', icon: User },
  { id: 'tables', name: 'Tables', icon: Table2 },
  { id: 'dropdowns', name: 'Dropdowns', icon: ChevronDown },
  { id: 'toasts', name: 'Toast Notifications', icon: Bell },
  { id: 'interactive', name: 'Interactive', icon: Zap },
  { id: 'icons', name: 'Icons', icon: Search },
  { id: 'data-viz', name: 'Data Visualization', icon: BarChart3 },
  { id: 'grid', name: 'Grid System', icon: Grid3X3 },
  { id: 'writing', name: 'Writing & Grammar', icon: Type },
  { id: 'guidelines', name: 'Usage Guidelines', icon: BookOpen }
];

// Helper function to copy color to clipboard
const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text);
};

</script>

<template>
  <Head title="OnRwanda Geo - Design System" />
  <AppLayout>
    <div class="min-h-screen bg-cta-background-two">
      <!-- Header -->
      <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="py-8">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-4xl font-bold text-gorilla-primary-three">
                  OnRwanda Geo Design System
                </h1>
                <p class="mt-3 text-lg text-gray-600">
                  A comprehensive design guide for our Rwanda-inspired geo mapping platform
                </p>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium text-gray-500 mb-1">Version</div>
                <div class="text-lg font-bold text-gorilla-primary">v1.0.0</div>
                <div class="text-sm text-gray-500">Material 3 Expressive</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="max-w-7xl mx-auto px-6 lg:px-8 py-12">
        <div class="flex gap-12">
          <!-- Sidebar Navigation -->
          <div class="w-72 flex-shrink-0">
            <div class="sticky top-8">
              <nav class="space-y-3">
                <button
                  v-for="section in sections"
                  :key="section.id"
                  @click="activeSection = section.id"
                  :class="[
                    'w-full flex items-center px-5 py-4 text-left rounded-xl transition-all duration-200 font-medium',
                    activeSection === section.id
                      ? 'bg-gorilla-primary text-white'
                      : 'text-gray-700 hover:bg-cta-background-one hover:text-gorilla-primary-three'
                  ]"
                >
                  <component :is="section.icon" class="mr-4 h-5 w-5" />
                  {{ section.name }}
                </button>
              </nav>
            </div>
          </div>

          <!-- Main Content -->
          <div class="flex-1">
            <!-- Color System Section -->
            <div v-if="activeSection === 'colors'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Color System
                </h2>
                <p class="text-gray-600 mb-8">
                  Our color palette is inspired by the Rwanda flag, featuring deep greens, blues, and earth tones
                  that represent our connection to the land and our commitment to growth and stability.
                </p>
              </div>

              <!-- Primary Colors -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Primary Colors</CardTitle>
                  <CardDescription>
                    Main brand colors inspired by Rwanda's flag and natural landscape
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div
                      v-for="(color, name) in colorPalette.primary"
                      :key="name"
                      class="group cursor-pointer"
                      @click="copyToClipboard(color)"
                    >
                      <div
                        :style="{ backgroundColor: color }"
                        class="h-24 rounded-lg mb-3 border border-gray-200 group-hover:scale-105 transition-transform"
                      ></div>
                      <div class="text-sm">
                        <div class="font-medium text-gray-900">{{ name }}</div>
                        <div class="text-gray-500 font-mono">{{ color }}</div>
                        <div class="text-xs text-gray-400 mt-1">Click to copy</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Background Colors -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Background Colors</CardTitle>
                  <CardDescription>
                    Subtle background colors for CTAs and content areas
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div
                      v-for="(color, name) in colorPalette.backgrounds"
                      :key="name"
                      class="group cursor-pointer"
                      @click="copyToClipboard(color)"
                    >
                      <div
                        :style="{ backgroundColor: color }"
                        class="h-24 rounded-lg mb-3 border border-gray-200 group-hover:scale-105 transition-transform"
                      ></div>
                      <div class="text-sm">
                        <div class="font-medium text-gray-900">{{ name }}</div>
                        <div class="text-gray-500 font-mono">{{ color }}</div>
                        <div class="text-xs text-gray-400 mt-1">Click to copy</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Color Usage Guidelines -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Usage Guidelines</CardTitle>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div class="border-l-4 border-gorilla-primary pl-4">
                      <h4 class="font-semibold text-gorilla-primary-three">Gorilla Primary (#1A773E)</h4>
                      <p class="text-sm text-gray-600 mb-2">
                        Use for primary actions, main navigation, and key brand elements.
                        Represents growth and Rwanda's green landscape.
                      </p>
                      <div class="text-xs text-gray-500">
                        <strong>Use for:</strong> Primary buttons, active states, brand logos<br>
                        <strong>Avoid:</strong> Large text blocks, error states
                      </div>
                    </div>
                    <div class="border-l-4 border-gorilla-primary-two pl-4">
                      <h4 class="font-semibold text-gorilla-primary-three">Gorilla Primary Two (#1C5172)</h4>
                      <p class="text-sm text-gray-600 mb-2">
                        Use for secondary actions, links, and supporting elements.
                        Represents stability and trust.
                      </p>
                      <div class="text-xs text-gray-500">
                        <strong>Use for:</strong> Secondary buttons, links, icons, borders<br>
                        <strong>Avoid:</strong> Primary call-to-action buttons
                      </div>
                    </div>
                    <div class="border-l-4 border-gorilla-primary-three pl-4">
                      <h4 class="font-semibold text-gorilla-primary-three">Gorilla Primary Three (#303017)</h4>
                      <p class="text-sm text-gray-600 mb-2">
                        Use for text, headings, and high-contrast elements.
                        Represents earth and foundation.
                      </p>
                      <div class="text-xs text-gray-500">
                        <strong>Use for:</strong> Headings, body text, form labels<br>
                        <strong>Avoid:</strong> Large background areas, decorative elements
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Color Accessibility -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Accessibility & Contrast</CardTitle>
                  <CardDescription>
                    All colors meet WCAG 2.1 AA standards for accessibility
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                      <h4 class="font-semibold text-gorilla-primary-three">High Contrast Combinations</h4>
                      <div class="space-y-2">
                        <div class="flex items-center justify-between p-3 bg-gorilla-primary text-white rounded">
                          <span>White text on Primary</span>
                          <span class="text-xs">AAA</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gorilla-primary-two text-white rounded">
                          <span>White text on Primary Two</span>
                          <span class="text-xs">AAA</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gorilla-primary-three text-white rounded">
                          <span>White text on Primary Three</span>
                          <span class="text-xs">AAA</span>
                        </div>
                      </div>
                    </div>
                    <div class="space-y-3">
                      <h4 class="font-semibold text-gorilla-primary-three">Background Combinations</h4>
                      <div class="space-y-2">
                        <div class="flex items-center justify-between p-3 bg-cta-background-one text-gorilla-primary-three rounded border">
                          <span>Dark text on CTA Background One</span>
                          <span class="text-xs">AAA</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-cta-background-two text-gorilla-primary-three rounded border">
                          <span>Dark text on CTA Background Two</span>
                          <span class="text-xs">AAA</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Button System Section -->
            <div v-else-if="activeSection === 'buttons'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Button System
                </h2>
                <p class="text-gray-600 mb-8">
                  Buttons let users perform actions with a tap or click. Following Wise design principles
                  with Rwanda-inspired colors for our geo mapping platform.
                </p>
              </div>

              <!-- Button Types -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Button Types</CardTitle>
                  <CardDescription>
                    Default and negative button types to emphasize the nature of actions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Default Buttons</h4>
                      <p class="text-sm text-gray-600 mb-4">Standard button type that conveys a general action. Use this by default.</p>
                      <div class="flex flex-wrap gap-4">
                        <Button class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                          Search Location
                        </Button>
                        <Button class="bg-gorilla-primary-two hover:bg-gorilla-primary-two/90 text-white">
                          View Map
                        </Button>
                        <Button class="bg-gorilla-primary-three hover:bg-gorilla-primary-three/90 text-white">
                          Export Data
                        </Button>
                      </div>
                    </div>

                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Negative Buttons</h4>
                      <p class="text-sm text-gray-600 mb-4">Use for confirming destructive actions, like deleting locations or canceling transfers.</p>
                      <div class="flex flex-wrap gap-4">
                        <Button class="bg-red-600 hover:bg-red-700 text-white">
                          Delete Location
                        </Button>
                        <Button class="bg-red-600 hover:bg-red-700 text-white">
                          Cancel Transfer
                        </Button>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Type Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Default:</strong> Use Rwanda flag colors for standard actions</p>
                        <p><strong>Negative:</strong> Use red for destructive actions that need confirmation</p>
                        <p><strong>Context:</strong> Button type should match the nature of the action</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Button Priorities -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Button Priorities</CardTitle>
                  <CardDescription>
                    Visual hierarchy to help important buttons take precedence over others
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Primary</h4>
                      <p class="text-sm text-gray-600 mb-4">The most important action to move forward in a flow, acknowledge and dismiss, or finish a task.</p>
                      <div class="flex flex-wrap gap-4">
                        <Button class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                          Continue
                        </Button>
                        <Button class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                          Save Location
                        </Button>
                      </div>
                      <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <p class="text-sm text-green-800"><strong>Rule:</strong> Use one primary button per page where relevant</p>
                      </div>
                    </div>

                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Secondary</h4>
                      <p class="text-sm text-gray-600 mb-4">Use for providing alternatives to the primary action, or when none of your actions are more important than others.</p>
                      <div class="flex flex-wrap gap-4">
                        <Button variant="outline" class="border-gorilla-primary text-gorilla-primary hover:bg-gorilla-primary hover:text-white">
                          Cancel
                        </Button>
                        <Button variant="outline" class="border-gorilla-primary-two text-gorilla-primary-two hover:bg-gorilla-primary-two hover:text-white">
                          Reset Filters
                        </Button>
                      </div>
                    </div>

                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Secondary Neutral</h4>
                      <p class="text-sm text-gray-600 mb-4">For functional actions, such as copying information like coordinates, controls or navigation.</p>
                      <div class="flex flex-wrap gap-4">
                        <Button variant="outline" class="border-gray-300 text-gray-700 hover:bg-gray-50">
                          <Copy class="mr-2 h-4 w-4" />
                          Copy Coordinates
                        </Button>
                        <Button variant="outline" class="border-gray-300 text-gray-700 hover:bg-gray-50">
                          View Details
                        </Button>
                      </div>
                    </div>

                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Tertiary</h4>
                      <p class="text-sm text-gray-600 mb-4">Dismissive actions give users a way out, letting them cancel, do nothing, dismiss, or skip.</p>
                      <div class="flex flex-wrap gap-4">
                        <Button variant="ghost" class="text-gray-600 hover:bg-gray-100">
                          Skip
                        </Button>
                        <Button variant="ghost" class="text-gray-600 hover:bg-gray-100">
                          Maybe Later
                        </Button>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Priority Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Primary:</strong> One per page - the most important action</p>
                        <p><strong>Secondary:</strong> Multiple key actions at same hierarchy level</p>
                        <p><strong>Secondary Neutral:</strong> Functional actions and controls</p>
                        <p><strong>Tertiary:</strong> Dismissive actions and navigation</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Button Sizes -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Button Sizes</CardTitle>
                  <CardDescription>
                    Three different sizes: small, medium, and large for different purposes
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Large Buttons</h4>
                      <p class="text-sm text-gray-600 mb-4">Used to move users forward in a flow. Full width on mobile, auto-width on desktop.</p>
                      <div class="space-y-3">
                        <Button size="lg" class="w-full bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                          Continue to Map View
                        </Button>
                        <Button size="lg" class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                          Search Locations
                        </Button>
                      </div>
                      <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <p class="text-sm text-green-800"><strong>Use:</strong> At bottom of page or content to aid user to next step</p>
                      </div>
                      <div class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p class="text-sm text-red-800"><strong>Don't:</strong> Use large buttons inside cards or list items</p>
                      </div>
                    </div>

                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Medium Buttons</h4>
                      <p class="text-sm text-gray-600 mb-4">For inline content that needs greater emphasis than small buttons. Supports icons and avatars.</p>
                      <div class="flex flex-wrap gap-4">
                        <Button class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                          <Search class="mr-2 h-4 w-4" />
                          Search
                        </Button>
                        <Button class="bg-gorilla-primary-two hover:bg-gorilla-primary-two/90 text-white">
                          Add Location
                          <Plus class="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                      <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <p class="text-sm text-green-800"><strong>Use:</strong> In line with other buttons at the same size</p>
                      </div>
                      <div class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p class="text-sm text-red-800"><strong>Don't:</strong> Use inside of list items</p>
                      </div>
                    </div>

                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Small Buttons</h4>
                      <p class="text-sm text-gray-600 mb-4">For smaller inline content such as list items. Supports icons and wraps to content.</p>
                      <div class="flex flex-wrap gap-4">
                        <Button size="sm" class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                          <Eye class="mr-2 h-3 w-3" />
                          View
                        </Button>
                        <Button size="sm" variant="outline" class="border-gray-300 text-gray-700 hover:bg-gray-50">
                          Edit
                          <Settings class="ml-2 h-3 w-3" />
                        </Button>
                      </div>
                      <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <p class="text-sm text-green-800"><strong>Use:</strong> In line with other buttons at same size or where you need more horizontal space</p>
                      </div>
                      <div class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p class="text-sm text-red-800"><strong>Don't:</strong> Use in place of large buttons or at full width</p>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Size Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Large:</strong> Footer buttons, primary actions, full-width on mobile</p>
                        <p><strong>Medium:</strong> Inline with other content, navigation headers</p>
                        <p><strong>Small:</strong> List items, table actions, compact spaces</p>
                        <p><strong>Consistency:</strong> Use same size for buttons at same hierarchy level</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Button Accessories -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Button Accessories</CardTitle>
                  <CardDescription>
                    Icons and avatars to support button content and indicate actions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Icons</h4>
                      <p class="text-sm text-gray-600 mb-4">Use icons that best match the written content. Don't use icons for the sake of it.</p>
                      <div class="space-y-4">
                        <div>
                          <p class="text-sm font-medium text-gray-700 mb-2">Icon Left (Supporting)</p>
                          <div class="flex flex-wrap gap-4">
                            <Button class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                              <MapPin class="mr-2 h-4 w-4" />
                              Find Location
                            </Button>
                            <Button class="bg-gorilla-primary-two hover:bg-gorilla-primary-two/90 text-white">
                              <Map class="mr-2 h-4 w-4" />
                              View Map
                            </Button>
                          </div>
                        </div>
                        <div>
                          <p class="text-sm font-medium text-gray-700 mb-2">Icon Right (Actions)</p>
                          <div class="flex flex-wrap gap-4">
                            <Button class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                              Continue
                              <ChevronRight class="ml-2 h-4 w-4" />
                            </Button>
                            <Button class="bg-gorilla-primary-two hover:bg-gorilla-primary-two/90 text-white">
                              Export
                              <Copy class="ml-2 h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                      <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <p class="text-sm text-green-800"><strong>Rule:</strong> Icon right for actions, icon left to support the message</p>
                      </div>
                      <div class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p class="text-sm text-red-800"><strong>Don't:</strong> Place actions on the left, or supporting icons on the right</p>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Accessory Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Icon Left:</strong> Use to support and clarify the button message</p>
                        <p><strong>Icon Right:</strong> Use for actions and directional movement</p>
                        <p><strong>Consistency:</strong> Use Lucide icons only, 16px for small, 20px for medium/large</p>
                        <p><strong>Purpose:</strong> Icons should add meaning, not decoration</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Button Content & Accessibility -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Button Content & Accessibility</CardTitle>
                  <CardDescription>
                    Content guidelines and accessibility considerations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Button States</h4>
                      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="space-y-3">
                          <p class="text-sm font-medium text-gray-700">Normal</p>
                          <Button class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                            Search Location
                          </Button>
                        </div>
                        <div class="space-y-3">
                          <p class="text-sm font-medium text-gray-700">Loading</p>
                          <Button disabled class="bg-gorilla-primary text-white cursor-wait">
                            <div class="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                            Searching...
                          </Button>
                        </div>
                        <div class="space-y-3">
                          <p class="text-sm font-medium text-gray-700">Disabled</p>
                          <Button disabled class="bg-gray-300 text-gray-500 cursor-not-allowed">
                            Search Location
                          </Button>
                        </div>
                      </div>
                    </div>

                    <!-- Content Guidelines -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Button Content</h4>
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h5 class="font-medium text-green-700 mb-2 flex items-center">
                            <CheckCircle class="mr-2 h-4 w-4" /> Do
                          </h5>
                          <div class="space-y-2">
                            <Button size="sm" class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                              Search
                            </Button>
                            <Button size="sm" class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                              Add location
                            </Button>
                            <Button size="sm" class="bg-red-600 hover:bg-red-700 text-white">
                              Delete map
                            </Button>
                          </div>
                        </div>
                        <div>
                          <h5 class="font-medium text-red-700 mb-2 flex items-center">
                            <XCircle class="mr-2 h-4 w-4" /> Don't
                          </h5>
                          <div class="space-y-2">
                            <Button size="sm" variant="outline" class="border-red-300 text-red-600 cursor-not-allowed">
                              I want to search
                            </Button>
                            <Button size="sm" variant="outline" class="border-red-300 text-red-600 cursor-not-allowed">
                              Add my location
                            </Button>
                            <Button size="sm" variant="outline" class="border-red-300 text-red-600 cursor-not-allowed">
                              Delete my map
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Content & Accessibility Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Text:</strong> Start with a verb, be 1-2 words, describe the action</p>
                        <p><strong>Case:</strong> Use sentence case (only capitalize first letter)</p>
                        <p><strong>Pronouns:</strong> Avoid 'me', 'my', 'I' - use direct action words</p>
                        <p><strong>Accessibility:</strong> Button text is read aloud by screen readers</p>
                        <p><strong>Responsive:</strong> Text wraps and button grows, never truncates</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Form Components Section -->
            <div v-else-if="activeSection === 'forms'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Form Components
                </h2>
                <p class="text-gray-600 mb-8">
                  Form elements designed for our geo mapping platform with clear labels,
                  proper validation states, and accessibility in mind.
                </p>
              </div>

              <!-- Input Fields -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Input Fields</CardTitle>
                  <CardDescription>
                    Text inputs for location data, coordinates, and user information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <!-- Basic Inputs -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Basic Input Fields</h4>
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                          <Label class="text-gorilla-primary-three font-medium">Location Name</Label>
                          <Input
                            placeholder="Enter location name..."
                            class="border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20"
                          />
                        </div>
                        <div class="space-y-2">
                          <Label class="text-gorilla-primary-three font-medium">Coordinates</Label>
                          <Input
                            placeholder="-1.9441, 30.0619"
                            class="border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20"
                          />
                        </div>
                      </div>
                    </div>

                    <!-- Search Input -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Search Input</h4>
                      <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                          </svg>
                        </div>
                        <Input
                          placeholder="Search Rwanda locations..."
                          class="pl-10 border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20"
                        />
                      </div>
                    </div>

                    <!-- Input States -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Input States</h4>
                      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="space-y-2">
                          <Label class="text-gorilla-primary-three font-medium">Normal</Label>
                          <Input
                            placeholder="Normal state"
                            class="border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20"
                          />
                        </div>
                        <div class="space-y-2">
                          <Label class="text-red-600 font-medium">Error</Label>
                          <Input
                            placeholder="Error state"
                            class="border-red-300 focus:border-red-500 focus:ring-red-500/20"
                          />
                          <p class="text-xs text-red-600">This field is required</p>
                        </div>
                        <div class="space-y-2">
                          <Label class="text-gray-400 font-medium">Disabled</Label>
                          <Input
                            placeholder="Disabled state"
                            disabled
                            class="bg-gray-50 border-gray-200"
                          />
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Usage Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Focus Color:</strong> #1A773E (gorilla-primary)</p>
                        <p><strong>Border:</strong> Gray-300 default, brand color on focus</p>
                        <p><strong>Placeholder:</strong> Use descriptive text or examples</p>
                        <p><strong>Labels:</strong> Always provide clear, descriptive labels</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Select Dropdowns -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Select Dropdowns</CardTitle>
                  <CardDescription>
                    Dropdown selectors for administrative divisions and data categories
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Administrative Levels</h4>
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                          <Label class="text-gorilla-primary-three font-medium">Province</Label>
                          <Select>
                            <SelectTrigger class="border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20">
                              <SelectValue placeholder="Select a province" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="kigali">Kigali City</SelectItem>
                              <SelectItem value="eastern">Eastern Province</SelectItem>
                              <SelectItem value="northern">Northern Province</SelectItem>
                              <SelectItem value="southern">Southern Province</SelectItem>
                              <SelectItem value="western">Western Province</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div class="space-y-2">
                          <Label class="text-gorilla-primary-three font-medium">District</Label>
                          <Select>
                            <SelectTrigger class="border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20">
                              <SelectValue placeholder="Select a district" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="gasabo">Gasabo</SelectItem>
                              <SelectItem value="kicukiro">Kicukiro</SelectItem>
                              <SelectItem value="nyarugenge">Nyarugenge</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Usage Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Use for:</strong> Administrative divisions, data categories, filter options</p>
                        <p><strong>Placeholder:</strong> Use "Select a..." format for clarity</p>
                        <p><strong>Options:</strong> Order alphabetically or by importance</p>
                        <p><strong>Focus:</strong> Same styling as input fields</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Navigation Components Section -->
            <div v-else-if="activeSection === 'navigation'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Navigation Components
                </h2>
                <p class="text-gray-600 mb-8">
                  Navigation elements with rounded styling for seamless movement between
                  main pages, settings, and different sections of the geo platform.
                </p>
              </div>

              <!-- Main Navigation -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Main Navigation</CardTitle>
                  <CardDescription>
                    Primary navigation with rounded design for main sections
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <!-- Horizontal Navigation -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Horizontal Navigation</h4>
                      <div class="bg-white border border-gray-200 rounded-full p-2 inline-flex space-x-1">
                        <button class="px-6 py-2 rounded-full bg-gorilla-primary text-white text-sm font-medium">
                          Map View
                        </button>
                        <button class="px-6 py-2 rounded-full text-gorilla-primary-three hover:bg-cta-background-one text-sm font-medium">
                          Data
                        </button>
                        <button class="px-6 py-2 rounded-full text-gorilla-primary-three hover:bg-cta-background-one text-sm font-medium">
                          Analytics
                        </button>
                        <button class="px-6 py-2 rounded-full text-gorilla-primary-three hover:bg-cta-background-one text-sm font-medium">
                          Settings
                        </button>
                      </div>
                    </div>

                    <!-- Vertical Navigation -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Vertical Navigation</h4>
                      <div class="bg-white border border-gray-200 rounded-2xl p-3 w-64">
                        <nav class="space-y-1">
                          <a href="#" class="flex items-center px-4 py-3 rounded-xl bg-gorilla-primary text-white">
                            <Map class="mr-3 h-5 w-5" />
                            Map Dashboard
                          </a>
                          <a href="#" class="flex items-center px-4 py-3 rounded-xl text-gorilla-primary-three hover:bg-cta-background-one">
                            <MapPin class="mr-3 h-5 w-5" />
                            Locations
                          </a>
                          <a href="#" class="flex items-center px-4 py-3 rounded-xl text-gorilla-primary-three hover:bg-cta-background-one">
                            <BarChart3 class="mr-3 h-5 w-5" />
                            Analytics
                          </a>
                          <a href="#" class="flex items-center px-4 py-3 rounded-xl text-gorilla-primary-three hover:bg-cta-background-one">
                            <Settings class="mr-3 h-5 w-5" />
                            Settings
                          </a>
                        </nav>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Usage Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Active State:</strong> Use gorilla-primary background with white text</p>
                        <p><strong>Hover State:</strong> Use cta-background-one for subtle feedback</p>
                        <p><strong>Border Radius:</strong> Use rounded-full for horizontal, rounded-xl for vertical</p>
                        <p><strong>Icons:</strong> Include meaningful icons for better recognition</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Breadcrumbs -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Breadcrumbs</CardTitle>
                  <CardDescription>
                    Location hierarchy navigation for administrative levels
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Administrative Breadcrumbs</h4>
                      <nav class="flex items-center space-x-2 text-sm">
                        <a href="#" class="text-gorilla-primary hover:text-gorilla-primary/80">Rwanda</a>
                        <span class="text-gray-400">/</span>
                        <a href="#" class="text-gorilla-primary hover:text-gorilla-primary/80">Kigali City</a>
                        <span class="text-gray-400">/</span>
                        <a href="#" class="text-gorilla-primary hover:text-gorilla-primary/80">Gasabo District</a>
                        <span class="text-gray-400">/</span>
                        <span class="text-gorilla-primary-three font-medium">Kimisagara Sector</span>
                      </nav>
                    </div>

                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Rounded Breadcrumbs</h4>
                      <nav class="flex items-center space-x-1">
                        <span class="px-3 py-1 bg-cta-background-one text-gorilla-primary-three rounded-full text-sm">
                          Rwanda
                        </span>
                        <span class="text-gray-400">→</span>
                        <span class="px-3 py-1 bg-cta-background-one text-gorilla-primary-three rounded-full text-sm">
                          Kigali City
                        </span>
                        <span class="text-gray-400">→</span>
                        <span class="px-3 py-1 bg-gorilla-primary text-white rounded-full text-sm">
                          Gasabo District
                        </span>
                      </nav>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Usage Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Use for:</strong> Administrative hierarchy, data drill-down navigation</p>
                        <p><strong>Current Page:</strong> Use gorilla-primary-three color, no link</p>
                        <p><strong>Separators:</strong> Use "/" for simple, "→" for rounded style</p>
                        <p><strong>Links:</strong> Use gorilla-primary with hover states</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Tab Navigation -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Tab Navigation</CardTitle>
                  <CardDescription>
                    Content switching with rounded tab design
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Rounded Tabs</h4>
                      <div class="bg-cta-background-one p-1 rounded-lg inline-flex">
                        <button class="px-4 py-2 rounded-md bg-white text-gorilla-primary-three font-medium border border-gray-200">
                          Overview
                        </button>
                        <button class="px-4 py-2 rounded-md text-gray-600 hover:text-gorilla-primary-three">
                          Demographics
                        </button>
                        <button class="px-4 py-2 rounded-md text-gray-600 hover:text-gorilla-primary-three">
                          Infrastructure
                        </button>
                      </div>
                    </div>

                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Underline Tabs</h4>
                      <div class="border-b border-gray-200">
                        <nav class="flex space-x-8">
                          <button class="py-2 px-1 border-b-2 border-gorilla-primary text-gorilla-primary font-medium">
                            Map Data
                          </button>
                          <button class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gorilla-primary-three">
                            Statistics
                          </button>
                          <button class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gorilla-primary-three">
                            Export
                          </button>
                        </nav>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Usage Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Rounded Tabs:</strong> Use for primary content switching</p>
                        <p><strong>Underline Tabs:</strong> Use for secondary navigation within content</p>
                        <p><strong>Active State:</strong> White background for rounded, colored border for underline</p>
                        <p><strong>Spacing:</strong> Consistent padding and spacing between tabs</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Card Components Section -->
            <div v-else-if="activeSection === 'cards'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Card Components
                </h2>
                <p class="text-gray-600 mb-8">
                  Clean, expressive card designs following Material 3 principles for displaying data,
                  statistics, and content across the geo mapping platform with no shadows.
                </p>
              </div>

              <!-- Dashboard Cards -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Dashboard Cards</CardTitle>
                  <CardDescription>
                    Statistical cards for key metrics and KPIs
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <!-- Metric Cards -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Metric Cards</h4>
                      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Primary Metric Card -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                          <div class="flex items-center justify-between">
                            <div>
                              <p class="text-sm text-gray-600">Total Locations</p>
                              <p class="text-3xl font-bold text-gorilla-primary-three">2,847</p>
                              <p class="text-sm text-gorilla-primary">↗ +12% from last month</p>
                            </div>
                            <div class="w-12 h-12 bg-gorilla-primary/10 rounded-lg flex items-center justify-center">
                              <MapPin class="h-6 w-6 text-gorilla-primary" />
                            </div>
                          </div>
                        </div>

                        <!-- Secondary Metric Card -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                          <div class="flex items-center justify-between">
                            <div>
                              <p class="text-sm text-gray-600">Active Maps</p>
                              <p class="text-3xl font-bold text-gorilla-primary-three">156</p>
                              <p class="text-sm text-gorilla-primary-two">↗ +8% from last month</p>
                            </div>
                            <div class="w-12 h-12 bg-gorilla-primary-two/10 rounded-lg flex items-center justify-center">
                              <Map class="h-6 w-6 text-gorilla-primary-two" />
                            </div>
                          </div>
                        </div>

                        <!-- Neutral Metric Card -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                          <div class="flex items-center justify-between">
                            <div>
                              <p class="text-sm text-gray-600">Data Points</p>
                              <p class="text-3xl font-bold text-gorilla-primary-three">45.2K</p>
                              <p class="text-sm text-gray-500">→ No change</p>
                            </div>
                            <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                              <BarChart3 class="h-6 w-6 text-gray-600" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Usage Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Background:</strong> White with subtle border, no shadows</p>
                        <p><strong>Icons:</strong> Use background color matching the metric importance</p>
                        <p><strong>Typography:</strong> Large numbers, small descriptive text</p>
                        <p><strong>Trends:</strong> Use brand colors for positive trends</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Content Cards -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Content Cards</CardTitle>
                  <CardDescription>
                    Cards for displaying location information and data details
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <!-- Location Cards -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Location Cards</h4>
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Detailed Location Card -->
                        <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                          <div class="h-32 bg-gorilla-primary"></div>
                          <div class="p-6">
                            <h3 class="text-lg font-semibold text-gorilla-primary-three mb-2">Gasabo District</h3>
                            <p class="text-sm text-gray-600 mb-4">
                              One of the three districts that make up the city of Kigali, Rwanda.
                            </p>
                            <div class="space-y-2">
                              <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Population:</span>
                                <span class="text-gorilla-primary-three font-medium">530,907</span>
                              </div>
                              <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Area:</span>
                                <span class="text-gorilla-primary-three font-medium">429.3 km²</span>
                              </div>
                              <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Sectors:</span>
                                <span class="text-gorilla-primary-three font-medium">15</span>
                              </div>
                            </div>
                            <Button class="w-full mt-4 bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                              View Details
                            </Button>
                          </div>
                        </div>

                        <!-- Simple Location Card -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                          <div class="flex items-start justify-between mb-4">
                            <div>
                              <h3 class="text-lg font-semibold text-gorilla-primary-three">Kicukiro District</h3>
                              <p class="text-sm text-gray-600">Kigali City, Rwanda</p>
                            </div>
                            <div class="w-10 h-10 bg-gorilla-primary-two/10 rounded-lg flex items-center justify-center">
                              <Building class="h-5 w-5 text-gorilla-primary-two" />
                            </div>
                          </div>
                          <div class="space-y-2 mb-4">
                            <div class="flex items-center text-sm text-gray-600">
                              <MapPin class="mr-2 h-4 w-4" />
                              <span>-1.9659, 30.1044</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                              <Users class="mr-2 h-4 w-4" />
                              <span>318,061 residents</span>
                            </div>
                          </div>
                          <div class="flex space-x-2">
                            <Button size="sm" variant="outline" class="border-gorilla-primary-two text-gorilla-primary-two hover:bg-gorilla-primary-two hover:text-white">
                              View Map
                            </Button>
                            <Button size="sm" variant="ghost" class="text-gorilla-primary hover:bg-gorilla-primary/10">
                              Details
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Usage Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Headers:</strong> Use solid colors only - no gradients for premium aesthetic</p>
                        <p><strong>Content:</strong> Clear hierarchy with titles, descriptions, and metadata</p>
                        <p><strong>Actions:</strong> Include relevant action buttons at the bottom</p>
                        <p><strong>Icons:</strong> Use meaningful icons to represent content type</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- List Cards -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">List Cards</CardTitle>
                  <CardDescription>
                    Cards for displaying lists of items and search results
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Search Results</h4>
                      <div class="bg-white border border-gray-200 rounded-lg divide-y divide-gray-100">
                        <div class="p-4 hover:bg-cta-background-two cursor-pointer">
                          <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                              <div class="w-8 h-8 bg-gorilla-primary/10 rounded-full flex items-center justify-center">
                                <Hospital class="h-4 w-4 text-gorilla-primary" />
                              </div>
                              <div>
                                <h4 class="text-sm font-medium text-gorilla-primary-three">King Faisal Hospital</h4>
                                <p class="text-xs text-gray-600">Kacyiru, Gasabo District</p>
                              </div>
                            </div>
                            <span class="text-xs text-gray-500">2.3 km</span>
                          </div>
                        </div>
                        <div class="p-4 hover:bg-cta-background-two cursor-pointer">
                          <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                              <div class="w-8 h-8 bg-gorilla-primary-two/10 rounded-full flex items-center justify-center">
                                <GraduationCap class="h-4 w-4 text-gorilla-primary-two" />
                              </div>
                              <div>
                                <h4 class="text-sm font-medium text-gorilla-primary-three">University of Rwanda</h4>
                                <p class="text-xs text-gray-600">Remera, Gasabo District</p>
                              </div>
                            </div>
                            <span class="text-xs text-gray-500">1.8 km</span>
                          </div>
                        </div>
                        <div class="p-4 hover:bg-cta-background-two cursor-pointer">
                          <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                              <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                <Store class="h-4 w-4 text-gray-600" />
                              </div>
                              <div>
                                <h4 class="text-sm font-medium text-gorilla-primary-three">Kigali City Market</h4>
                                <p class="text-xs text-gray-600">Nyarugenge District</p>
                              </div>
                            </div>
                            <span class="text-xs text-gray-500">4.1 km</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Usage Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Hover States:</strong> Use cta-background-two for subtle feedback</p>
                        <p><strong>Dividers:</strong> Use subtle gray dividers between items</p>
                        <p><strong>Icons:</strong> Consistent icon treatment with colored backgrounds</p>
                        <p><strong>Metadata:</strong> Include relevant secondary information</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Avatar Components Section -->
            <div v-else-if="activeSection === 'avatars'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Avatar Components
                </h2>
                <p class="text-gray-600 mb-8">
                  Avatars represent users and entities in the geo mapping platform.
                  Following Wise design principles for consistent user representation.
                </p>
              </div>

              <!-- Avatar Sizes -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Avatar Sizes</CardTitle>
                  <CardDescription>
                    Different sizes for various contexts and use cases
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Size Variants</h4>
                      <div class="flex flex-wrap items-center gap-6">
                        <!-- Extra Small -->
                        <div class="text-center">
                          <div class="w-6 h-6 bg-gorilla-primary rounded-full flex items-center justify-center text-white text-xs font-medium mb-2">
                            JD
                          </div>
                          <p class="text-xs text-gray-600">XS (24px)</p>
                        </div>

                        <!-- Small -->
                        <div class="text-center">
                          <div class="w-8 h-8 bg-gorilla-primary-two rounded-full flex items-center justify-center text-white text-sm font-medium mb-2">
                            JD
                          </div>
                          <p class="text-xs text-gray-600">S (32px)</p>
                        </div>

                        <!-- Medium -->
                        <div class="text-center">
                          <div class="w-10 h-10 bg-gorilla-primary-three rounded-full flex items-center justify-center text-white text-sm font-medium mb-2">
                            JD
                          </div>
                          <p class="text-xs text-gray-600">M (40px)</p>
                        </div>

                        <!-- Large -->
                        <div class="text-center">
                          <div class="w-12 h-12 bg-gorilla-primary rounded-full flex items-center justify-center text-white text-base font-medium mb-2">
                            JD
                          </div>
                          <p class="text-xs text-gray-600">L (48px)</p>
                        </div>

                        <!-- Extra Large -->
                        <div class="text-center">
                          <div class="w-16 h-16 bg-gorilla-primary-two rounded-full flex items-center justify-center text-white text-lg font-medium mb-2">
                            JD
                          </div>
                          <p class="text-xs text-gray-600">XL (64px)</p>
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Size Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>XS (24px):</strong> List items, compact tables, inline mentions</p>
                        <p><strong>S (32px):</strong> Navigation bars, small cards, comments</p>
                        <p><strong>M (40px):</strong> Default size for most use cases, forms</p>
                        <p><strong>L (48px):</strong> User profiles, important actions</p>
                        <p><strong>XL (64px):</strong> Profile headers, onboarding flows</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Avatar Types -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Avatar Types</CardTitle>
                  <CardDescription>
                    Different avatar representations for users and entities
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">User Avatars</h4>
                      <p class="text-sm text-gray-600 mb-4">Represent individual users with initials or profile images.</p>
                      <div class="flex flex-wrap items-center gap-4">
                        <div class="w-10 h-10 bg-gorilla-primary rounded-full flex items-center justify-center text-white text-sm font-medium">
                          JD
                        </div>
                        <div class="w-10 h-10 bg-gorilla-primary-two rounded-full flex items-center justify-center text-white text-sm font-medium">
                          AM
                        </div>
                        <div class="w-10 h-10 bg-gorilla-primary-three rounded-full flex items-center justify-center text-white text-sm font-medium">
                          RK
                        </div>
                        <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-gray-600">
                          <User class="h-5 w-5" />
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Organization Avatars</h4>
                      <p class="text-sm text-gray-600 mb-4">Represent organizations, institutions, or administrative entities.</p>
                      <div class="flex flex-wrap items-center gap-4">
                        <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white">
                          <Building class="h-5 w-5" />
                        </div>
                        <div class="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center text-white">
                          <Hospital class="h-5 w-5" />
                        </div>
                        <div class="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center text-white">
                          <GraduationCap class="h-5 w-5" />
                        </div>
                        <div class="w-10 h-10 bg-orange-600 rounded-full flex items-center justify-center text-white">
                          <Store class="h-5 w-5" />
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Type Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>User Avatars:</strong> Use Rwanda flag colors, show initials or User icon</p>
                        <p><strong>Organization Avatars:</strong> Use contextual colors and relevant Lucide icons</p>
                        <p><strong>Fallback:</strong> Always provide initials or icon when image unavailable</p>
                        <p><strong>Accessibility:</strong> Include alt text describing the user or entity</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Avatar States -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Avatar States & Indicators</CardTitle>
                  <CardDescription>
                    Status indicators and interactive states for avatars
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Status Indicators</h4>
                      <p class="text-sm text-gray-600 mb-4">Show user availability or entity status with subtle indicators.</p>
                      <div class="flex flex-wrap items-center gap-6">
                        <!-- Online -->
                        <div class="relative">
                          <div class="w-10 h-10 bg-gorilla-primary rounded-full flex items-center justify-center text-white text-sm font-medium">
                            JD
                          </div>
                          <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                          <p class="text-xs text-gray-600 mt-2 text-center">Online</p>
                        </div>

                        <!-- Away -->
                        <div class="relative">
                          <div class="w-10 h-10 bg-gorilla-primary-two rounded-full flex items-center justify-center text-white text-sm font-medium">
                            AM
                          </div>
                          <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-yellow-500 border-2 border-white rounded-full"></div>
                          <p class="text-xs text-gray-600 mt-2 text-center">Away</p>
                        </div>

                        <!-- Offline -->
                        <div class="relative">
                          <div class="w-10 h-10 bg-gray-400 rounded-full flex items-center justify-center text-white text-sm font-medium">
                            RK
                          </div>
                          <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-gray-400 border-2 border-white rounded-full"></div>
                          <p class="text-xs text-gray-600 mt-2 text-center">Offline</p>
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">State Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Online:</strong> Green indicator for active users</p>
                        <p><strong>Away:</strong> Yellow indicator for temporarily unavailable users</p>
                        <p><strong>Offline:</strong> Gray indicator and desaturated avatar for inactive users</p>
                        <p><strong>Position:</strong> Status indicators always bottom-right of avatar</p>
                        <p><strong>Size:</strong> Indicator size should be 25% of avatar diameter</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Tables Section -->
            <div v-else-if="activeSection === 'tables'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Tables
                </h2>
                <p class="text-gray-600 mb-8">
                  Clean, accessible table components for displaying geographic data,
                  administrative information, and structured content.
                </p>
              </div>

              <!-- Basic Table -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Basic Data Table</CardTitle>
                  <CardDescription>
                    Standard table for displaying geographic administrative data
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>District</TableHead>
                        <TableHead>Province</TableHead>
                        <TableHead>Population</TableHead>
                        <TableHead>Area (km²)</TableHead>
                        <TableHead class="text-right">Density</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell class="font-medium">Gasabo</TableCell>
                        <TableCell>Kigali City</TableCell>
                        <TableCell>530,907</TableCell>
                        <TableCell>429.3</TableCell>
                        <TableCell class="text-right">1,237/km²</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell class="font-medium">Kicukiro</TableCell>
                        <TableCell>Kigali City</TableCell>
                        <TableCell>318,061</TableCell>
                        <TableCell>166.7</TableCell>
                        <TableCell class="text-right">1,908/km²</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell class="font-medium">Nyarugenge</TableCell>
                        <TableCell>Kigali City</TableCell>
                        <TableCell>284,551</TableCell>
                        <TableCell>134.0</TableCell>
                        <TableCell class="text-right">2,124/km²</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell class="font-medium">Musanze</TableCell>
                        <TableCell>Northern Province</TableCell>
                        <TableCell>368,267</TableCell>
                        <TableCell>530.4</TableCell>
                        <TableCell class="text-right">694/km²</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>

              <!-- Interactive Table -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Interactive Table with Actions</CardTitle>
                  <CardDescription>
                    Table with action buttons and status indicators
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Location</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Last Updated</TableHead>
                        <TableHead class="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>
                          <div class="flex items-center space-x-2">
                            <MapPin class="h-4 w-4 text-gorilla-primary" />
                            <span class="font-medium">King Faisal Hospital</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div class="flex items-center space-x-2">
                            <Hospital class="h-4 w-4 text-gorilla-primary-two" />
                            <span>Healthcare</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Active
                          </span>
                        </TableCell>
                        <TableCell>2 hours ago</TableCell>
                        <TableCell class="text-right">
                          <div class="flex justify-end space-x-2">
                            <Button size="sm" variant="outline">
                              <Eye class="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Settings class="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>
                          <div class="flex items-center space-x-2">
                            <MapPin class="h-4 w-4 text-gorilla-primary" />
                            <span class="font-medium">University of Rwanda</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div class="flex items-center space-x-2">
                            <GraduationCap class="h-4 w-4 text-gorilla-primary-two" />
                            <span>Education</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            Pending
                          </span>
                        </TableCell>
                        <TableCell>1 day ago</TableCell>
                        <TableCell class="text-right">
                          <div class="flex justify-end space-x-2">
                            <Button size="sm" variant="outline">
                              <Eye class="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Settings class="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>
                          <div class="flex items-center space-x-2">
                            <MapPin class="h-4 w-4 text-gorilla-primary" />
                            <span class="font-medium">Kigali City Market</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div class="flex items-center space-x-2">
                            <Store class="h-4 w-4 text-gorilla-primary-two" />
                            <span>Commercial</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Inactive
                          </span>
                        </TableCell>
                        <TableCell>3 days ago</TableCell>
                        <TableCell class="text-right">
                          <div class="flex justify-end space-x-2">
                            <Button size="sm" variant="outline">
                              <Eye class="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Settings class="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>

              <!-- Table Guidelines -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Table Usage Guidelines</CardTitle>
                  <CardDescription>
                    Best practices for table design and implementation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-3">Structure</h4>
                      <div class="space-y-3">
                        <div class="border-l-4 border-gorilla-primary pl-3">
                          <h5 class="font-medium text-gorilla-primary-three">Clear Headers</h5>
                          <p class="text-sm text-gray-600">Use descriptive column headers that clearly indicate content</p>
                        </div>
                        <div class="border-l-4 border-gorilla-primary-two pl-3">
                          <h5 class="font-medium text-gorilla-primary-three">Consistent Alignment</h5>
                          <p class="text-sm text-gray-600">Left-align text, right-align numbers, center-align actions</p>
                        </div>
                        <div class="border-l-4 border-gorilla-primary-three pl-3">
                          <h5 class="font-medium text-gorilla-primary-three">Logical Ordering</h5>
                          <p class="text-sm text-gray-600">Order columns by importance and logical flow</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-3">Interaction</h4>
                      <div class="space-y-3">
                        <div class="border-l-4 border-gray-300 pl-3">
                          <h5 class="font-medium text-gorilla-primary-three">Row Hover</h5>
                          <p class="text-sm text-gray-600">Subtle hover states to indicate interactivity</p>
                        </div>
                        <div class="border-l-4 border-gray-300 pl-3">
                          <h5 class="font-medium text-gorilla-primary-three">Action Buttons</h5>
                          <p class="text-sm text-gray-600">Use icon buttons for common actions like view, edit, delete</p>
                        </div>
                        <div class="border-l-4 border-gray-300 pl-3">
                          <h5 class="font-medium text-gorilla-primary-three">Status Indicators</h5>
                          <p class="text-sm text-gray-600">Use colored badges for status with clear meaning</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Usage Guidelines -->
                  <div class="bg-cta-background-one p-4 rounded-lg mt-6">
                    <h5 class="font-semibold text-gorilla-primary-three mb-2">Implementation Guidelines</h5>
                    <div class="text-sm text-gray-600 space-y-1">
                      <p><strong>Responsive:</strong> Consider horizontal scrolling on mobile devices</p>
                      <p><strong>Loading:</strong> Show skeleton states while data loads</p>
                      <p><strong>Empty States:</strong> Provide helpful messages when no data is available</p>
                      <p><strong>Pagination:</strong> Use pagination for large datasets (>50 rows)</p>
                      <p><strong>Sorting:</strong> Allow sorting on relevant columns with clear indicators</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Dropdowns Section -->
            <div v-else-if="activeSection === 'dropdowns'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Dropdown Components
                </h2>
                <p class="text-gray-600 mb-8">
                  Dropdown menus and select components for navigation, actions,
                  and data selection across the geo platform.
                </p>
              </div>

              <!-- Basic Dropdown -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Action Dropdown</CardTitle>
                  <CardDescription>
                    Standard dropdown menu for actions and navigation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Basic Action Menu</h4>
                      <div class="flex space-x-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline">
                              Actions
                              <ChevronDown class="ml-2 h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem>
                              <Eye class="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Settings class="mr-2 h-4 w-4" />
                              Edit Location
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Copy class="mr-2 h-4 w-4" />
                              Copy Coordinates
                            </DropdownMenuItem>
                            <DropdownMenuItem class="text-red-600">
                              <Minus class="mr-2 h-4 w-4" />
                              Remove
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button>
                              <MapPin class="mr-2 h-4 w-4" />
                              Add Location
                              <ChevronDown class="ml-2 h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem>
                              <Hospital class="mr-2 h-4 w-4" />
                              Healthcare Facility
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <GraduationCap class="mr-2 h-4 w-4" />
                              Educational Institution
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Store class="mr-2 h-4 w-4" />
                              Commercial Location
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Building class="mr-2 h-4 w-4" />
                              Government Building
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Dropdown Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Icons:</strong> Use consistent Lucide icons to clarify actions</p>
                        <p><strong>Grouping:</strong> Group related actions together with separators</p>
                        <p><strong>Destructive Actions:</strong> Use red text for delete/remove actions</p>
                        <p><strong>Keyboard:</strong> Ensure keyboard navigation works properly</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Toast Notifications Section -->
            <div v-else-if="activeSection === 'toasts'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Toast Notifications
                </h2>
                <p class="text-gray-600 mb-8">
                  Non-intrusive notification system for user feedback,
                  status updates, and system messages.
                </p>
              </div>

              <!-- Toast Examples -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Toast Variants</CardTitle>
                  <CardDescription>
                    Different types of toast notifications for various use cases
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <!-- Success Toast -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Success Notifications</h4>
                      <div class="bg-green-50 border border-green-200 rounded-lg p-4 flex items-start space-x-3">
                        <CheckCircle class="h-5 w-5 text-green-600 mt-0.5" />
                        <div class="flex-1">
                          <h5 class="font-medium text-green-900">Location Added Successfully</h5>
                          <p class="text-sm text-green-700 mt-1">
                            King Faisal Hospital has been added to the map with coordinates -1.9441, 30.0619
                          </p>
                        </div>
                        <button class="text-green-600 hover:text-green-800">
                          <XCircle class="h-4 w-4" />
                        </button>
                      </div>
                    </div>

                    <!-- Error Toast -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Error Notifications</h4>
                      <div class="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3">
                        <XCircle class="h-5 w-5 text-red-600 mt-0.5" />
                        <div class="flex-1">
                          <h5 class="font-medium text-red-900">Failed to Load Map Data</h5>
                          <p class="text-sm text-red-700 mt-1">
                            Unable to connect to the mapping service. Please check your connection and try again.
                          </p>
                        </div>
                        <button class="text-red-600 hover:text-red-800">
                          <XCircle class="h-4 w-4" />
                        </button>
                      </div>
                    </div>

                    <!-- Info Toast -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Information Notifications</h4>
                      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-start space-x-3">
                        <Bell class="h-5 w-5 text-blue-600 mt-0.5" />
                        <div class="flex-1">
                          <h5 class="font-medium text-blue-900">Map Data Updated</h5>
                          <p class="text-sm text-blue-700 mt-1">
                            Administrative boundaries have been updated with the latest government data.
                          </p>
                        </div>
                        <button class="text-blue-600 hover:text-blue-800">
                          <XCircle class="h-4 w-4" />
                        </button>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Toast Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Timing:</strong> Auto-dismiss after 5 seconds for success, manual dismiss for errors</p>
                        <p><strong>Position:</strong> Top-right corner, stack multiple toasts vertically</p>
                        <p><strong>Content:</strong> Clear, actionable messages with context</p>
                        <p><strong>Icons:</strong> Use appropriate Lucide icons to reinforce message type</p>
                        <p><strong>Actions:</strong> Include relevant actions when appropriate (retry, undo, etc.)</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Interactive Components Section -->
            <div v-else-if="activeSection === 'interactive'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Interactive Components
                </h2>
                <p class="text-gray-600 mb-8">
                  Interactive elements including switches, pagination, and toggles with
                  smooth animations and clear state feedback.
                </p>
              </div>

              <!-- Switch Toggles -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Switch Toggles</CardTitle>
                  <CardDescription>
                    Toggle switches for settings and feature controls
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <!-- Basic Switches -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Toggle Switches</h4>
                      <div class="space-y-4">
                        <!-- On State -->
                        <div class="flex items-center justify-between p-4 bg-cta-background-one rounded-lg">
                          <div>
                            <h5 class="font-medium text-gorilla-primary-three">Show Satellite View</h5>
                            <p class="text-sm text-gray-600">Display satellite imagery on the map</p>
                          </div>
                          <div class="relative inline-flex h-6 w-11 items-center rounded-full bg-gorilla-primary">
                            <span class="inline-block h-4 w-4 transform rounded-full bg-white transition translate-x-6"></span>
                          </div>
                        </div>

                        <!-- Off State -->
                        <div class="flex items-center justify-between p-4 bg-cta-background-one rounded-lg">
                          <div>
                            <h5 class="font-medium text-gorilla-primary-three">Auto-refresh Data</h5>
                            <p class="text-sm text-gray-600">Automatically update map data every 5 minutes</p>
                          </div>
                          <div class="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-300">
                            <span class="inline-block h-4 w-4 transform rounded-full bg-white transition translate-x-1"></span>
                          </div>
                        </div>

                        <!-- Disabled State -->
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg opacity-50">
                          <div>
                            <h5 class="font-medium text-gorilla-primary-three">GPS Tracking</h5>
                            <p class="text-sm text-gray-600">Feature not available in current plan</p>
                          </div>
                          <div class="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200">
                            <span class="inline-block h-4 w-4 transform rounded-full bg-white transition translate-x-1"></span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Usage Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>On State:</strong> Use gorilla-primary background</p>
                        <p><strong>Off State:</strong> Use gray-300 background</p>
                        <p><strong>Animation:</strong> Smooth transition for toggle movement</p>
                        <p><strong>Labels:</strong> Clear description of what the toggle controls</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Pagination -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Pagination</CardTitle>
                  <CardDescription>
                    Navigation through large datasets and search results
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <!-- Standard Pagination -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Standard Pagination</h4>
                      <div class="flex items-center justify-between">
                        <p class="text-sm text-gray-600">Showing 1 to 10 of 247 results</p>
                        <nav class="flex items-center space-x-1">
                          <button class="px-3 py-2 text-sm text-gray-400 bg-gray-100 rounded-md cursor-not-allowed">
                            Previous
                          </button>
                          <button class="px-3 py-2 text-sm bg-gorilla-primary text-white rounded-md">
                            1
                          </button>
                          <button class="px-3 py-2 text-sm text-gorilla-primary-three hover:bg-cta-background-one rounded-md">
                            2
                          </button>
                          <button class="px-3 py-2 text-sm text-gorilla-primary-three hover:bg-cta-background-one rounded-md">
                            3
                          </button>
                          <span class="px-3 py-2 text-sm text-gray-400">...</span>
                          <button class="px-3 py-2 text-sm text-gorilla-primary-three hover:bg-cta-background-one rounded-md">
                            25
                          </button>
                          <button class="px-3 py-2 text-sm text-gorilla-primary hover:bg-gorilla-primary/10 rounded-md">
                            Next
                          </button>
                        </nav>
                      </div>
                    </div>

                    <!-- Compact Pagination -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Compact Pagination</h4>
                      <div class="flex items-center justify-center space-x-2">
                        <button class="p-2 text-gray-400 bg-gray-100 rounded-lg cursor-not-allowed">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                          </svg>
                        </button>
                        <span class="px-4 py-2 text-sm text-gorilla-primary-three bg-cta-background-one rounded-lg">
                          Page 1 of 25
                        </span>
                        <button class="p-2 text-gorilla-primary hover:bg-gorilla-primary/10 rounded-lg">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Usage Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Current Page:</strong> Use gorilla-primary background</p>
                        <p><strong>Hover States:</strong> Use cta-background-one for page numbers</p>
                        <p><strong>Disabled:</strong> Use gray colors for unavailable actions</p>
                        <p><strong>Context:</strong> Show total results and current range</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Progress Indicators -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Progress Indicators</CardTitle>
                  <CardDescription>
                    Progress bars and loading states for data processing
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <!-- Progress Bars -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Progress Bars</h4>
                      <div class="space-y-4">
                        <div>
                          <div class="flex justify-between text-sm mb-2">
                            <span class="text-gorilla-primary-three">Data Upload Progress</span>
                            <span class="text-gorilla-primary">75%</span>
                          </div>
                          <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-gorilla-primary h-2 rounded-full" style="width: 75%"></div>
                          </div>
                        </div>

                        <div>
                          <div class="flex justify-between text-sm mb-2">
                            <span class="text-gorilla-primary-three">Map Processing</span>
                            <span class="text-gorilla-primary-two">45%</span>
                          </div>
                          <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-gorilla-primary-two h-2 rounded-full" style="width: 45%"></div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Loading Spinners -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Loading Spinners</h4>
                      <div class="flex items-center space-x-8">
                        <div class="flex items-center space-x-2">
                          <div class="animate-spin rounded-full h-6 w-6 border-2 border-gorilla-primary border-t-transparent"></div>
                          <span class="text-sm text-gorilla-primary-three">Loading...</span>
                        </div>
                        <div class="flex items-center space-x-2">
                          <div class="animate-spin rounded-full h-4 w-4 border-2 border-gorilla-primary-two border-t-transparent"></div>
                          <span class="text-xs text-gray-600">Processing</span>
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Usage Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Progress Color:</strong> Use brand colors to match content type</p>
                        <p><strong>Labels:</strong> Include percentage and descriptive text</p>
                        <p><strong>Spinners:</strong> Use for indeterminate loading states</p>
                        <p><strong>Animation:</strong> Smooth, consistent animation timing</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Icons Section -->
            <div v-else-if="activeSection === 'icons'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Icon System
                </h2>
                <p class="text-gray-600 mb-8">
                  Our icon system uses only Lucide Vue Next icons - no colored icons, emojis,
                  or custom graphics. Simple, consistent, and universally understood.
                </p>
              </div>

              <!-- Icon Principles -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Icon Principles</CardTitle>
                  <CardDescription>
                    Core principles for icon usage across the platform
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                      <div class="border-l-4 border-gorilla-primary pl-4">
                        <h4 class="font-semibold text-gorilla-primary-three">Lucide Only</h4>
                        <p class="text-sm text-gray-600">
                          Use only Lucide Vue Next icons. No emojis, colored icons, or custom graphics.
                        </p>
                      </div>
                      <div class="border-l-4 border-gorilla-primary-two pl-4">
                        <h4 class="font-semibold text-gorilla-primary-three">Universally Understood</h4>
                        <p class="text-sm text-gray-600">
                          Choose icons that are clear and recognizable across cultures and languages.
                        </p>
                      </div>
                    </div>
                    <div class="space-y-4">
                      <div class="border-l-4 border-gorilla-primary-three pl-4">
                        <h4 class="font-semibold text-gorilla-primary-three">Consistent Sizing</h4>
                        <p class="text-sm text-gray-600">
                          Use standard sizes: 16px, 20px, 24px for UI elements, larger for emphasis.
                        </p>
                      </div>
                      <div class="border-l-4 border-gray-300 pl-4">
                        <h4 class="font-semibold text-gorilla-primary-three">Meaningful Context</h4>
                        <p class="text-sm text-gray-600">
                          Icons should support and clarify content, not decorate or confuse.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Icon Sizes -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Icon Sizes</CardTitle>
                  <CardDescription>
                    Standard icon sizes for different use cases
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">UI Icons</h4>
                      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="text-center p-4 border border-gray-200 rounded-lg">
                          <Search class="h-4 w-4 mx-auto mb-2 text-gorilla-primary" />
                          <p class="text-sm font-medium">16px</p>
                          <p class="text-xs text-gray-600">Small UI elements</p>
                        </div>
                        <div class="text-center p-4 border border-gray-200 rounded-lg">
                          <Search class="h-5 w-5 mx-auto mb-2 text-gorilla-primary" />
                          <p class="text-sm font-medium">20px</p>
                          <p class="text-xs text-gray-600">Standard UI icons</p>
                        </div>
                        <div class="text-center p-4 border border-gray-200 rounded-lg">
                          <Search class="h-6 w-6 mx-auto mb-2 text-gorilla-primary" />
                          <p class="text-sm font-medium">24px</p>
                          <p class="text-xs text-gray-600">Buttons, navigation</p>
                        </div>
                        <div class="text-center p-4 border border-gray-200 rounded-lg">
                          <Search class="h-8 w-8 mx-auto mb-2 text-gorilla-primary" />
                          <p class="text-sm font-medium">32px</p>
                          <p class="text-xs text-gray-600">Large actions</p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Feature Icons</h4>
                      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center p-4 border border-gray-200 rounded-lg">
                          <MapPin class="h-12 w-12 mx-auto mb-2 text-gorilla-primary" />
                          <p class="text-sm font-medium">48px</p>
                          <p class="text-xs text-gray-600">Feature highlights</p>
                        </div>
                        <div class="text-center p-4 border border-gray-200 rounded-lg">
                          <Map class="h-16 w-16 mx-auto mb-2 text-gorilla-primary" />
                          <p class="text-sm font-medium">64px</p>
                          <p class="text-xs text-gray-600">Hero sections</p>
                        </div>
                        <div class="text-center p-4 border border-gray-200 rounded-lg">
                          <BarChart3 class="h-24 w-24 mx-auto mb-2 text-gorilla-primary" />
                          <p class="text-sm font-medium">96px</p>
                          <p class="text-xs text-gray-600">Empty states</p>
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Size Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>16-24px:</strong> Use for inline icons, buttons, and navigation</p>
                        <p><strong>32-48px:</strong> Use for feature callouts and important actions</p>
                        <p><strong>64-96px:</strong> Use for empty states and hero sections</p>
                        <p><strong>Consistency:</strong> Use the same size for similar functions</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Common Icons -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Common Geographic Icons</CardTitle>
                  <CardDescription>
                    Frequently used icons for geo mapping features
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                      <div class="text-center p-3 border border-gray-200 rounded-lg hover:bg-cta-background-one">
                        <MapPin class="h-6 w-6 mx-auto mb-2 text-gorilla-primary" />
                        <p class="text-xs font-medium">MapPin</p>
                        <p class="text-xs text-gray-600">Locations</p>
                      </div>
                      <div class="text-center p-3 border border-gray-200 rounded-lg hover:bg-cta-background-one">
                        <Map class="h-6 w-6 mx-auto mb-2 text-gorilla-primary" />
                        <p class="text-xs font-medium">Map</p>
                        <p class="text-xs text-gray-600">Map view</p>
                      </div>
                      <div class="text-center p-3 border border-gray-200 rounded-lg hover:bg-cta-background-one">
                        <Search class="h-6 w-6 mx-auto mb-2 text-gorilla-primary" />
                        <p class="text-xs font-medium">Search</p>
                        <p class="text-xs text-gray-600">Find places</p>
                      </div>
                      <div class="text-center p-3 border border-gray-200 rounded-lg hover:bg-cta-background-one">
                        <Layers class="h-6 w-6 mx-auto mb-2 text-gorilla-primary" />
                        <p class="text-xs font-medium">Layers</p>
                        <p class="text-xs text-gray-600">Map layers</p>
                      </div>
                      <div class="text-center p-3 border border-gray-200 rounded-lg hover:bg-cta-background-one">
                        <Settings class="h-6 w-6 mx-auto mb-2 text-gorilla-primary" />
                        <p class="text-xs font-medium">Settings</p>
                        <p class="text-xs text-gray-600">Configuration</p>
                      </div>
                      <div class="text-center p-3 border border-gray-200 rounded-lg hover:bg-cta-background-one">
                        <BarChart3 class="h-6 w-6 mx-auto mb-2 text-gorilla-primary" />
                        <p class="text-xs font-medium">BarChart3</p>
                        <p class="text-xs text-gray-600">Analytics</p>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Icon Selection Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Geographic:</strong> Use MapPin for locations, Map for map views, Layers for overlays</p>
                        <p><strong>Actions:</strong> Use Search for finding, Plus for adding, Settings for configuration</p>
                        <p><strong>Data:</strong> Use BarChart3 for analytics, FileText for documents</p>
                        <p><strong>Navigation:</strong> Use ChevronLeft/Right for pagination, Navigation for directions</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Data Visualization Section -->
            <div v-else-if="activeSection === 'data-viz'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Data Visualization Components
                </h2>
                <p class="text-gray-600 mb-8">
                  Components for displaying geographic data, statistics, and analytics
                  that align with our geo mapping platform theme.
                </p>
              </div>

              <!-- Map Components -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Map Components</CardTitle>
                  <CardDescription>
                    Map-related UI elements and controls
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <!-- Map Controls -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Map Controls</h4>
                      <div class="bg-gray-100 rounded-lg p-6 relative">
                        <!-- Simulated Map Background -->
                        <div class="absolute inset-0 bg-cta-background-one rounded-lg"></div>

                        <!-- Zoom Controls -->
                        <div class="absolute top-4 right-4 bg-white rounded-lg border border-gray-200 overflow-hidden">
                          <button class="block w-10 h-10 flex items-center justify-center hover:bg-cta-background-one border-b border-gray-200">
                            <span class="text-gorilla-primary-three font-bold">+</span>
                          </button>
                          <button class="block w-10 h-10 flex items-center justify-center hover:bg-cta-background-one">
                            <span class="text-gorilla-primary-three font-bold">−</span>
                          </button>
                        </div>

                        <!-- Layer Toggle -->
                        <div class="absolute top-4 left-4 bg-white rounded-lg border border-gray-200 p-2">
                          <button class="flex items-center space-x-2 px-3 py-2 rounded-md hover:bg-cta-background-one">
                            <Layers class="h-4 w-4" />
                            <span class="text-sm text-gorilla-primary-three">Layers</span>
                          </button>
                        </div>

                        <!-- Location Marker -->
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                          <div class="w-6 h-6 bg-gorilla-primary rounded-full border-2 border-white"></div>
                        </div>

                        <!-- Scale Bar -->
                        <div class="absolute bottom-4 left-4 bg-white rounded px-2 py-1 text-xs text-gorilla-primary-three border border-gray-200">
                          1 km
                        </div>
                      </div>
                    </div>

                    <!-- Map Legend -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Map Legend</h4>
                      <div class="bg-white border border-gray-200 rounded-lg p-4 w-64">
                        <h5 class="font-medium text-gorilla-primary-three mb-3">Legend</h5>
                        <div class="space-y-2">
                          <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-gorilla-primary rounded-full"></div>
                            <span class="text-sm text-gray-700">Health Facilities</span>
                          </div>
                          <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-gorilla-primary-two rounded-full"></div>
                            <span class="text-sm text-gray-700">Educational Institutions</span>
                          </div>
                          <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-gorilla-primary-three rounded-full"></div>
                            <span class="text-sm text-gray-700">Government Offices</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Usage Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Controls:</strong> Position controls consistently (zoom top-right, layers top-left)</p>
                        <p><strong>Markers:</strong> Use brand colors for different data categories</p>
                        <p><strong>Background:</strong> White backgrounds for controls with subtle borders</p>
                        <p><strong>Legend:</strong> Clear, descriptive labels with color coding</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Chart Components -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Chart Components</CardTitle>
                  <CardDescription>
                    Data visualization charts and graphs
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <!-- Bar Chart -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Population by District</h4>
                      <div class="space-y-3">
                        <div class="flex items-center justify-between">
                          <span class="text-sm text-gray-600 w-24">Gasabo</span>
                          <div class="flex-1 mx-4">
                            <div class="bg-gray-200 rounded-full h-4">
                              <div class="bg-gorilla-primary h-4 rounded-full" style="width: 85%"></div>
                            </div>
                          </div>
                          <span class="text-sm text-gorilla-primary-three font-medium w-16">530,907</span>
                        </div>
                        <div class="flex items-center justify-between">
                          <span class="text-sm text-gray-600 w-24">Nyarugenge</span>
                          <div class="flex-1 mx-4">
                            <div class="bg-gray-200 rounded-full h-4">
                              <div class="bg-gorilla-primary-two h-4 rounded-full" style="width: 60%"></div>
                            </div>
                          </div>
                          <span class="text-sm text-gorilla-primary-three font-medium w-16">284,551</span>
                        </div>
                        <div class="flex items-center justify-between">
                          <span class="text-sm text-gray-600 w-24">Kicukiro</span>
                          <div class="flex-1 mx-4">
                            <div class="bg-gray-200 rounded-full h-4">
                              <div class="bg-gorilla-primary-three h-4 rounded-full" style="width: 50%"></div>
                            </div>
                          </div>
                          <span class="text-sm text-gorilla-primary-three font-medium w-16">318,061</span>
                        </div>
                      </div>
                    </div>

                    <!-- Donut Chart -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Land Use Distribution</h4>
                      <div class="flex items-center space-x-6">
                        <!-- Simulated Donut Chart -->
                        <div class="relative w-32 h-32">
                          <svg class="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                            <path
                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                              fill="none"
                              stroke="#edefeb"
                              stroke-width="3"
                            />
                            <path
                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                              fill="none"
                              stroke="#1A773E"
                              stroke-width="3"
                              stroke-dasharray="60, 100"
                            />
                          </svg>
                          <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-lg font-bold text-gorilla-primary-three">60%</span>
                          </div>
                        </div>
                        <div class="space-y-2">
                          <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-gorilla-primary rounded-full"></div>
                            <span class="text-sm text-gray-700">Agricultural (60%)</span>
                          </div>
                          <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-gorilla-primary-two rounded-full"></div>
                            <span class="text-sm text-gray-700">Urban (25%)</span>
                          </div>
                          <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-gorilla-primary-three rounded-full"></div>
                            <span class="text-sm text-gray-700">Forest (15%)</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Usage Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Colors:</strong> Use brand colors consistently across charts</p>
                        <p><strong>Labels:</strong> Include clear labels and values</p>
                        <p><strong>Legends:</strong> Position legends for easy reference</p>
                        <p><strong>Accessibility:</strong> Ensure sufficient color contrast</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Usage Guidelines Section -->
            <div v-else-if="activeSection === 'guidelines'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Usage Guidelines
                </h2>
                <p class="text-gray-600 mb-8">
                  Comprehensive guidelines for implementing our design system effectively
                  across the OnRwanda Geo platform.
                </p>
              </div>

              <!-- Design Principles -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Design Principles</CardTitle>
                  <CardDescription>
                    Core principles that guide our design decisions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                      <div class="border-l-4 border-gorilla-primary pl-4">
                        <h4 class="font-semibold text-gorilla-primary-three">Rwanda-Inspired</h4>
                        <p class="text-sm text-gray-600">
                          Our color palette reflects Rwanda's flag and natural beauty,
                          creating a strong connection to the local context.
                        </p>
                      </div>
                      <div class="border-l-4 border-gorilla-primary-two pl-4">
                        <h4 class="font-semibold text-gorilla-primary-three">Material 3 Expressive</h4>
                        <p class="text-sm text-gray-600">
                          Following Google's Material 3 expressive design launched in 2025,
                          with no shadows, clean lines, and bold, expressive elements.
                        </p>
                      </div>
                    </div>
                    <div class="space-y-4">
                      <div class="border-l-4 border-gorilla-primary-three pl-4">
                        <h4 class="font-semibold text-gorilla-primary-three">Accessibility First</h4>
                        <p class="text-sm text-gray-600">
                          All components meet WCAG 2.1 AA standards with
                          proper contrast ratios and keyboard navigation.
                        </p>
                      </div>
                      <div class="border-l-4 border-gray-300 pl-4">
                        <h4 class="font-semibold text-gorilla-primary-three">Geo-Focused</h4>
                        <p class="text-sm text-gray-600">
                          Every component is designed with geographic data
                          and mapping functionality in mind.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Do's and Don'ts -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Do's and Don'ts</CardTitle>
                  <CardDescription>
                    Best practices and common mistakes to avoid
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Do's -->
                    <div>
                      <h4 class="font-semibold text-gorilla-primary mb-4 flex items-center">
                        <CheckCircle class="mr-2 h-5 w-5" /> Do's
                      </h4>
                      <div class="space-y-3">
                        <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                          <p class="text-sm text-green-800">Use consistent spacing and typography throughout</p>
                        </div>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                          <p class="text-sm text-green-800">Maintain proper color contrast for accessibility</p>
                        </div>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                          <p class="text-sm text-green-800">Use rounded corners consistently (lg for cards, xl for navigation)</p>
                        </div>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                          <p class="text-sm text-green-800">Include meaningful icons and clear labels</p>
                        </div>
                      </div>
                    </div>

                    <!-- Don'ts -->
                    <div>
                      <h4 class="font-semibold text-red-600 mb-4 flex items-center">
                        <XCircle class="mr-2 h-5 w-5" /> Don'ts
                      </h4>
                      <div class="space-y-3">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                          <p class="text-sm text-red-800">Don't use shadows, drop shadows, or any shadow effects - Material 3 expressive design prohibits shadows</p>
                        </div>
                        <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                          <p class="text-sm text-red-800">Don't use gradients - they look cheap and don't align with our premium aesthetic</p>
                        </div>
                        <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                          <p class="text-sm text-red-800">Don't use colors outside the defined palette</p>
                        </div>
                        <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                          <p class="text-sm text-red-800">Don't use multiple primary buttons in the same view</p>
                        </div>
                        <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                          <p class="text-sm text-red-800">Don't overcrowd interfaces with too many elements</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Implementation Guide -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Implementation Guide</CardTitle>
                  <CardDescription>
                    Technical guidelines for developers
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-3">CSS Classes</h4>
                      <div class="bg-gray-50 rounded-lg p-4 font-mono text-sm">
                        <div class="space-y-1">
                          <div><span class="text-blue-600">/* Primary Colors */</span></div>
                          <div>bg-gorilla-primary: #1A773E</div>
                          <div>bg-gorilla-primary-two: #1C5172</div>
                          <div>bg-gorilla-primary-three: #303017</div>
                          <div class="mt-3"><span class="text-blue-600">/* Background Colors */</span></div>
                          <div>bg-cta-background-one: #edefeb</div>
                          <div>bg-cta-background-two: #fafbfa</div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-3">Component Usage</h4>
                      <div class="bg-gray-50 rounded-lg p-4 text-sm">
                        <div class="space-y-2">
                          <p><strong>Buttons:</strong> Use size variants (sm, default, lg) and appropriate variants (default, outline, ghost)</p>
                          <p><strong>Cards:</strong> Always include proper padding and border styling</p>
                          <p><strong>Forms:</strong> Include labels and proper focus states</p>
                          <p><strong>Navigation:</strong> Use rounded styling for main navigation elements</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Responsive Guidelines -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Responsive Design</CardTitle>
                  <CardDescription>
                    Guidelines for responsive behavior across devices
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-3">Breakpoints</h4>
                      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-cta-background-one p-4 rounded-lg">
                          <h5 class="font-medium text-gorilla-primary-three">Mobile</h5>
                          <p class="text-sm text-gray-600">< 768px</p>
                          <p class="text-xs text-gray-500 mt-1">Single column layout, stacked navigation</p>
                        </div>
                        <div class="bg-cta-background-one p-4 rounded-lg">
                          <h5 class="font-medium text-gorilla-primary-three">Tablet</h5>
                          <p class="text-sm text-gray-600">768px - 1024px</p>
                          <p class="text-xs text-gray-500 mt-1">Two column layout, condensed navigation</p>
                        </div>
                        <div class="bg-cta-background-one p-4 rounded-lg">
                          <h5 class="font-medium text-gorilla-primary-three">Desktop</h5>
                          <p class="text-sm text-gray-600">> 1024px</p>
                          <p class="text-xs text-gray-500 mt-1">Full layout with sidebar navigation</p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-3">Component Behavior</h4>
                      <div class="space-y-3">
                        <div class="border border-gray-200 rounded-lg p-3">
                          <h5 class="font-medium text-gorilla-primary-three text-sm">Cards</h5>
                          <p class="text-xs text-gray-600">Stack vertically on mobile, grid layout on larger screens</p>
                        </div>
                        <div class="border border-gray-200 rounded-lg p-3">
                          <h5 class="font-medium text-gorilla-primary-three text-sm">Navigation</h5>
                          <p class="text-xs text-gray-600">Hamburger menu on mobile, full navigation on desktop</p>
                        </div>
                        <div class="border border-gray-200 rounded-lg p-3">
                          <h5 class="font-medium text-gorilla-primary-three text-sm">Forms</h5>
                          <p class="text-xs text-gray-600">Full width on mobile, constrained width on desktop</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Grid System Section -->
            <div v-else-if="activeSection === 'grid'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Grid System
                </h2>
                <p class="text-gray-600 mb-8">
                  Our responsive grid system ensures consistent layouts across all screen sizes,
                  following Material 3 expressive design principles.
                </p>
              </div>

              <!-- Grid Breakpoints -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Breakpoints & Columns</CardTitle>
                  <CardDescription>
                    Responsive breakpoints with column configurations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <!-- Mobile XS -->
                      <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center mb-3">
                          <Smartphone class="h-5 w-5 text-gorilla-primary mr-2" />
                          <h4 class="font-semibold text-gorilla-primary-three">XS Mobile</h4>
                        </div>
                        <div class="space-y-2 text-sm">
                          <div class="flex justify-between">
                            <span class="text-gray-600">Breakpoint:</span>
                            <span class="font-medium">320px - 479px</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Columns:</span>
                            <span class="font-medium">6</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Margin:</span>
                            <span class="font-medium">20px</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Gutter:</span>
                            <span class="font-medium">12px</span>
                          </div>
                        </div>
                      </div>

                      <!-- Mobile S -->
                      <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center mb-3">
                          <Smartphone class="h-5 w-5 text-gorilla-primary mr-2" />
                          <h4 class="font-semibold text-gorilla-primary-three">S Mobile</h4>
                        </div>
                        <div class="space-y-2 text-sm">
                          <div class="flex justify-between">
                            <span class="text-gray-600">Breakpoint:</span>
                            <span class="font-medium">480px - 767px</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Columns:</span>
                            <span class="font-medium">6</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Margin:</span>
                            <span class="font-medium">32px</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Gutter:</span>
                            <span class="font-medium">12px</span>
                          </div>
                        </div>
                      </div>

                      <!-- Tablet M -->
                      <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center mb-3">
                          <Tablet class="h-5 w-5 text-gorilla-primary-two mr-2" />
                          <h4 class="font-semibold text-gorilla-primary-three">M Tablet</h4>
                        </div>
                        <div class="space-y-2 text-sm">
                          <div class="flex justify-between">
                            <span class="text-gray-600">Breakpoint:</span>
                            <span class="font-medium">768px - 991px</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Columns:</span>
                            <span class="font-medium">12</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Margin:</span>
                            <span class="font-medium">40px</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Gutter:</span>
                            <span class="font-medium">16px</span>
                          </div>
                        </div>
                      </div>

                      <!-- Desktop L -->
                      <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center mb-3">
                          <Monitor class="h-5 w-5 text-gorilla-primary-three mr-2" />
                          <h4 class="font-semibold text-gorilla-primary-three">L Desktop</h4>
                        </div>
                        <div class="space-y-2 text-sm">
                          <div class="flex justify-between">
                            <span class="text-gray-600">Breakpoint:</span>
                            <span class="font-medium">992px - 1199px</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Columns:</span>
                            <span class="font-medium">12</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Margin:</span>
                            <span class="font-medium">80px</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Gutter:</span>
                            <span class="font-medium">28px</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Usage Guidelines -->
                    <div class="bg-cta-background-one p-4 rounded-lg">
                      <h5 class="font-semibold text-gorilla-primary-three mb-2">Grid Usage Guidelines</h5>
                      <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Maximum Width:</strong> 1440px - content centers beyond this width</p>
                        <p><strong>Margins:</strong> Increase with screen size for better readability</p>
                        <p><strong>Gutters:</strong> Consistent spacing between columns</p>
                        <p><strong>Responsive:</strong> Use semantic breakpoints, not device-specific</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Writing & Grammar Section -->
            <div v-else-if="activeSection === 'writing'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                  Writing & Grammar
                </h2>
                <p class="text-gray-600 mb-8">
                  Clear, consistent writing guidelines for OnRwanda Geo platform,
                  inspired by Wise design principles and adapted for geographic content.
                </p>
              </div>

              <!-- Voice & Tone -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Voice & Tone</CardTitle>
                  <CardDescription>
                    How we communicate with users across the platform
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-3">Our Voice</h4>
                      <div class="space-y-3">
                        <div class="border-l-4 border-gorilla-primary pl-3">
                          <h5 class="font-medium text-gorilla-primary-three">Clear & Direct</h5>
                          <p class="text-sm text-gray-600">Use simple language that anyone can understand</p>
                        </div>
                        <div class="border-l-4 border-gorilla-primary-two pl-3">
                          <h5 class="font-medium text-gorilla-primary-three">Helpful & Informative</h5>
                          <p class="text-sm text-gray-600">Provide context and guidance for geographic data</p>
                        </div>
                        <div class="border-l-4 border-gorilla-primary-three pl-3">
                          <h5 class="font-medium text-gorilla-primary-three">Locally Relevant</h5>
                          <p class="text-sm text-gray-600">Respect Rwandan context and terminology</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-3">Tone Guidelines</h4>
                      <div class="space-y-2 text-sm">
                        <div class="bg-green-50 border border-green-200 rounded p-2">
                          <p class="text-green-800"><strong>Do:</strong> Use active voice and present tense</p>
                        </div>
                        <div class="bg-green-50 border border-green-200 rounded p-2">
                          <p class="text-green-800"><strong>Do:</strong> Write in sentence case</p>
                        </div>
                        <div class="bg-red-50 border border-red-200 rounded p-2">
                          <p class="text-red-800"><strong>Don't:</strong> Use technical jargon without explanation</p>
                        </div>
                        <div class="bg-red-50 border border-red-200 rounded p-2">
                          <p class="text-red-800"><strong>Don't:</strong> Use exclamation marks excessively</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <!-- Geographic Terminology -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary">Geographic Terminology</CardTitle>
                  <CardDescription>
                    Consistent terms for Rwanda's administrative divisions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 class="font-semibold text-gorilla-primary-three mb-3">Administrative Levels</h4>
                        <div class="space-y-2 text-sm">
                          <div class="flex justify-between border-b border-gray-100 pb-1">
                            <span class="text-gray-600">Country:</span>
                            <span class="font-medium">Rwanda</span>
                          </div>
                          <div class="flex justify-between border-b border-gray-100 pb-1">
                            <span class="text-gray-600">Level 1:</span>
                            <span class="font-medium">Province</span>
                          </div>
                          <div class="flex justify-between border-b border-gray-100 pb-1">
                            <span class="text-gray-600">Level 2:</span>
                            <span class="font-medium">District</span>
                          </div>
                          <div class="flex justify-between border-b border-gray-100 pb-1">
                            <span class="text-gray-600">Level 3:</span>
                            <span class="font-medium">Sector</span>
                          </div>
                          <div class="flex justify-between border-b border-gray-100 pb-1">
                            <span class="text-gray-600">Level 4:</span>
                            <span class="font-medium">Cell</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Level 5:</span>
                            <span class="font-medium">Village</span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h4 class="font-semibold text-gorilla-primary-three mb-3">Common Terms</h4>
                        <div class="space-y-2 text-sm">
                          <div class="bg-cta-background-one p-2 rounded">
                            <p><strong>Coordinates:</strong> Use decimal degrees format</p>
                            <p class="text-xs text-gray-600">Example: -1.9441, 30.0619</p>
                          </div>
                          <div class="bg-cta-background-one p-2 rounded">
                            <p><strong>Locations:</strong> Always include administrative context</p>
                            <p class="text-xs text-gray-600">Example: Kimisagara, Gasabo District</p>
                          </div>
                          <div class="bg-cta-background-one p-2 rounded">
                            <p><strong>Distances:</strong> Use metric system (km, m)</p>
                            <p class="text-xs text-gray-600">Example: 2.5 km, 150 m</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Placeholder for other sections -->
            <div v-else class="text-center py-16">
              <div class="flex flex-col items-center">
                <div class="w-16 h-16 bg-cta-background-one rounded-full flex items-center justify-center mb-4">
                  <component :is="sections.find(s => s.id === activeSection)?.icon" class="h-8 w-8 text-gorilla-primary" />
                </div>
                <h3 class="text-xl font-semibold text-gorilla-primary-three mb-2">
                  {{ sections.find(s => s.id === activeSection)?.name }} Section
                </h3>
                <p class="text-gray-600">
                  This section is under construction. Coming soon!
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<style scoped>
/* Custom styles for design system */
.font-mono {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>
