<script setup>
import { Head } from '@inertiajs/vue3';
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from 'vue';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';

const props = defineProps({
    provinces: {
        type: Array,
        required: true,
        default: () => [],
    },
    districts: {
        type: Array,
        required: true,
        default: () => [],
    },
});

const mapContainer = ref(null);
const map = ref(null);
const zoom = ref(8);
const center = ref([29.8739, -1.9403]);

// Filter controls
const viewMode = ref('both'); // 'provinces', 'districts', 'both'
const selectedProvince = ref('all');
const selectedDistrict = ref('all');
const selectedDistricts = ref(new Set());

// Analytics data
const selectedProvinceData = ref(null);
const selectedDistrictData = ref(null);
const provinceDistricts = ref([]);

// Computed options for dropdowns
const provinceOptions = computed(() => [
    { value: 'all', label: 'All Provinces' },
    ...props.provinces.map(p => ({ value: p.id.toString(), label: p.name_en || p.name_local || `Province ${p.id}` }))
]);

const districtOptions = computed(() => [
    { value: 'all', label: 'All Districts' },
    ...props.districts.map(d => ({ value: d.id.toString(), label: d.name || `District ${d.id}` }))
]);

const filteredDistricts = computed(() => {
    if (selectedProvince.value === 'all') return props.districts;
    return props.districts.filter(d => d.province_id?.toString() === selectedProvince.value);
});

// Watch for filter changes
watch([viewMode, selectedProvince, selectedDistrict], () => {
    updateMapLayers();
    updateAnalytics();
});

const updateAnalytics = () => {
    // Update selected province data
    if (selectedProvince.value !== 'all') {
        selectedProvinceData.value = props.provinces.find(p => p.id.toString() === selectedProvince.value);
        provinceDistricts.value = props.districts.filter(d => d.province_id?.toString() === selectedProvince.value);
    } else {
        selectedProvinceData.value = null;
        provinceDistricts.value = [];
    }

    // Update selected district data
    if (selectedDistrict.value !== 'all') {
        selectedDistrictData.value = props.districts.find(d => d.id.toString() === selectedDistrict.value);
        // Also set the province if a district is selected
        if (selectedDistrictData.value && selectedDistrictData.value.province_id) {
            selectedProvinceData.value = props.provinces.find(p => p.id.toString() === selectedDistrictData.value.province_id.toString());
        }
    } else {
        selectedDistrictData.value = null;
    }
};

const updateMapLayers = () => {
    if (!map.value) return;

    // Hide/show province layers
    props.provinces.forEach((province) => {
        const fillLayerId = `province-${province.id}`;
        const borderLayerId = `province-${province.id}-border`;
        
        const shouldShow = (viewMode.value === 'provinces' || viewMode.value === 'both') &&
                            (selectedProvince.value === 'all' || selectedProvince.value === province.id.toString());
        
        if (map.value.getLayer(fillLayerId)) {
            map.value.setLayoutProperty(fillLayerId, 'visibility', shouldShow ? 'visible' : 'none');
            map.value.setLayoutProperty(borderLayerId, 'visibility', shouldShow ? 'visible' : 'none');
        }
    });

    // Hide/show district layers
    props.districts.forEach((district) => {
        const fillLayerId = `district-${district.id}`;
        const borderLayerId = `district-${district.id}-border`;
        
        let shouldShow = (viewMode.value === 'districts' || viewMode.value === 'both');
        
        // Filter by province if selected
        if (selectedProvince.value !== 'all') {
            shouldShow = shouldShow && district.province_id?.toString() === selectedProvince.value;
        }
        
        // Filter by specific district if selected
        if (selectedDistrict.value !== 'all') {
            shouldShow = shouldShow && selectedDistrict.value === district.id.toString();
        }
        
        if (map.value.getLayer(fillLayerId)) {
            map.value.setLayoutProperty(fillLayerId, 'visibility', shouldShow ? 'visible' : 'none');
            map.value.setLayoutProperty(borderLayerId, 'visibility', shouldShow ? 'visible' : 'none');
        }
    });
};

const toggleDistrictSelection = (districtId) => {
    const newSelection = new Set(selectedDistricts.value);
    if (newSelection.has(districtId)) {
        newSelection.delete(districtId);
    } else {
        newSelection.add(districtId);
    }
    selectedDistricts.value = newSelection;
    updateMapLayers();
};

const clearAllFilters = () => {
    viewMode.value = 'both';
    selectedProvince.value = 'all';
    selectedDistrict.value = 'all';
    selectedDistricts.value = new Set();
};

/**
 * Calculates the bounding box of a GeoJSON feature.
 * @param {object} geojson - The GeoJSON feature.
 * @returns {Array} - An array representing the bounding box: [minLng, minLat, maxLng, maxLat].
 */
const getBounds = (geojson) => {
    if (!geojson || !geojson.type) {
        console.warn("Invalid GeoJSON object provided for bounds calculation.");
        return null;
    }

    const coordinates = [];

    const processCoordinates = (coords) => {
        if (Array.isArray(coords[0])) {
            // Multi-dimensional array (e.g., MultiPolygon, LineString with multiple points)
            coords.forEach(processCoordinates);
        } else {
            // Single coordinate pair [lng, lat]
            coordinates.push(coords);
        }
    };

    if (geojson.type === 'Feature') {
        if (geojson.geometry && geojson.geometry.coordinates) {
            processCoordinates(geojson.geometry.coordinates);
        }
    } else if (geojson.type === 'FeatureCollection') {
        geojson.features.forEach(feature => {
            if (feature.geometry && feature.geometry.coordinates) {
                processCoordinates(feature.geometry.coordinates);
            }
        });
    } else if (geojson.coordinates) {
        processCoordinates(geojson.coordinates);
    }

    if (coordinates.length === 0) {
        return null;
    }

    let minLng = Infinity;
    let minLat = Infinity;
    let maxLng = -Infinity;
    let maxLat = -Infinity;

    coordinates.flat(Infinity).forEach((coord, index, arr) => {
        if (index % 2 === 0) { // Longitude
            minLng = Math.min(minLng, coord);
            maxLng = Math.max(maxLng, coord);
        } else { // Latitude
            minLat = Math.min(minLat, coord);
            maxLat = Math.max(maxLat, coord);
        }
    });

    return [minLng, minLat, maxLng, maxLat];
};

/**
 * Zooms the map to fit the bounding box of a given GeoJSON feature.
 * @param {object} geojson - The GeoJSON data for the feature to zoom to.
 */
const zoomToFeature = (geojson) => {
    if (!map.value || !geojson) {
        console.warn("Map not initialized or no GeoJSON provided for zoomToFeature.");
        return;
    }

    const bounds = getBounds(geojson);

    if (bounds) {
        map.value.fitBounds(bounds, {
            padding: 50, // Add some padding around the feature
            duration: 1000, // Animation duration in milliseconds
            maxZoom: 10 // Optional: prevent zooming in too close
        });
    } else {
        console.warn("Could not calculate bounds for the given GeoJSON.");
    }
};


onMounted(async () => {
    await nextTick();

    if (!mapContainer.value) {
        console.error('Map container not found');
        return;
    }

    map.value = new maplibregl.Map({
        container: mapContainer.value,
        style: {
            version: 8,
            sources: {
                osm: {
                    type: 'raster',
                    tiles: [
                        'https://a.tile.openstreetmap.org/{z}/{x}/{y}.png',
                        'https://b.tile.openstreetmap.org/{z}/{x}/{y}.png',
                        'https://c.tile.openstreetmap.org/{z}/{x}/{y}.png',
                    ],
                    tileSize: 256,
                    attribution: '© OpenStreetMap contributors',
                },
            },
            layers: [
                {
                    id: 'osm-tiles',
                    type: 'raster',
                    source: 'osm',
                    minzoom: 0,
                    maxzoom: 19,
                    paint: {
                        'raster-saturation': -1,
                        'raster-contrast': 0.3,
                        'raster-brightness-min': 0.2,
                        'raster-brightness-max': 0.8,
                    }
                },
            ],
        },
        center: center.value,
        zoom: zoom.value,
    });

    map.value.addControl(new maplibregl.NavigationControl({
        showCompass: false,
        showZoom: true,
        visualizePitch: false
    }), 'top-right');

    map.value.on('load', () => {
        // Add Districts
        if (props.districts && props.districts.length > 0) {
            props.districts.forEach((district) => {
                if (!district.id || !district.geojson) {
                    console.warn(`Invalid district data: ${JSON.stringify(district)}`);
                    return;
                }

                const sourceId = `district-${district.id}`;
                const fillLayerId = `district-${district.id}`;
                const borderLayerId = `district-${district.id}-border`;

                map.value.addSource(sourceId, {
                    type: 'geojson',
                    data: district.geojson,
                });

                map.value.addLayer({
                    id: fillLayerId,
                    type: 'fill',
                    source: sourceId,
                    paint: {
                        'fill-color': '#22c55e', // green-500
                        'fill-opacity': 0.4,
                    },
                    layout: {},
                });

                map.value.addLayer({
                    id: borderLayerId,
                    type: 'line',
                    source: sourceId,
                    paint: {
                        'line-color': '#16a34a', // green-600
                        'line-width': 1.5,
                    },
                });

                // District interactions
                map.value.on('mouseenter', fillLayerId, () => {
                    map.value.getCanvas().style.cursor = 'pointer';
                    map.value.setPaintProperty(fillLayerId, 'fill-opacity', 0.6);
                    map.value.setPaintProperty(borderLayerId, 'line-width', 2);
                });

                map.value.on('mouseleave', fillLayerId, () => {
                    map.value.getCanvas().style.cursor = '';
                    map.value.setPaintProperty(fillLayerId, 'fill-opacity', 0.3);
                    map.value.setPaintProperty(borderLayerId, 'line-width', 1);
                });

                map.value.on('click', fillLayerId, (e) => {
                    const popupContent = `
                        <div class="popup-content">
                            <h3 class="popup-title">${district.name || 'Unknown District'}</h3>
                            
                        </div>
                    `;
                    new maplibregl.Popup({
                        className: 'custom-popup'
                    })
                        .setLngLat(e.lngLat)
                        .setHTML(popupContent)
                        .addTo(map.value);
                    
                    // Zoom to the clicked district
                    zoomToFeature(district.geojson);
                });
            });
        }

        // Add Provinces
        if (props.provinces && props.provinces.length > 0) {
            props.provinces.forEach((province) => {
                if (!province.id || !province.geojson) {
                    console.warn(`Invalid province data: ${JSON.stringify(province)}`);
                    return;
                }

                const sourceId = `province-${province.id}`;
                const fillLayerId = `province-${province.id}`;
                const borderLayerId = `province-${province.id}-border`;

                map.value.addSource(sourceId, {
                    type: 'geojson',
                    data: province.geojson,
                });

                map.value.addLayer({
                    id: fillLayerId,
                    type: 'fill',
                    source: sourceId,
                    paint: {
                        'fill-color': '#3b82f6', // blue-500
                        'fill-opacity': 0.3,
                    },
                    layout: {},
                });

                map.value.addLayer({
                    id: borderLayerId,
                    type: 'line',
                    source: sourceId,
                    paint: {
                        'line-color': '#2563eb', // blue-600
                        'line-width': 2,
                    },
                });

                // Province interactions
                map.value.on('mouseenter', fillLayerId, () => {
                    map.value.getCanvas().style.cursor = 'pointer';
                    map.value.setPaintProperty(fillLayerId, 'fill-opacity', 0.4);
                    map.value.setPaintProperty(borderLayerId, 'line-width', 3);
                });

                map.value.on('mouseleave', fillLayerId, () => {
                    map.value.getCanvas().style.cursor = '';
                    map.value.setPaintProperty(fillLayerId, 'fill-opacity', 0.2);
                    map.value.setPaintProperty(borderLayerId, 'line-width', 2);
                });

                map.value.on('click', fillLayerId, (e) => {
                    const popupContent = `
                        <div class="popup-content">
                            <h3 class="popup-title">${province.name_en || province.name_local || 'Unknown Province'}</h3>
                            <div class="popup-details">
                                <p><span class="popup-label">Local Name:</span> ${province.name_local || 'N/A'}</p>
                            </div>
                        </div>
                    `;
                    new maplibregl.Popup({
                        className: 'custom-popup'
                    })
                        .setLngLat(e.lngLat)
                        .setHTML(popupContent)
                        .addTo(map.value);
                    
                    // Zoom to the clicked province
                    zoomToFeature(province.geojson);
                });
            });
        }

        setTimeout(() => {
            map.value.resize();
            updateMapLayers();
            updateAnalytics();
        }, 100);
    });

    window.addEventListener('resize', () => {
        if (map.value) {
            map.value.resize();
        }
    });
});

onUnmounted(() => {
    if (map.value) {
        map.value.remove();
    }
});
</script>

<template>
    <Head title="Rwanda Geographic Explorer" />
    
    <div class="min-h-screen bg-white">
        <div class="border-b border-gray-200 bg-white">
            <div class="mx-auto max-w-7xl px-4 py-6">
                <h1 class="text-3xl font-bold tracking-tight text-gray-900">Rwanda Geographic Explorer</h1>
                <p class="mt-2 text-sm text-gray-600">Interactive map with provinces and districts filtering</p>
            </div>
        </div>

        <div class="bg-gray-50 border-b border-gray-200">
            <div class="mx-auto max-w-7xl px-4 py-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">View Mode</label>
                        <select 
                            v-model="viewMode"
                            class="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm  focus:border-gray-900 focus:outline-none focus:ring-1 focus:ring-gray-900"
                        >
                            <option value="both">Provinces & Districts</option>
                            <option value="provinces">Provinces Only</option>
                            <option value="districts">Districts Only</option>
                        </select>
                    </div>

                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">Filter by Province</label>
                        <select 
                            v-model="selectedProvince"
                            class="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm  focus:border-gray-900 focus:outline-none focus:ring-1 focus:ring-gray-900"
                        >
                            <option v-for="option in provinceOptions" :key="option.value" :value="option.value">
                                {{ option.label }}
                            </option>
                        </select>
                    </div>

                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">Filter by District</label>
                        <select 
                            v-model="selectedDistrict"
                            class="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm  focus:border-gray-900 focus:outline-none focus:ring-1 focus:ring-gray-900"
                        >
                            <option v-for="option in districtOptions" :key="option.value" :value="option.value">
                                {{ option.label }}
                            </option>
                        </select>
                    </div>

                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">Actions</label>
                        <button 
                            @click="clearAllFilters"
                            class="w-full rounded-md bg-gray-900 px-3 py-2 text-sm font-medium text-white  hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-900 focus:ring-offset-2 transition-colors"
                        >
                            Clear All Filters
                        </button>
                    </div>
                </div>

                <div class="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div class="rounded-lg bg-white border border-gray-200 p-4">
                        <div class="text-2xl font-bold text-gray-900">{{ provinces.length }}</div>
                        <div class="text-sm text-gray-600">Total Provinces</div>
                    </div>
                    <div class="rounded-lg bg-white border border-gray-200 p-4">
                        <div class="text-2xl font-bold text-gray-900">{{ districts.length }}</div>
                        <div class="text-sm text-gray-600">Total Districts</div>
                    </div>
                    <div class="rounded-lg bg-white border border-gray-200 p-4">
                        <div class="text-2xl font-bold text-gray-900">{{ filteredDistricts.length }}</div>
                        <div class="text-sm text-gray-600">Filtered Districts</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mx-auto max-w-7xl px-4 py-6">
            <div class="rounded-lg border border-gray-200 overflow-hidden ">
                <div ref="mapContainer" class="map-container"></div>
            </div>
        </div>

        <div class="mx-auto max-w-7xl px-4 pb-6">
            <div v-if="selectedProvinceData || selectedDistrictData" class="space-y-4">
                <div v-if="selectedProvinceData" class="rounded-lg bg-blue-50 border border-blue-200 p-6">
                    <div class="flex items-center gap-3 mb-4">
                        <div class="w-4 h-4 bg-blue-500 rounded"></div>
                        <h3 class="text-lg font-semibold text-blue-900">Province Details</h3>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="bg-white rounded-lg p-4 border border-blue-200">
                            <div class="text-2xl font-bold text-blue-900">{{ selectedProvinceData.name_en || selectedProvinceData.name_local || 'Unknown' }}</div>
                            <div class="text-sm text-blue-600">Province Name</div>
                        </div>
                        <div class="bg-white rounded-lg p-4 border border-blue-200">
                            <div class="text-2xl font-bold text-blue-900">{{ selectedProvinceData.code || 'N/A' }}</div>
                            <div class="text-sm text-blue-600">Province Code</div>
                        </div>
                        <div class="bg-white rounded-lg p-4 border border-blue-200">
                            <div class="text-2xl font-bold text-blue-900">{{ provinceDistricts.length }}</div>
                            <div class="text-sm text-blue-600">Districts</div>
                        </div>
                    </div>

                    <div v-if="provinceDistricts.length > 0" class="mt-6">
                        <h4 class="text-md font-medium text-blue-900 mb-3">Districts in {{ selectedProvinceData.name_en || selectedProvinceData.name_local }}</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            <div 
                                v-for="district in provinceDistricts" 
                                :key="district.id"
                                class="bg-green-50 border border-green-200 rounded-lg p-3 hover:bg-green-100 transition-colors cursor-pointer"
                                @click="selectedDistrict = district.id.toString(); updateAnalytics()"
                            >
                                <div class="flex items-center gap-2">
                                    <div class="w-3 h-3 bg-green-500 rounded"></div>
                                    <div class="font-medium text-green-900">{{ district.name || `District ${district.id}` }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div v-else class="rounded-lg bg-gray-50 border border-gray-200 p-8 text-center">
                <div class="text-gray-500 mb-2">
                    <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Select a Province or District</h3>
                <p class="text-gray-600">Choose a province or district from the filters above to view detailed analytics and information.</p>
            </div>
        </div>
    </div>
</template>

<style>
@import "maplibre-gl/dist/maplibre-gl.css";

.map-container {
    height: 600px;
    width: 100%;
    position: relative;
    overflow: hidden;
    background-color: #f9fafb;
}

/* Custom popup styles */
.custom-popup .maplibregl-popup-content {
    background-color: white;
    border-radius: 8px;
    padding: 0;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    max-width: 280px;
}

.custom-popup .maplibregl-popup-tip {
    border-top-color: white;
}

.popup-content {
    padding: 16px;
}

.popup-title {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 12px 0;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 8px;
}

.popup-details p {
    margin: 6px 0;
    font-size: 14px;
    color: #374151;
    line-height: 1.4;
}

.popup-label {
    font-weight: 500;
    color: #111827;
}

/* Navigation control styling */
.maplibregl-ctrl-group {
    background: white;
    border-radius: 6px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.maplibregl-ctrl-group button {
    background-color: white;
    border: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #374151;
    transition: all 0.15s ease;
}

.maplibregl-ctrl-group button:hover {
    background-color: #f9fafb;
    color: #111827;
}

.maplibregl-ctrl-group button:not(:last-child) {
    border-bottom: 1px solid #e5e7eb;
}
</style>