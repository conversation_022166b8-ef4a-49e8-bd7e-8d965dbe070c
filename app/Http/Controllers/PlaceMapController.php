<?php

namespace App\Http\Controllers;

use App\Data\GeoFancingData;
use App\Data\GpsDataDTO;
use App\Data\PlaceMapData;
use App\Data\PlaceMapItemData;
use App\Enums\MapTypeEnums;
use App\Http\Requests\GeoFancingRequest;
use App\Http\Requests\GpsDataRequest;
use App\Http\Requests\PlaceMapItemRequest;
use App\Http\Requests\PlaceMapRequest;
use App\Services\PlaceMapService;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class PlaceMapController extends Controller
{

    protected $user;
    public function __construct(private PlaceMapService $service)
    {
        $this->user = Auth::user();
    }

    public function sharedMap(string $mapKey)
    {
        return Inertia::render('PlaceMap/SharedMap', [
            'mapKey' => $mapKey,
        ]);
    }
    public function sharedMapData(string $mapKey)
    {

        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $searchQuery = \request()->query('searchQuery', null);
        $placeItems =  $this->service->getSharedMapPlaces($mapKey, perPage:(int) $perPage, page:(int) $page, searchQuery: $searchQuery);
        $mapData = $this->service->getSharedMap($mapKey);
        return response()->json([
            'mapData' => $mapData,
            'placeItems' => $this->pagination($placeItems)
        ]);
        
    }

    public function myMap()
    {
        $placeMapsType = MapTypeEnums::cases();
        return Inertia::render('PlaceMap/Index', ['placeMapTypes' => $placeMapsType]);
    }

    public function myMapItems(int $placeMapId)
    {
        return Inertia::render('PlaceMap/MapItems', [
            'placeMapId' => $placeMapId,
            'geoFancingfields' => config('geo-data.geo-fancing')
        ]);
    }

    public function index()
    {

        $searchQuery = \request()->query('searchQuery', null);
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);

        $result =  $this->service->getMapPlace($this->user->id, perPage: $perPage, page: $page, searchQuery: $searchQuery);
        return  $this->pagination($result);
    }

    public function store(PlaceMapRequest $request)
    {

        $data = PlaceMapData::from($request->validated());
        $this->service->createPlace($this->user->id, $data);
        return response()->json([
            'timestamp' => now(),
            'message' => 'Place map created successfully'
        ]);
    }

    public function update(PlaceMapRequest $request, int $placeMapId)
    {
        $data = PlaceMapData::from($request->validated());
        $this->service->updatePlace($this->user->id, $placeMapId, $data);
        return response()->json([
            'timestamp' => now(),
            'message' => 'Place map updated successfully'
        ]);
    }

    public function getPlaceMapById(int $placeMapId)
    {
        return $this->service->getPlaceMapById($this->user->id, $placeMapId);
    }

    public function createPlaceItem(int $placeMapId, PlaceMapItemRequest $request)
    {

        $data = PlaceMapItemData::from($request->validated());
        $this->service->createPlaceMapItem($this->user->id, $placeMapId, $data);
        return response()->json([
            'timestamp' => now(),
            'message' => 'Place map item created successfully'
        ]);
    }

    public function updatePlaceItem(int $placeMapId, int $placeMapItemId, PlaceMapItemRequest $request)
    {
        $data = PlaceMapItemData::from($request->validated());
        $this->service->updatePlaceMapItem($this->user->id, $placeMapId, $placeMapItemId, $data);
        return response()->json([
            'timestamp' => now(),
            'message' => 'Place map item updated successfully'
        ]);
    }

    public function getPlaceMapItemById(int $placeMapId, int $placeMapItemId)
    {

        return $this->service->getPlaceMapItemById($this->user->id, $placeMapId, $placeMapItemId);
    }

    public function getPlaceMapItem(int $placeMapId)
    {

        $searchQuery = \request()->query('searchQuery', null);
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);

        $result =  $this->service->getPlaceMapItem($this->user->id, $placeMapId, perPage: (int) $perPage, page:(int) $page, searchQuery: $searchQuery);
        return  $this->pagination($result);
    }

    public function updateGeoFancing(GeoFancingRequest $request)
    {

        $data = GeoFancingData::from($request->validated());

        $this->service->updateGeoFancing($this->user->id, $data);
        return response()->json([
            'timestamp' => now(),
            'message' => 'Geo-fencing updated successfully'
        ]);
    }

    public function storeGpsData(GpsDataRequest $request)
    {

        $validated = $request->validated();
        $data = GpsDataDTO::from($validated);
        $this->service->storeGpsData($data);
        return response()->json([
            'timestamp' => now(),
            'message' => 'Gps data stored successfully'
        ]);
    }

    public function getNotifications()
    {

        $data = $this->service->getNotifications($this->user->id);
        return response()->json($data);
    }
}
