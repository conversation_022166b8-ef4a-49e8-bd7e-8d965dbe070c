<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GpsDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'key' => 'required|string|max:255|min:4',
            'latitude' => 'required|numeric|min:-90|max:90',
            'longitude' => 'required|numeric|min:-180|max:180',
            'timestamp' => 'nullable|date_format:Y-m-d H:i:s',
            'details' => 'nullable|string|max:255|min:4',
        ];
    }
}
