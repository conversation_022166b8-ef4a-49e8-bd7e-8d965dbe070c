<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GeoFancingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'placeMapId' => 'required|numeric|min:1',
            'placeMapItemId' => 'required|numeric|min:1',
            'apiKey' => 'required|string|in:yes,no',
            'webHookStatus' => 'required|string|in:yes,no',
            'webHookUrl' => 'nullable|required_if:webHookStatus,yes|url:http,https',
            'geoFancing' => 'required|string|in:yes,no',
            'geoFancingData' => 'nullable|required_if:geoFancing,yes|array',
            'geoFancingData.*.name' => 'required|string|max:80|min:1',
            'geoFancingData.*.value' => 'required|string|max:255|min:1',
        ];
    }
}
