<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CustomDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'dataMapID' => 'required|numeric|min:1',
            'dataMapItemID' => 'required|numeric|min:1',
            'objectID' => 'nullable|string|max:255|min:1',
            'dataItems' => 'required|array',
            'dataItems.*.name' => 'required|string|max:60|min:4',
            'dataItems.*.value' => 'required|string|max:255|min:1',
        ];
    }
}
