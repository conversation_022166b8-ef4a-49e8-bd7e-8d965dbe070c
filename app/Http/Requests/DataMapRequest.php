<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DataMapRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:80|min:4',
            'image' => 'required|string|in:pin,star,heart,flag,home,work,cafe,park',
            'description' => 'nullable|string|max:255|min:4',
            'visibility' => 'nullable|in:public,private',
            'status' => 'nullable|in:active,inactive',
            'customFields' => 'nullable|array',
            'customFields.*.name' => 'required|string|max:80|min:4',
            'customFields.*.type' => 'required|string|in:text,number,date,subItems',
            'customFields.*.length' => 'nullable|numeric|min:1',
            'customFields.*.isRequired' => 'required|in:yes,no',
        ];
    }
}
