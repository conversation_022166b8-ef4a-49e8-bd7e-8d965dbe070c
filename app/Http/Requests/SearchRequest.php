<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SearchRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }


    public function rules(): array
    {

        return [
            'searchQuery' => 'required|string|max:80|min:4',
            'lang' => 'required|string:min:2|max:2|in:fr,en,rw',
            'filterData' => 'required|in:province,district,sector,cell,village,all,pattern,health_fac',
        ];
    }

    public function messages(): array
    {
        return [
            'searchQuery.required' => 'search query is required',
            'searchQuery.max' => 'search query should not be more than 80 characters long',
            'searchQuery.min' => 'search query should not be less than 4 characters long',
            'lang.required' => 'language is required, check the language code in (fr, en, rw)',
            'lang.in' => 'language should be either fr, en or rw',
            'filterData.required' => 'filter data is required and should be one of the following: province, district, sector, cell, village, all or pattern',
            'filterData.in' => 'filter data should be either province, district, sector, cell, village, all, health_fac or pattern',
        ];
    }
}
