<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PlaceMapItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        return [
            'name' => 'required_without:locationID|string|max:80|min:4',
            'image' => 'nullable|string|in:pin,star,heart,flag,home,work,cafe,park,restaurant,shopping,hospital,school',
            'address' => 'nullable|string|max:255|min:4',
            'locationID' => 'nullable|numeric|min:1',
            'description' => 'nullable|string|max:255|min:4',
            'latitude' => 'nullable|numeric|min:-90|max:90',
            'longitude' => 'nullable|numeric|min:-180|max:180',
            'type' => 'nullable|in:province,district,sector,cell,village,place,movingItem,itinerary',
            'source' => 'nullable|string',
            'visibility' => 'nullable|in:public,private',
            'status' => 'nullable|in:active,inactive',
            'dataItems' => 'nullable|array',
            'dataItems.*.id' => 'nullable|numeric|min:1',
            'dataItems.*.name' => 'required|string|max:60|min:4',
            'dataItems.*.value' => 'required|string|max:255|min:1',
        ];
    }
}
