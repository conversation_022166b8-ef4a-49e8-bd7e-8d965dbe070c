<?php

namespace App\Notifications;

use App\Models\PlaceMapItem;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class GeoFenceNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public PlaceMapItem $placeMapItem,
        public string $triggerType,
        public string $entityName,
        public string $entityType,
        public float $latitude,
        public float $longitude,
        public ?float $distance = null,
        public ?string $details = null
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $subject = "Geofence Alert: {$this->placeMapItem->name}";
        $actionText = $this->getActionText();

        return (new MailMessage)
            ->subject($subject)
            ->line("Your tracked item '{$this->placeMapItem->name}' has {$actionText}.")
            ->line("Location: {$this->entityName} ({$this->entityType})")
            ->line("Coordinates: {$this->latitude}, {$this->longitude}")
            ->when($this->distance, fn($mail) => $mail->line("Distance: " . round($this->distance, 2) . " meters"))
            ->when($this->details, fn($mail) => $mail->line("Details: {$this->details}"))
            ->action('View on Map', url("/map/place/{$this->placeMapItem->place_map_id}"))
            ->line('This is an automated geofencing notification.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'place_map_item_id' => $this->placeMapItem->id,
            'place_map_item_name' => $this->placeMapItem->name,
            'place_map_id' => $this->placeMapItem->place_map_id,
            'trigger_type' => $this->triggerType,
            'entity_name' => $this->entityName,
            'entity_type' => $this->entityType,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'distance' => $this->distance,
            'details' => $this->details,
            'message' => $this->getActionText(),
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Get human-readable action text based on trigger type.
     */
    private function getActionText(): string
    {
        return match ($this->triggerType) {
            'entered' => "entered the geofenced area of {$this->entityName}",
            'exited' => "exited the geofenced area of {$this->entityName}",
            'approaching' => "is approaching {$this->entityName}",
            'moving_away' => "is moving away from {$this->entityName}",
            default => "triggered a geofence alert near {$this->entityName}",
        };
    }
}
