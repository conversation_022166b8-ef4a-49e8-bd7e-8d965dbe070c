<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Lara<PERSON>\Scout\Searchable;

class DataMapItem extends Model
{
    /** @use HasFactory<\Database\Factories\DataMapItemFactory> */
    use HasFactory, Searchable;

    protected $table = 'DataMapItem';

    protected $fillable = [
        'data_map_id',
        'place_map_item_id',
        'name',
        'description',
        'dataItems',
        'type',
        'visibility',
        'details',
        'status',
        'geometry',
        'centroid',
    ];

    protected $casts = [
        'dataItems' => 'array',
        'details' => 'array',
    ];

    public function toSearchableArray(): array
    {

        $array = $this->toArray();
        unset($array['geometry'], $array['centroid']);
        return array_merge($array, [
            'id' => (string) $this->id,
            'name' => $this->name,
            'description' => (string) $this->description,
            'data_map_id' => (string) $this->data_map_id,
        ]);
    }

    public function datamap() : BelongsTo
    {
        return $this->belongsTo(DataMap::class, 'data_map_id');
    }
}
