<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Laravel\Scout\Searchable;


class HealthFac extends Model
{

    use HasFactory, Searchable;

    protected $table = 'HealthFac';

    protected $fillable = [
        'name',
        'type',
        'layer',
        'latitude',
        'longitude',
        'code',
        'capture_year',
        'source',
        'geojson',
        'population',
        'description',
        'latitude',
        'longitude',
        'village_id',
    ];

    protected $casts = [
        'geojson' => 'array',
    ];

    public function toSearchableArray(): array
    {
        return array_merge($this->toArray(), [
            'id' => (string) $this->id,
            'name' => $this->name,
            'code' => (string) $this->code,
            'latitude' => (string) $this->latitude,
            'longitude' => (string) $this->longitude,
        ]);
    }

    public function village() : BelongsTo
    {
        return $this->belongsTo(Village::class);
    }
}
