<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Lara<PERSON>\Scout\Searchable;


class Village extends Model
{

    use HasFactory, Searchable;

    protected $table = 'Village';

    protected $fillable = [
        'cell_id',
        'name',
        'code',
        'capture_year',
        'source',
        'geojson',
        'shape_length',
        'shape_area',
        'population',
        'description',
        'latitude',
        'longitude',
        'geometry',
        'centroid',
    ];

    protected $casts = [
        'geojson' => 'array',
    ];

    public function cell(): BelongsTo
    {
        return $this->belongsTo(Cell::class);
    }

    public function toSearchableArray(): array
    {

        $array = $this->toArray();
        unset($array['geometry'], $array['centroid']);

        return array_merge($array, [
            'id' => (string) $this->id,
            'name' => $this->name,
            'code' => (string) $this->code,
            'latitude' => (string) $this->latitude,
            'longitude' =>  (string) $this->longitude,
        ]);
    }
}
