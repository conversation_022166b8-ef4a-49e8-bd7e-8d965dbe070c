<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Laravel\Scout\Searchable;

class Cell extends Model
{

    use HasFactory, Searchable;

    protected $table = 'Cell';

    protected $fillable = [
        'name',
        'code',
        'capture_year',
        'source',
        'geojson',
        'shape_length',
        'shape_area',
        'population',
        'description',
        'sector_id',
        'latitude',
        'longitude',
        'geometry',
        'centroid',
    ];

    protected $casts = [
        'geojson' => 'array',
    ];

    public function sector(): BelongsTo
    {
        return $this->belongsTo(Sector::class);
    }

    public function village(): HasMany
    {

        return $this->hasMany(Village::class);
    }

    public function toSearchableArray(): array
    {

        $array = $this->toArray();
        unset($array['geometry'], $array['centroid']);

        return array_merge($array, [
            'id' => (string) $this->id,
            'name' => $this->name,
            'code' => (string) $this->code,
            'latitude' => (string) $this->latitude,
            'longitude' =>  (string) $this->longitude,
        ]);
    }
}
