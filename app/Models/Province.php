<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON>vel\Scout\Searchable;

class Province extends Model
{
    /** @use HasFactory<\Database\Factories\ProvinceFactory> */
    use HasFactory, Searchable;

    protected $table = 'Province';

    protected $fillable = [
        'name_en',
        'name_local',
        'code',
        'capture_year',
        'source',
        'geojson',
        'shape_length',
        'shape_area',
        'population',
        'description',
        'latitude',
        'longitude',
        'geometry',
        'centroid',
    ];

    protected $casts = [
        'geojson' => 'array',
    ];

    public function toSearchableArray(): array
    {
        $array = $this->toArray();

        unset($array['geometry'], $array['centroid']);

        return array_merge($array, [
            'id' => (string) $this->id,
            'name_en' => $this->name_en,
            'name_local' => $this->name_local,
            'code' => (string) $this->code,
            'latitude' => (string) $this->latitude,
            'longitude' => (string) $this->longitude,
        ]);
    }
}
