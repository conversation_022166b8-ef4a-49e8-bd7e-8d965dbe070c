<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use <PERSON><PERSON>\Scout\Searchable;

class PlaceMapItem extends Model
{

    use HasFactory, Searchable;

    protected $table = 'PlaceMapItem';

    protected $fillable = [
        'place_map_id',
        'location_id',
        'name',
        'description',
        'image',
        'latitude',
        'longitude',
        'geojson',
        'type',
        'source',
        'visibility',
        'geoFancing',
        'status',
        'address',
        'isTemplate',
        'dataItems',
    ];

    public function toSearchableArray(): array
    {
        return array_merge($this->toArray(), [
            'id' => (string) $this->id,
            'name' => $this->name,
            'description' => (string)$this->description,
            'latitude' => (string) $this->latitude,
            'longitude' =>  (string) $this->longitude,
            'address' => (string) $this->address
        ]);
    }

    protected $casts = [
        'geojson' => 'array',
        'dataItems' => 'array',
        'geoFancing' => 'array',
    ];

    public function placeMap(): BelongsTo
    {
        return $this->belongsTo(PlaceMap::class);
    }
}
