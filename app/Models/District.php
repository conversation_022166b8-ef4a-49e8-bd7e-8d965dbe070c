<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Laravel\Scout\Searchable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class District extends Model
{

    use HasFactory, Searchable;

    protected $table = 'District';

    protected $fillable = [
        'province_id',
        'name',
        'code',
        'capture_year',
        'source',
        'geojson',
        'shape_length',
        'shape_area',
        'population',
        'description',
        'latitude',
        'longitude',
        'geometry',
        'centroid',
    ];

    protected $casts = [
        'geojson' => 'array',
    ];

    public function toSearchableArray(): array
    {
        $array = $this->toArray();

        unset($array['geometry'], $array['centroid']);
        return array_merge($array, [
            'id' => (string) $this->id,
            'name' => $this->name,
            'code' => (string) $this->code,
            'latitude' => (string) $this->latitude,
            'longitude' =>  (string) $this->longitude,
        ]);
    }

    public function province(): BelongsTo
    {
        return $this->belongsTo(Province::class);
    }

    public function sector(): HasMany
    {
        return $this->hasMany(Sector::class);
    }
}
