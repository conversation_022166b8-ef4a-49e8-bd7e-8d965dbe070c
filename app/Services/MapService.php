<?php

namespace App\Services;

use App\Data\NavigationData;
use App\Data\SearchData;
use App\Data\SearchLatLongData;
use App\Enums\LocationTypeEnums;
use App\Models\Cell;
use App\Models\Province;
use App\Models\District;
use App\Models\HealthFac;
use App\Models\Sector;
use App\Models\Village;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Spinen\Geometry\Geometry;

class MapService
{
    protected $geometry;

    /**
     * @param Geometry $geometry
     */
    public function __construct(Geometry $geometry)
    {
        $this->geometry = $geometry;
    }

    public function getDistrictDistance($fromDistrict, $toDistrict, string $unit = 'km', string $lang = 'en'): ?float
    {
        $cacheKey = "district_distance_{$fromDistrict}_{$toDistrict}_{$unit}_{$lang}";
        return Cache::remember($cacheKey, 3600, function () use ($fromDistrict, $toDistrict, $unit, $lang) {
            try {
                $fromQuery = is_numeric($fromDistrict)
                    ? District::where('id', $fromDistrict)
                    : District::where('name', $this->parseLocationName($fromDistrict, $lang, LocationTypeEnums::DISTRICT->value));

                $toQuery = is_numeric($toDistrict)
                    ? District::where('id', $toDistrict)
                    : District::where('name', $this->parseLocationName($toDistrict, $lang, LocationTypeEnums::DISTRICT->value));

                $from = $fromQuery->select('id', 'name', DB::raw('ST_AsText(centroid) as centroid_wkt'))->first();
                $to = $toQuery->select('id', 'name', DB::raw('ST_AsText(centroid) as centroid_wkt'))->first();

                if (!$from || !$to) {
                    Log::warning("District distance calculation failed: " .
                        ($from ? "To district not found: $toDistrict" : "From district not found: $fromDistrict"));
                    return null;
                }

                if ($from->id === $to->id) {
                    return 0.0;
                }
                $distanceMeters = DB::selectOne(
                    'SELECT ST_Distance_Sphere(
                        (SELECT centroid FROM District WHERE id = ?),
                        (SELECT centroid FROM District WHERE id = ?)
                    ) as distance',
                    [$from->id, $to->id]
                )->distance;
                return $this->convertDistance($distanceMeters, $unit);
            } catch (\Throwable $e) {
                Log::error("Error calculating district distance: {$e->getMessage()}", [
                    'from' => $fromDistrict,
                    'to' => $toDistrict,
                    'unit' => $unit,
                    'lang' => $lang
                ]);
                return null;
            }
        });
    }

    /**
     * Calculate distance between two cells
     *
     * @param string|int $fromCell Identifier (ID or name) of the starting cell
     * @param string|int $toCell Identifier (ID or name) of the ending cell
     * @param string $unit Distance unit (km, m, mi)
     * @param string $lang Language for name-based lookup (rw, en, fr)
     * @return float|null Distance in the specified unit, or null if invalid
     */
    public function getCellDistance($fromCell, $toCell, string $unit = 'km', string $lang = 'en'): ?float
    {
        $cacheKey = "cell_distance_{$fromCell}_{$toCell}_{$unit}_{$lang}";
        return Cache::remember($cacheKey, 3600, function () use ($fromCell, $toCell, $unit, $lang) {
            try {
                $fromQuery = is_numeric($fromCell)
                    ? Cell::where('id', $fromCell)
                    : Cell::where('name', $this->parseLocationName($fromCell, $lang, LocationTypeEnums::CELL->value));

                $toQuery = is_numeric($toCell)
                    ? Cell::where('id', $toCell)
                    : Cell::where('name', $this->parseLocationName($toCell, $lang, LocationTypeEnums::CELL->value));

                $from = $fromQuery->select('id', 'name', DB::raw('ST_AsText(centroid) as centroid_wkt'))->first();
                $to = $toQuery->select('id', 'name', DB::raw('ST_AsText(centroid) as centroid_wkt'))->first();

                if (!$from || !$to) {
                    Log::warning("Cell distance calculation failed: " .
                        ($from ? "To cell not found: $toCell" : "From cell not found: $fromCell"));
                    return null;
                }

                if ($from->id === $to->id) {
                    return 0.0;
                }

                $distanceMeters = DB::selectOne(
                    'SELECT ST_Distance_Sphere(
                        (SELECT centroid FROM Cell WHERE id = ?),
                        (SELECT centroid FROM Cell WHERE id = ?)
                    ) as distance',
                    [$from->id, $to->id]
                )->distance;

                return $this->convertDistance($distanceMeters, $unit);
            } catch (\Throwable $e) {
                Log::error("Error calculating cell distance: {$e->getMessage()}", [
                    'from' => $fromCell,
                    'to' => $toCell,
                    'unit' => $unit,
                    'lang' => $lang
                ]);
                return null;
            }
        });
    }

    /**
     * Convert distance from meters to the specified unit
     *
     * @param float $distanceMeters Distance in meters
     * @param string $unit Target unit (km, m, mi)
     * @return float Distance in the specified unit
     */
    protected function convertDistance(float $distanceMeters, string $unit): float
    {
        switch (strtolower($unit)) {
            case 'km':
                return $distanceMeters / 1000;
            case 'mi':
                return $distanceMeters / 1609.344;
            case 'm':
            default:
                return $distanceMeters;
        }
    }

    protected function parseLocationName(string $name, string $lang, string $type): string
    {
        $patterns = [
            'rw' => [
                LocationTypeEnums::DISTRICT->value => '/^Akarere ka\s+/i',
                LocationTypeEnums::SECTOR->value => '/^Umurenge wa\s+/i',
                LocationTypeEnums::CELL->value => '/^Akagari ka\s+/i',
                LocationTypeEnums::VILLAGE->value => '/^Umudugudu wa\s+/i',
            ],
            'en' => [
                LocationTypeEnums::DISTRICT->value => '/\s+District$/i',
                LocationTypeEnums::SECTOR->value => '/\s+Sector$/i',
                LocationTypeEnums::CELL->value => '/\s+Cell$/i',
                LocationTypeEnums::VILLAGE->value => '/\s+Village$/i',
            ],
            'fr' => [
                LocationTypeEnums::DISTRICT->value => '/^District de\s+/i',
                LocationTypeEnums::SECTOR->value => '/^Secteur de\s+/i',
                LocationTypeEnums::CELL->value => '/^Cellule de\s+/i',
                LocationTypeEnums::VILLAGE->value => '/^Village de\s+/i',
            ],
        ];

        return preg_replace($patterns[$lang][$type] ?? '/^/', '', $name);
    }


    public function province()
    {
        return Cache::remember('Provices', 3600, function () {
            return Province::select('id', 'name_en', 'name_local', 'geojson')->get()->map(
                fn($province) => [
                    'id' => $province->id * (56 + $province->id),
                    'name_en' => $province->name_en,
                    'name_local' => $province->name_local,
                    'geojson' => json_decode((string) $province->geojson),
                ]
            );
        });
    }

    public function district()
    {

        return Cache::remember('Districts', 3600, function () {
            return District::select('id', 'name', 'code', 'capture_year', 'source', 'geojson', 'province_id')
                ->get()
                ->map(
                    fn($district) => [
                        'id' => $district->id * (56 + $district->id),
                        'name' => $district->name,
                        'code' => $district->code,
                        'capture_year' => $district->capture_year,
                        'source' => $district->source,
                        'geojson' => json_decode((string) $district->geojson),
                        'province_id' => $district->province_id
                    ]
                );
        });
    }

    public function search(SearchData $data)
    {

        $details = $this->filterSearch($data);

        $returnResult = $this->emptySearchResult();

        if (in_array(LocationTypeEnums::DISTRICT->value, $details['filterData'])) {
            $returnResult['districts'] = $this->getDistrict($details);
        }

        if (in_array(LocationTypeEnums::SECTOR->value, $details['filterData'])) {
            $returnResult['sectors'] = $this->getSector($details);
        }

        if (in_array(LocationTypeEnums::CELL->value, $details['filterData'])) {
            $returnResult['cells'] = $this->getCell($details);
        }

        if (in_array(LocationTypeEnums::VILLAGE->value, $details['filterData'])) {
            $returnResult['villages'] = $this->getVillages($details);
        }

        if (in_array(LocationTypeEnums::HEALTH_FAC->value, $details['filterData'])) {
            $returnResult['healthFacs'] = $this->getHealthFacilities($details);
        }

        return $returnResult;
    }

    public function apiSearch(SearchData $data)
    {

        $details = $this->filterSearch($data);
        $returnResult = $this->emptySearchResult();

        if (in_array(LocationTypeEnums::DISTRICT->value, $details['filterData'])) {
            $returnResult['districts'] = $this->getDistrict($details, isApi: true);
        }

        if (in_array(LocationTypeEnums::SECTOR->value, $details['filterData'])) {
            $returnResult['sectors'] = $this->getSector($details, isApi: true);
        }

        if (in_array(LocationTypeEnums::CELL->value, $details['filterData'])) {
            $returnResult['cells'] = $this->getCell($details, isApi: true);
        }

        if (in_array(LocationTypeEnums::VILLAGE->value, $details['filterData'])) {
            $returnResult['villages'] = $this->getVillages($details, isApi: true);
        }

        if (in_array(LocationTypeEnums::HEALTH_FAC->value, $details['filterData'])) {
            $returnResult['healthFacs'] = $this->getHealthFacilities($details, isApi: true);
        }

        $returnResult['searchQuery'] = $details['searchQuery'];
        return $returnResult;
    }

    public function searchLatitudeLongitude(SearchLatLongData $data)
    {
        
        $latitude = $data->latitude;
        $longitude = $data->longitude;
        $pointWKT = "POINT($longitude $latitude)";
        $cacheKey = "village_point_{$latitude}_{$longitude}";

        try {
            $village = Cache::remember($cacheKey, 3600, function () use ($pointWKT) {
                $village = DB::selectOne(
                    "SELECT 
                    v.id, v.name, v.code, v.latitude, v.longitude, v.geojson,
                    c.id AS cell_id, c.name AS cell_name, 
                    s.id AS sector_id, s.name AS sector_name,
                    d.id AS district_id, d.name AS district_name,
                    p.id AS province_id, p.name_en AS province_name_en, p.name_local AS province_name_local
                FROM Village v
                INNER JOIN Cell c ON v.cell_id = c.id
                INNER JOIN Sector s ON c.sector_id = s.id
                INNER JOIN District d ON s.district_id = d.id
                INNER JOIN Province p ON d.province_id = p.id
                WHERE ST_Contains(v.geometry, ST_GeomFromText(?, 4326))
                LIMIT 1",
                    [$pointWKT]
                );

                if (!$village) {
                    $village = DB::selectOne(
                        "SELECT 
                        v.id, v.name, v.code, v.latitude, v.longitude, v.geojson,
                        c.id AS cell_id, c.name AS cell_name, 
                        s.id AS sector_id, s.name AS sector_name,
                        d.id AS district_id, d.name AS district_name,
                        p.id AS province_id, p.name_en AS province_name_en, p.name_local AS province_name_local
                    FROM Village v
                    INNER JOIN Cell c ON v.cell_id = c.id
                    INNER JOIN Sector s ON c.sector_id = s.id
                    INNER JOIN District d ON s.district_id = d.id
                    INNER JOIN Province p ON d.province_id = p.id
                    WHERE ST_Distance_Sphere(v.centroid, ST_GeomFromText(?, 4326)) <= 1000
                    ORDER BY ST_Distance_Sphere(v.centroid, ST_GeomFromText(?, 4326)) ASC
                    LIMIT 1",
                        [$pointWKT, $pointWKT]
                    );
                }

                return $village;
            });

            if (!$village) {
                return collect();
            }

            return collect([
                [
                    'id' => $village->id,
                    'name' => $village->name,
                    'code' => $village->code,
                    'latitude' => $village->latitude,
                    'longitude' => $village->longitude,
                    'geojson' => $village->geojson,
                    'address' => "{$village->province_name_en}, {$village->district_name}, {$village->sector_name}, {$village->cell_name}, Umudugudu wa {$village->name}"
                ]
            ]);
        } catch (\Throwable $e) {
            Log::error("Error in searchLatitudeLongitude: {$e->getMessage()}", [
                'latitude' => $latitude,
                'longitude' => $longitude
            ]);
            return collect();
        }
    }

    protected function formatLocationResult($location, string $type, array $details): array
    {
        $result = [
            'id' => $location->id,
            'code' => $location->code,
            'latitude' => $location->latitude,
            'longitude' => $location->longitude,
            'geojson' => $location->geojson,
        ];

        switch ($type) {
            case 'village':
                $result['name'] = $this->formatLang($location->name, $details['lang'], LocationTypeEnums::VILLAGE->value);
                // Load relationships if not already loaded
                if (!$location->relationLoaded('cell')) {
                    $location->load(['cell.sector.district.province']);
                }
                $result['address'] = $this->buildVillageAddress($location);
                break;

            case 'cell':
                $result['name'] = $this->formatLang($location->name, $details['lang'], LocationTypeEnums::CELL->value);
                // Load relationships if not already loaded
                if (!$location->relationLoaded('sector')) {
                    $location->load(['sector.district.province']);
                }
                $result['address'] = $this->buildCellAddress($location);
                break;

            case 'sector':
                $result['name'] = $this->formatLang($location->name, $details['lang'], LocationTypeEnums::SECTOR->value);
                // Load relationships if not already loaded
                if (!$location->relationLoaded('district')) {
                    $location->load(['district.province']);
                }
                $result['address'] = $this->buildSectorAddress($location);
                break;

            case 'district':
                $result['name'] = $this->formatLang($location->name, $details['lang'], LocationTypeEnums::DISTRICT->value);
                // Load relationships if not already loaded
                if (!$location->relationLoaded('province')) {
                    $location->load(['province']);
                }
                $result['address'] = $location->province->name_en;
                break;
        }

        return $result;
    }

    /**
     * Build address strings for different location types
     */
    protected function buildVillageAddress($village): string
    {
        return $village->cell->sector->district->province->name_en . ', ' .
            $village->cell->sector->district->name . ', ' .
            $village->cell->sector->name . ', ' .
            $village->cell->name;
    }

    protected function buildCellAddress($cell): string
    {
        return $cell->sector->district->province->name_en . ', ' .
            $cell->sector->district->name . ', ' .
            $cell->sector->name;
    }

    protected function buildSectorAddress($sector): string
    {
        return $sector->district->province->name_en . ', ' .
            $sector->district->name;
    }

    public function emptySearchResult(): array
    {
        return [
            'searchQuery' => null,
            'provinces' => [],
            'districts' => [],
            'sectors' => [],
            'cells' => [],
            'villages' => [],
            'healthFacs' => [],

        ];
    }

    public function filterSearch(SearchData $data)
    {

        if ($data->filterData == LocationTypeEnums::DISTRICT->value) {

            return [
                'lang' => $data->lang,
                'searchQuery' => $data->searchQuery,
                'filterData' => [
                    LocationTypeEnums::DISTRICT->value
                ]
            ];
        } elseif ($data->filterData == LocationTypeEnums::SECTOR->value) {

            return [
                'lang' => $data->lang,
                'searchQuery' => $data->searchQuery,
                'filterData' => [
                    LocationTypeEnums::SECTOR->value
                ]
            ];
        } elseif ($data->filterData == LocationTypeEnums::CELL->value) {

            return [
                'lang' => $data->lang,
                'searchQuery' => $data->searchQuery,
                'filterData' => [
                    LocationTypeEnums::CELL->value
                ]
            ];
        } elseif ($data->filterData == LocationTypeEnums::VILLAGE->value) {

            return [
                'lang' => $data->lang,
                'searchQuery' => $data->searchQuery,
                'filterData' => [
                    LocationTypeEnums::VILLAGE->value
                ]
            ];
        } elseif ($data->filterData == LocationTypeEnums::HEALTH_FAC->value) {

            return [
                'lang' => $data->lang,
                'searchQuery' => $data->searchQuery,
                'filterData' => [
                    LocationTypeEnums::HEALTH_FAC->value
                ]
            ];
        } elseif ($data->filterData == 'all') {

            return [
                'lang' => $data->lang,
                'searchQuery' => $data->searchQuery,
                'filterData' => [
                    LocationTypeEnums::DISTRICT->value,
                    LocationTypeEnums::SECTOR->value,
                    LocationTypeEnums::CELL->value,
                    LocationTypeEnums::VILLAGE->value,
                    LocationTypeEnums::HEALTH_FAC->value
                ]
            ];
        } else {
            return $this->searchPatterLogic($data);
        }
    }

    public function searchPatterLogic(SearchData $data)
    {

        $masterPatternMap = [
            'Akarere ka'    => LocationTypeEnums::DISTRICT->value,
            'Umurenge wa'   => LocationTypeEnums::SECTOR->value,
            'Akagari ka'    => LocationTypeEnums::CELL->value,
            'Umudugudu wa'  => LocationTypeEnums::VILLAGE->value,
            'Akarere'       => LocationTypeEnums::DISTRICT->value,
            'Umurenge'      => LocationTypeEnums::SECTOR->value,
            'Akagari'       => LocationTypeEnums::CELL->value,
            'Umudugudu'     => LocationTypeEnums::VILLAGE->value,
            'District of'   => LocationTypeEnums::DISTRICT->value,
            'Sector of'     => LocationTypeEnums::SECTOR->value,
            'Cell of'       => LocationTypeEnums::CELL->value,
            'Village of'    => LocationTypeEnums::VILLAGE->value,
            'District de'   => LocationTypeEnums::DISTRICT->value,
            'Secteur de'    => LocationTypeEnums::SECTOR->value,
            'Cellule de'    => LocationTypeEnums::CELL->value,
            'Village de'    => LocationTypeEnums::VILLAGE->value,
            'District'      => LocationTypeEnums::DISTRICT->value,
            'Secteur'       => LocationTypeEnums::SECTOR->value,
            'Cellule'       => LocationTypeEnums::CELL->value,
            'Cell'          => LocationTypeEnums::CELL->value,
            'Village'       => LocationTypeEnums::VILLAGE->value,
            'Ibitaro'       => LocationTypeEnums::HEALTH_FAC->value,
            'Ibitaro bya'   => LocationTypeEnums::HEALTH_FAC->value,
            'Ivuriro'       => LocationTypeEnums::HEALTH_FAC->value,
            'Ivuriro rya'   => LocationTypeEnums::HEALTH_FAC->value,
            'Centre de sante' => LocationTypeEnums::HEALTH_FAC->value,
            'Hospital'      => LocationTypeEnums::HEALTH_FAC->value,
            'Hospital de'   => LocationTypeEnums::HEALTH_FAC->value,
            'Centre de sante de' => LocationTypeEnums::HEALTH_FAC->value,

        ];

        uksort($masterPatternMap, fn($a, $b) => strlen($b) <=> strlen($a));

        $query = trim($data->searchQuery);
        Log::info('querry: ' . $query);
        $lang = $data->lang;

        foreach ($masterPatternMap as $pattern => $type) {

            if (stripos($query, $pattern) === 0) {

                $searchQuery = trim(substr($query, strlen($pattern)));

                Log::info('searchQuery: ' . $searchQuery);
                Log::info('type: ' . $type);
                return [
                    'lang'        => $lang,
                    'searchQuery' => $searchQuery,
                    'filterData'  => [$type]
                ];
            }
        }


        $cleanedQuery = $query;
        $searchTerms = explode(',', $query);
        $termCount = count($searchTerms);

        if ($termCount > 1) {
            $hierarchy = [
                LocationTypeEnums::DISTRICT->value,
                LocationTypeEnums::SECTOR->value,
                LocationTypeEnums::CELL->value,
                LocationTypeEnums::VILLAGE->value,
            ];
            $filterData = array_slice($hierarchy, 0, $termCount);
        } else {
            $filterData = [
                LocationTypeEnums::DISTRICT->value,
                LocationTypeEnums::SECTOR->value,
                LocationTypeEnums::CELL->value,
                LocationTypeEnums::VILLAGE->value,
            ];
        }

        Log::info('searchQuery: ' . $cleanedQuery);
        Log::info('filterData: ' . json_encode($filterData));
        return [
            'lang'        => $lang,
            'searchQuery' => $cleanedQuery,
            'filterData'  => $filterData,
        ];
    }

    public function getProvince($data, $isApi = false)
    {
        return   Province::search($data['searchQuery'])
            ->query(function ($query) {
                $query->select('id', 'name_en', 'name_local', 'code', 'geojson', 'latitude', 'longitude');
            })
            ->get()
            ->map(function ($province) use ($isApi) {
                $dataToRetrun =  [
                    'id' => $province->id,
                    'code' => $province->code,
                    'name_en' => $province->name_en,
                    'name_local' => $province->name_local,
                    'geojson' => $province->geojson,
                    'latitude' => $province->latitude,
                    'longitude' => $province->longitude
                ];

                if ($isApi) {
                 //   unset($dataToRetrun['latitude']);
                //    unset($dataToRetrun['longitude']);
                    unset($dataToRetrun['geojson']);
                    unset($dataToRetrun['code']);
                  //  unset($dataToRetrun['id']);
                }
                return $dataToRetrun;
            });
    }

    public function getDistrict($data, $isApi = false)
    {
        return District::search($data['searchQuery'])
            ->query(function ($query) {
                $query->with(['province:id,name_en,name_local'])
                    ->select('id', 'name', 'code', 'province_id', 'geojson', 'latitude', 'longitude')
                    ->limit(4);
            })->get()
            ->map(function ($district) use ($data, $isApi) {
                $dataToRetrun =  [
                    'id' => $district->id,
                    'code' => $district->code,
                    'name' => $this->formatLang($district->name, $data['lang'], LocationTypeEnums::DISTRICT->value),
                    'address' => $district->province->name_en,
                    'geojson' => $district->geojson,
                    'latitude' => $district->latitude,
                    'longitude' => $district->longitude
                ];

                if ($isApi) {
                    unset($dataToRetrun['geojson']);
                  //  unset($dataToRetrun['id']);
                    unset($dataToRetrun['code']);
              //      unset($dataToRetrun['latitude']);
                 //   unset($dataToRetrun['longitude']);
                }
                return $dataToRetrun;
            });
    }

    public function getSector($data, $isApi = false)
    {
        return Sector::search($data['searchQuery'])
            ->query(function ($query) {
                $query->select('id', 'name', 'code', 'district_id', 'geojson', 'latitude', 'longitude')
                    ->with(['district:id,name,province_id', 'district.province:id,name_en,name_local'])
                    ->limit(10);
            })->get()
            ->map(function ($sector) use ($data, $isApi) {
                $dataToRetrun =  [
                    'id' => $sector->id,
                    'code' => $sector->code,
                    'name' => $this->formatLang($sector->name, $data['lang'], LocationTypeEnums::SECTOR->value),
                    'address' => $sector->district->province->name_en . ', ' . $sector->district->name,
                    'geojson' => $sector->geojson,
                    'latitude' => $sector->latitude,
                    'longitude' => $sector->longitude
                ];

                if ($isApi) {
                    unset($dataToRetrun['geojson']);
                 //   unset($dataToRetrun['id']);
                    unset($dataToRetrun['code']);
                 //   unset($dataToRetrun['latitude']);
                  //  unset($dataToRetrun['longitude']);
                }

                return $dataToRetrun;
            });
    }

    public function getCell($data, $isApi = false)
    {
        return  Cell::search($data['searchQuery'])
            ->query(function ($query) {
                $query->select('id', 'name', 'code', 'sector_id', 'geojson', 'latitude', 'longitude')
                    ->with(['sector:id,name,district_id', 'sector.district:id,name,province_id', 'sector.district.province:id,name_en,name_local'])
                    ->limit(10);
            })->get()
            ->map(function ($cell) use ($data, $isApi) {
                $dataToRetrun =  [
                    'id' => $cell->id,
                    'code' => $cell->code,
                    'name' => $this->formatLang($cell->name, $data['lang'], LocationTypeEnums::CELL->value),
                    'address' => $cell->sector->district->province->name_en . ', ' . $cell->sector->district->name . ', ' . $cell->sector->name,
                    'geojson' => $cell->geojson,
                    'latitude' => $cell->latitude,
                    'longitude' => $cell->longitude
                ];

                if ($isApi) {
                    unset($dataToRetrun['geojson']);
                  //  unset($dataToRetrun['id']);
                    unset($dataToRetrun['code']);
                //    unset($dataToRetrun['latitude']);
                   // unset($dataToRetrun['longitude']);
                }

                return $dataToRetrun;
            });
    }



    protected function getVillages($data, $isApi = false)
    {
        return Village::search($data['searchQuery'])
            ->query(function ($query) {
                $query->select('id', 'name', 'cell_id', 'code', 'geojson', 'latitude', 'longitude')
                    ->with(['cell:id,name,sector_id', 'cell.sector:id,name,district_id', 'cell.sector.district:id,name,province_id', 'cell.sector.district.province:id,name_en,name_local'])
                    ->limit(10);
            })->get()
            ->map(function ($village) use ($data, $isApi) {
                $dataToRetrun = [
                    'id' => $village->id,
                    'code' => $village->code,
                    'name' => $this->formatLang($village->name, $data['lang'], LocationTypeEnums::VILLAGE->value),
                    'address' => $village->cell->sector->district->province->name_en . ', ' . $village->cell->sector->district->name . ', ' . $village->cell->sector->name . ', ' . $village->cell->name,
                    'geojson' => $village->geojson,
                    'latitude' => $village->latitude,
                    'longitude' => $village->longitude
                ];

                if ($isApi) {
                    unset($dataToRetrun['geojson']);
               //     unset($dataToRetrun['id']);
                    unset($dataToRetrun['code']);
                 //   unset($dataToRetrun['latitude']);
                  //  unset($dataToRetrun['longitude']);
                }

                return $dataToRetrun;
            });
    }

    protected function getHealthFacilities($data, $isApi = false)
    {
        return HealthFac::search($data['searchQuery'])
            ->query(function ($query) {
                $query->select('id', 'name', 'type', 'village_id', 'latitude', 'longitude', 'code')
                    ->with(['village:id,name,cell_id', 'village.cell:id,name,sector_id', 'village.cell.sector:id,name,district_id', 'village.cell.sector.district:id,name,province_id', 'village.cell.sector.district.province:id,name_en,name_local'])
                    ->limit(10);
            })->get()
            ->map(function ($healthFacility) use ($data, $isApi) {
                $dataToRetrun = [
                    'id' => $healthFacility->id,
                    'code' => $healthFacility->code,
                    'name' => $healthFacility->name . ' - ' . $healthFacility->type,
                    'latitude' => $healthFacility->latitude,
                    'longitude' => $healthFacility->longitude,
                    'address' => $healthFacility->village->cell->sector->district->province->name_local . ', ' . $healthFacility->village->cell->sector->district->name . ', ' . $healthFacility->village->cell->sector->name . ', ' . $healthFacility->village->cell->name,
                ];

                if ($isApi) {
                   // unset($dataToRetrun['id']);
                    unset($dataToRetrun['code']);
                 //   unset($dataToRetrun['latitude']);
                   // unset($dataToRetrun['longitude']);
                }

                return $dataToRetrun;
            });
    }

    public function getNavigation(NavigationData $data){

        Log::info('Navigation request data: ' . json_encode($data));

        try {
            // Initialize OSRM service
            $osrmService = app(OsrmService::class);

            // Get route from OSRM
            $osrmResponse = $osrmService->getRoute($data);

            // Check if OSRM returned a valid response
            if (!isset($osrmResponse['code']) || $osrmResponse['code'] !== 'Ok') {
                Log::error('OSRM returned error response', ['response' => $osrmResponse]);
                throw new \Exception('Failed to get route from OSRM: ' . ($osrmResponse['message'] ?? 'Unknown error'));
            }

            // Format the response for our frontend
            $formattedResponse = $this->formatNavigationResponse($osrmResponse, $data);

            Log::info('Navigation response formatted successfully');

            return $formattedResponse;

        } catch (\Exception $e) {
            Log::error('Navigation request failed: ' . $e->getMessage(), [
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Format OSRM response for frontend consumption
     *
     * @param array $osrmResponse
     * @param NavigationData $navigationData
     * @return array
     */
    protected function formatNavigationResponse(array $osrmResponse, NavigationData $navigationData): array
    {
        $routes = [];

        if (isset($osrmResponse['routes']) && !empty($osrmResponse['routes'])) {
            foreach ($osrmResponse['routes'] as $index => $route) {
                $formattedRoute = [
                    'index' => $index,
                    'distance' => $route['distance'] ?? 0, // meters
                    'duration' => $route['duration'] ?? 0, // seconds
                    'geometry' => $route['geometry'] ?? null,
                    'legs' => [],
                    'summary' => [
                        'distance_text' => $this->formatDistance($route['distance'] ?? 0),
                        'duration_text' => $this->formatDuration($route['duration'] ?? 0),
                        'mode' => $navigationData->mode,
                        'language' => $navigationData->language
                    ]
                ];

                // Process route legs (segments between waypoints)
                if (isset($route['legs']) && !empty($route['legs'])) {
                    foreach ($route['legs'] as $legIndex => $leg) {
                        $formattedLeg = [
                            'index' => $legIndex,
                            'distance' => $leg['distance'] ?? 0,
                            'duration' => $leg['duration'] ?? 0,
                            'steps' => []
                        ];

                        // Process turn-by-turn steps
                        if (isset($leg['steps']) && !empty($leg['steps'])) {
                            foreach ($leg['steps'] as $stepIndex => $step) {
                                $formattedStep = [
                                    'index' => $stepIndex,
                                    'distance' => $step['distance'] ?? 0,
                                    'duration' => $step['duration'] ?? 0,
                                    'geometry' => $step['geometry'] ?? null,
                                    'maneuver' => [
                                        'type' => $step['maneuver']['type'] ?? 'unknown',
                                        'modifier' => $step['maneuver']['modifier'] ?? null,
                                        'bearing_before' => $step['maneuver']['bearing_before'] ?? null,
                                        'bearing_after' => $step['maneuver']['bearing_after'] ?? null,
                                        'location' => $step['maneuver']['location'] ?? null,
                                        'instruction' => $this->generateInstruction($step, $navigationData->language)
                                    ],
                                    'name' => $step['name'] ?? '',
                                    'ref' => $step['ref'] ?? null,
                                    'mode' => $step['mode'] ?? $navigationData->mode
                                ];

                                $formattedLeg['steps'][] = $formattedStep;
                            }
                        }

                        $formattedRoute['legs'][] = $formattedLeg;
                    }
                }

                $routes[] = $formattedRoute;
            }
        }

        return [
            'code' => 'Ok',
            'routes' => $routes,
            'waypoints' => $osrmResponse['waypoints'] ?? [],
            'metadata' => [
                'request' => [
                    'from' => $navigationData->from,
                    'to' => $navigationData->to,
                    'through' => $navigationData->throught ?? [],
                    'mode' => $navigationData->mode,
                    'language' => $navigationData->language
                ],
                'timestamp' => now()->toISOString(),
                'service' => 'OSRM'
            ]
        ];
    }

    /**
     * Format distance in meters to human readable format
     *
     * @param float $meters
     * @return string
     */
    protected function formatDistance(float $meters): string
    {
        if ($meters >= 1000) {
            return round($meters / 1000, 1) . ' km';
        }
        return round($meters) . ' m';
    }

    /**
     * Format duration in seconds to human readable format
     *
     * @param float $seconds
     * @return string
     */
    protected function formatDuration(float $seconds): string
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'min';
        }

        if ($minutes > 0) {
            return $minutes . 'min';
        }

        return round($seconds) . 's';
    }

    /**
     * Generate turn-by-turn instruction based on maneuver
     *
     * @param array $step
     * @param string $language
     * @return string
     */
    protected function generateInstruction(array $step, string $language): string
    {
        $maneuver = $step['maneuver'] ?? [];
        $type = $maneuver['type'] ?? 'unknown';
        $modifier = $maneuver['modifier'] ?? null;
        $name = $step['name'] ?? '';
        $distance = $step['distance'] ?? 0;

        // Basic instruction templates by language
        $instructions = [
            'en' => [
                'depart' => 'Head ' . ($modifier ?? 'straight') . ($name ? ' on ' . $name : ''),
                'turn' => 'Turn ' . ($modifier ?? 'straight') . ($name ? ' onto ' . $name : ''),
                'merge' => 'Merge ' . ($modifier ?? 'straight') . ($name ? ' onto ' . $name : ''),
                'ramp' => 'Take the ramp ' . ($modifier ?? 'straight') . ($name ? ' onto ' . $name : ''),
                'roundabout' => 'Enter the roundabout' . ($name ? ' and exit onto ' . $name : ''),
                'arrive' => 'Arrive at your destination',
                'continue' => 'Continue ' . ($modifier ?? 'straight') . ($name ? ' on ' . $name : ''),
                'unknown' => 'Continue for ' . $this->formatDistance($distance)
            ],
            'fr' => [
                'depart' => 'Dirigez-vous ' . ($modifier ?? 'tout droit') . ($name ? ' sur ' . $name : ''),
                'turn' => 'Tournez ' . ($modifier ?? 'tout droit') . ($name ? ' sur ' . $name : ''),
                'merge' => 'Fusionnez ' . ($modifier ?? 'tout droit') . ($name ? ' sur ' . $name : ''),
                'ramp' => 'Prenez la bretelle ' . ($modifier ?? 'tout droit') . ($name ? ' sur ' . $name : ''),
                'roundabout' => 'Entrez dans le rond-point' . ($name ? ' et sortez sur ' . $name : ''),
                'arrive' => 'Arrivée à destination',
                'continue' => 'Continuez ' . ($modifier ?? 'tout droit') . ($name ? ' sur ' . $name : ''),
                'unknown' => 'Continuez sur ' . $this->formatDistance($distance)
            ],
            'rw' => [
                'depart' => 'Tangira ' . ($modifier ?? 'mu buryo bwuzuye') . ($name ? ' kuri ' . $name : ''),
                'turn' => 'Hinduka ' . ($modifier ?? 'mu buryo bwuzuye') . ($name ? ' kuri ' . $name : ''),
                'merge' => 'Huza ' . ($modifier ?? 'mu buryo bwuzuye') . ($name ? ' kuri ' . $name : ''),
                'ramp' => 'Fata inzira ' . ($modifier ?? 'mu buryo bwuzuye') . ($name ? ' kuri ' . $name : ''),
                'roundabout' => 'Injira mu kibuga' . ($name ? ' usohoke kuri ' . $name : ''),
                'arrive' => 'Ugeze aho ugiye',
                'continue' => 'Komeza ' . ($modifier ?? 'mu buryo bwuzuye') . ($name ? ' kuri ' . $name : ''),
                'unknown' => 'Komeza kuri ' . $this->formatDistance($distance)
            ]
        ];

        $langInstructions = $instructions[$language] ?? $instructions['en'];
        return $langInstructions[$type] ?? $langInstructions['unknown'];
    }

    protected function formatLang(string $name, string $lang, string $code)
    {

        switch ($lang) {
            case 'rw':
                if ($code == LocationTypeEnums::DISTRICT->value) {
                    return 'Akarere ka ' . $name;
                }
                if ($code == LocationTypeEnums::SECTOR->value) {
                    return 'Umurenge wa ' . $name;
                }
                if ($code == LocationTypeEnums::CELL->value) {
                    return 'Akagari ka ' . $name;
                }
                if ($code == LocationTypeEnums::VILLAGE->value) {
                    return 'Umudugudu wa ' . $name;
                }
                break;
            case 'en':
                if ($code == LocationTypeEnums::DISTRICT->value) {
                    return $name . ' District';
                }
                if ($code == LocationTypeEnums::SECTOR->value) {
                    return $name . ' Sector';
                }
                if ($code == LocationTypeEnums::CELL->value) {
                    return $name . ' Cell';
                }
                if ($code == LocationTypeEnums::VILLAGE->value) {
                    return $name . ' Village';
                }
                break;
            case 'fr':
                if ($code == LocationTypeEnums::DISTRICT->value) {
                    return 'District de ' . $name;
                }
                if ($code == LocationTypeEnums::SECTOR->value) {
                    return 'Secteur de ' . $name;
                }
                if ($code == LocationTypeEnums::CELL->value) {
                    return 'Cellule de ' . $name;
                }
                if ($code == LocationTypeEnums::VILLAGE->value) {
                    return 'Village de ' . $name;
                }
        }
    }
}
