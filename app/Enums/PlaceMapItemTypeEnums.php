<?php

namespace App\Enums;

enum PlaceMapItemTypeEnums: string
{

    case PROVINCE = 'province';
    case DISTRICT = 'district';
    case SECTOR = 'sector';
    case CELL = 'cell';
    case VILLAGE = 'village';
    case MOVINGITEM = 'movingItem';
    case PLACE = 'place';
    case HOTEL = 'hotel';
    case WATERBODIE = 'waterbodie';
    case RIVERS = 'rivers';
    case ROAD = 'road';
    case BUILDING = 'building';
    case TOWN = 'town';
    case SCHOOL = 'school';
    case MARKET = 'market';
    case ITINERARY = 'itinerary';
}
